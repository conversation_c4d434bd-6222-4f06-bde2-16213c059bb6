// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contraception_method_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ContraceptionMethod _$ContraceptionMethodFromJson(Map<String, dynamic> json) =>
    ContraceptionMethod(
      id: json['id'] as String,
      userId: json['userId'] as String,
      methodType: json['methodType'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate:
          json['endDate'] == null
              ? null
              : DateTime.parse(json['endDate'] as String),
      isActive: json['isActive'] as bool,
      notes: json['notes'] as String?,
      prescribedBy: json['prescribedBy'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt:
          json['updatedAt'] == null
              ? null
              : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$ContraceptionMethodToJson(
  ContraceptionMethod instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'methodType': instance.methodType,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate?.toIso8601String(),
  'isActive': instance.isActive,
  'notes': instance.notes,
  'prescribedBy': instance.prescribedBy,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};
