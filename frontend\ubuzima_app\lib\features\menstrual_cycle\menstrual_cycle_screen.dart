import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/family_planning_service.dart';
import '../../core/models/menstrual_cycle_model.dart';
import '../../widgets/voice_button.dart';
import '../../widgets/ai_assistant_fab.dart';
import 'cycle_calendar_widget.dart';
import 'cycle_tracking_form.dart';
import 'fertility_predictions_widget.dart';

class MenstrualCycleScreen extends StatefulWidget {
  const MenstrualCycleScreen({super.key});

  @override
  State<MenstrualCycleScreen> createState() => _MenstrualCycleScreenState();
}

class _MenstrualCycleScreenState extends State<MenstrualCycleScreen>
    with TickerProviderStateMixin {
  final FamilyPlanningService _familyPlanningService = FamilyPlanningService();
  late TabController _tabController;

  List<MenstrualCycle> _cycles = [];
  Map<String, dynamic> _currentCycle = {};
  Map<String, dynamic> _predictions = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadCycleData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCycleData() async {
    setState(() => _isLoading = true);

    try {
      final results = await Future.wait([
        _familyPlanningService.getMenstrualCycles(limit: 12),
        _familyPlanningService.getCurrentCycle(),
        _familyPlanningService.getFertilityPredictions(),
      ]);

      setState(() {
        _cycles = results[0] as List<MenstrualCycle>;
        _currentCycle = results[1] as Map<String, dynamic>;
        _predictions = results[2] as Map<String, dynamic>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Habaye ikosa mu gufata amakuru: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: NestedScrollView(
        headerSliverBuilder:
            (context, innerBoxIsScrolled) => [
              _buildAppBar(isTablet, innerBoxIsScrolled),
            ],
        body:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(isTablet),
                    _buildCalendarTab(isTablet),
                    _buildPredictionsTab(isTablet),
                  ],
                ),
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: 'add_cycle',
            onPressed: () => _showAddCycleDialog(),
            backgroundColor: AppTheme.accentColor,
            child: const Icon(Icons.add_rounded, color: Colors.white),
          ),
          const SizedBox(height: 16),
          const AiAssistantFab(),
        ],
      ),
    );
  }

  Widget _buildAppBar(bool isTablet, bool innerBoxIsScrolled) {
    return SliverAppBar(
      expandedHeight: isTablet ? 200 : 160,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.accentColor,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Gukurikirana Imihango',
          style: AppTheme.headingMedium.copyWith(
            color: Colors.white,
            fontSize: isTablet ? 24 : 20,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.accentColor,
                AppTheme.accentColor.withOpacity(0.8),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: _buildCurrentCycleInfo(isTablet),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh_rounded, color: Colors.white),
          onPressed: _loadCycleData,
        ),
        VoiceButton(
          heroTag: 'menstrual_cycle_voice',
          prompt: 'Gukurikirana imihango',
          onResult: (result) {
            // Handle voice result
            debugPrint('Voice result: $result');
          },
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        tabs: const [
          Tab(icon: Icon(Icons.dashboard_rounded), text: 'Ibipimo'),
          Tab(icon: Icon(Icons.calendar_month_rounded), text: 'Kalendari'),
          Tab(icon: Icon(Icons.insights_rounded), text: 'Ibiteganijwe'),
        ],
      ),
    );
  }

  Widget _buildCurrentCycleInfo(bool isTablet) {
    if (_currentCycle.isEmpty) {
      return Container(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: Center(
          child: Text(
            'Nta makuru y\'imihango aboneka',
            style: AppTheme.bodyLarge.copyWith(
              color: Colors.white70,
              fontSize: isTablet ? 16 : 14,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildCycleInfoCard(
                'Umunsi w\'Imihango',
                '${_currentCycle['dayOfCycle'] ?? 0}',
                Icons.calendar_today_rounded,
                isTablet,
              ),
              _buildCycleInfoCard(
                'Iminsi Isigaye',
                '${_currentCycle['daysUntilNext'] ?? 0}',
                Icons.schedule_rounded,
                isTablet,
              ),
              _buildCycleInfoCard(
                'Icyiciro',
                _currentCycle['phase'] ?? 'Ntabwo hahari',
                Icons.circle_rounded,
                isTablet,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCycleInfoCard(
    String title,
    String value,
    IconData icon,
    bool isTablet,
  ) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: isTablet ? 24 : 20),
          SizedBox(height: AppTheme.spacing4),
          Text(
            value,
            style: AppTheme.headingSmall.copyWith(
              color: Colors.white,
              fontSize: isTablet ? 16 : 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTheme.bodySmall.copyWith(
              color: Colors.white70,
              fontSize: isTablet ? 12 : 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(bool isTablet) {
    return RefreshIndicator(
      onRefresh: _loadCycleData,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCycleStats(isTablet),
            SizedBox(height: AppTheme.spacing24),
            _buildRecentCycles(isTablet),
            SizedBox(height: AppTheme.spacing24),
            _buildSymptomTracking(isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarTab(bool isTablet) {
    return RefreshIndicator(
      onRefresh: _loadCycleData,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: Column(
          children: [
            CycleCalendarWidget(
              cycles: _cycles,
              currentCycle: _currentCycle,
              onDateSelected: (date) {
                // Handle date selection for cycle tracking
                _showCycleDetailsDialog(date);
              },
            ),
            SizedBox(height: AppTheme.spacing24),
            _buildCalendarLegend(isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildPredictionsTab(bool isTablet) {
    return RefreshIndicator(
      onRefresh: _loadCycleData,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: Column(
          children: [
            FertilityPredictionsWidget(
              predictions: _predictions,
              isTablet: isTablet,
            ),
            SizedBox(height: AppTheme.spacing24),
            _buildFertilityTips(isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildCycleStats(bool isTablet) {
    final averageLength =
        _cycles.isNotEmpty
            ? _cycles.map((c) => c.cycleLength ?? 28).reduce((a, b) => a + b) /
                _cycles.length
            : 28.0;

    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ibipimo by\'Imihango',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 20 : 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Uburebure Busanzwe',
                  '${averageLength.round()} iminsi',
                  Icons.timeline_rounded,
                  AppTheme.accentColor,
                  isTablet,
                ),
              ),
              SizedBox(width: AppTheme.spacing16),
              Expanded(
                child: _buildStatItem(
                  'Imihango Yakurikiranywe',
                  '${_cycles.length}',
                  Icons.history_rounded,
                  AppTheme.primaryColor,
                  isTablet,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms);
  }

  Widget _buildStatItem(
    String title,
    String value,
    IconData icon,
    Color color,
    bool isTablet,
  ) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: isTablet ? 28 : 24),
          SizedBox(height: AppTheme.spacing8),
          Text(
            value,
            style: AppTheme.headingMedium.copyWith(
              color: color,
              fontSize: isTablet ? 18 : 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing4),
          Text(
            title,
            style: AppTheme.bodySmall.copyWith(
              color: Colors.black87,
              fontSize: isTablet ? 12 : 11,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentCycles(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Imihango ya Vuba',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 20 : 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          if (_cycles.isEmpty)
            Center(
              child: Text(
                'Nta mihango yakurikiranywe',
                style: AppTheme.bodyMedium.copyWith(color: Colors.black54),
              ),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _cycles.take(5).length,
              separatorBuilder:
                  (context, index) => Divider(color: Colors.grey.shade200),
              itemBuilder: (context, index) {
                final cycle = _cycles[index];
                return _buildCycleListItem(cycle, isTablet);
              },
            ),
        ],
      ),
    ).animate().fadeIn(duration: 800.ms, delay: 200.ms);
  }

  Widget _buildCycleListItem(MenstrualCycle cycle, bool isTablet) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Container(
        padding: EdgeInsets.all(AppTheme.spacing8),
        decoration: BoxDecoration(
          color: AppTheme.accentColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        ),
        child: Icon(
          Icons.calendar_month_rounded,
          color: AppTheme.accentColor,
          size: isTablet ? 24 : 20,
        ),
      ),
      title: Text(
        'Imihango ${cycle.startDate != null ? _formatDate(cycle.startDate!) : ''}',
        style: AppTheme.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: isTablet ? 14 : 13,
        ),
      ),
      subtitle: Text(
        'Iminsi ${cycle.cycleLength ?? 28} • ${cycle.flowIntensity != null ? _getFlowIntensityText(cycle.flowIntensity!) : 'Ntabwo hahari'}',
        style: AppTheme.bodySmall.copyWith(
          color: Colors.black54,
          fontSize: isTablet ? 12 : 11,
        ),
      ),
      trailing: Icon(
        Icons.chevron_right_rounded,
        color: Colors.black54,
        size: isTablet ? 24 : 20,
      ),
      onTap: () => _showCycleDetailsDialog(cycle.startDate),
    );
  }

  Widget _buildSymptomTracking(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Gukurikirana Ibimenyetso',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 20 : 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          Text(
            'Andika ibimenyetso byawe bya buri munsi kugira ngo ubone uburyo imihango yawe igenda.',
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.black87,
              fontSize: isTablet ? 14 : 13,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          ElevatedButton.icon(
            onPressed: () => _showSymptomTrackingDialog(),
            icon: const Icon(Icons.add_rounded),
            label: const Text('Ongeraho Ibimenyetso'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: AppTheme.spacing20,
                vertical: AppTheme.spacing12,
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 1000.ms, delay: 400.ms);
  }

  Widget _buildCalendarLegend(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ibisobanuro',
            style: AppTheme.headingSmall.copyWith(
              fontSize: isTablet ? 16 : 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildLegendItem('Imihango', Colors.red, isTablet),
              _buildLegendItem('Gusanga', AppTheme.accentColor, isTablet),
              _buildLegendItem('Gusohoka', Colors.green, isTablet),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color, bool isTablet) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: isTablet ? 16 : 14,
          height: isTablet ? 16 : 14,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        SizedBox(width: AppTheme.spacing8),
        Text(
          label,
          style: AppTheme.bodySmall.copyWith(fontSize: isTablet ? 12 : 11),
        ),
      ],
    );
  }

  Widget _buildFertilityTips(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Inama z\'Uburemere',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 20 : 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          _buildTipItem(
            'Kurya neza ni ngombwa mu gihe cy\'uburemere',
            Icons.restaurant_rounded,
            isTablet,
          ),
          _buildTipItem(
            'Gukora siporo byoroshya uburemere',
            Icons.fitness_center_rounded,
            isTablet,
          ),
          _buildTipItem(
            'Kuraguza ubwoba bugabanya amahirwe yo gusanga',
            Icons.psychology_rounded,
            isTablet,
          ),
        ],
      ),
    );
  }

  Widget _buildTipItem(String tip, IconData icon, bool isTablet) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppTheme.spacing8),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.successColor, size: isTablet ? 20 : 18),
          SizedBox(width: AppTheme.spacing12),
          Expanded(
            child: Text(
              tip,
              style: AppTheme.bodyMedium.copyWith(fontSize: isTablet ? 14 : 13),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddCycleDialog() {
    showDialog(
      context: context,
      builder:
          (context) => CycleTrackingForm(
            onCycleSaved: (cycle) {
              _loadCycleData();
              Navigator.of(context).pop();
            },
          ),
    );
  }

  void _showCycleDetailsDialog(DateTime? date) {
    if (date == null) return;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Imihango ${_formatDate(date)}'),
            content: Text('Amakuru y\'imihango y\'uyu munsi azongera hano.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Siga'),
              ),
            ],
          ),
    );
  }

  void _showSymptomTrackingDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Ongeraho Ibimenyetso'),
            content: const Text('Aha hazongera form yo kwandika ibimenyetso.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Siga'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Save symptoms logic here
                },
                child: const Text('Bika'),
              ),
            ],
          ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getFlowIntensityText(int intensity) {
    switch (intensity) {
      case 1:
        return 'Gake';
      case 2:
        return 'Busanzwe';
      case 3:
        return 'Byinshi';
      case 4:
        return 'Byinshi cyane';
      default:
        return 'Ntabwo hahari';
    }
  }
}
