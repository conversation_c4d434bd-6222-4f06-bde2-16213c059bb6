import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import '../../core/theme/app_theme.dart';
import '../../core/models/appointment_model.dart';
import '../../core/services/appointment_service.dart';
import '../../widgets/voice_button.dart';
import 'appointment_booking_screen.dart';

class AppointmentManagementScreen extends StatefulWidget {
  const AppointmentManagementScreen({super.key});

  @override
  State<AppointmentManagementScreen> createState() =>
      _AppointmentManagementScreenState();
}

class _AppointmentManagementScreenState
    extends State<AppointmentManagementScreen> {
  final AppointmentService _appointmentService = AppointmentService();
  List<Appointment> _appointments = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';

  final List<Map<String, dynamic>> _filterOptions = [
    {'key': 'all', 'label': 'Byose', 'icon': Icons.list_rounded},
    {'key': 'scheduled', 'label': 'Biteguwe', 'icon': Icons.schedule_rounded},
    {
      'key': 'confirmed',
      'label': 'Byemejwe',
      'icon': Icons.check_circle_rounded,
    },
    {'key': 'completed', 'label': 'Byarangiye', 'icon': Icons.done_all_rounded},
    {
      'key': 'cancelled',
      'label': 'Byahagaritswe',
      'icon': Icons.cancel_rounded,
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  Future<void> _loadAppointments() async {
    setState(() => _isLoading = true);

    try {
      final appointments = await _appointmentService.getAppointments(
        userId: '3', // Current user ID - should come from auth service
        status: _selectedFilter == 'all' ? null : _selectedFilter.toUpperCase(),
      );

      setState(() {
        _appointments = appointments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Habaye ikosa mu gushaka gahunda');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: AppTheme.errorColor),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          _buildAppBar(isTablet),
          _buildFilterTabs(isTablet),
          _buildContent(isTablet),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToBooking(),
        icon: const Icon(Icons.add_rounded),
        label: const Text('Gahunda nshya'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildAppBar(bool isTablet) {
    return SliverAppBar(
      expandedHeight: isTablet ? 120 : 100,
      floating: true,
      pinned: true,
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Gahunda zanjye',
          style: TextStyle(
            fontSize: isTablet ? 24 : 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      actions: [
        IconButton(
          onPressed: _loadAppointments,
          icon: const Icon(Icons.refresh_rounded),
          tooltip: 'Kuvugurura',
        ),
      ],
    );
  }

  Widget _buildFilterTabs(bool isTablet) {
    return SliverToBoxAdapter(
      child: Container(
        height: isTablet ? 80 : 70,
        padding: EdgeInsets.symmetric(
          horizontal: isTablet ? AppTheme.spacing24 : AppTheme.spacing16,
          vertical: AppTheme.spacing12,
        ),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: _filterOptions.length,
          itemBuilder: (context, index) {
            final option = _filterOptions[index];
            final isSelected = _selectedFilter == option['key'];

            return Container(
              margin: EdgeInsets.only(right: AppTheme.spacing12),
              child: FilterChip(
                selected: isSelected,
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      option['icon'] as IconData,
                      size: isTablet ? 20 : 16,
                      color: isSelected ? Colors.white : AppTheme.primaryColor,
                    ),
                    SizedBox(width: AppTheme.spacing8),
                    Text(
                      option['label'] as String,
                      style: TextStyle(
                        fontSize: isTablet ? 14 : 12,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                onSelected: (selected) {
                  setState(() => _selectedFilter = option['key'] as String);
                  _loadAppointments();
                },
                backgroundColor: Colors.white,
                selectedColor: AppTheme.primaryColor,
                checkmarkColor: Colors.white,
                labelStyle: TextStyle(
                  color: isSelected ? Colors.white : AppTheme.primaryColor,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildContent(bool isTablet) {
    if (_isLoading) {
      return const SliverFillRemaining(
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (_appointments.isEmpty) {
      return SliverFillRemaining(child: _buildEmptyState(isTablet));
    }

    return SliverPadding(
      padding: EdgeInsets.all(
        isTablet ? AppTheme.spacing24 : AppTheme.spacing16,
      ),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate((context, index) {
          final appointment = _appointments[index];
          return _buildAppointmentCard(appointment, isTablet)
              .animate(delay: (index * 100).ms)
              .fadeIn(duration: 600.ms)
              .slideX(begin: -0.3, duration: 600.ms);
        }, childCount: _appointments.length),
      ),
    );
  }

  Widget _buildEmptyState(bool isTablet) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy_rounded,
            size: isTablet ? 80 : 64,
            color: AppTheme.textSecondary,
          ),
          SizedBox(height: AppTheme.spacing16),
          Text(
            'Nta gahunda uhari',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: AppTheme.textSecondary),
          ),
          SizedBox(height: AppTheme.spacing8),
          Text(
            'Kanda hano hasi kugira ngo utegure gahunda nshya',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppTheme.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentCard(Appointment appointment, bool isTablet) {
    final dateFormat = DateFormat('EEEE, MMMM d, y', 'rw');
    final timeFormat = DateFormat('HH:mm');

    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacing16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        boxShadow: AppTheme.softShadow,
        border: Border.all(
          color: _getStatusColor(appointment.status).withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with status
          Container(
            padding: EdgeInsets.all(
              isTablet ? AppTheme.spacing20 : AppTheme.spacing16,
            ),
            decoration: BoxDecoration(
              color: _getStatusColor(appointment.status).withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppTheme.radiusLarge),
                topRight: Radius.circular(AppTheme.radiusLarge),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppTheme.spacing12,
                    vertical: AppTheme.spacing6,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(appointment.status),
                    borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  ),
                  child: Text(
                    _getStatusText(appointment.status),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isTablet ? 12 : 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  _getAppointmentTypeText(appointment.appointmentType),
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: _getStatusColor(appointment.status),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: EdgeInsets.all(
              isTablet ? AppTheme.spacing20 : AppTheme.spacing16,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Date and time
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today_rounded,
                      size: isTablet ? 20 : 16,
                      color: AppTheme.primaryColor,
                    ),
                    SizedBox(width: AppTheme.spacing8),
                    Expanded(
                      child: Text(
                        dateFormat.format(appointment.appointmentDate),
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppTheme.spacing8),
                Row(
                  children: [
                    Icon(
                      Icons.access_time_rounded,
                      size: isTablet ? 20 : 16,
                      color: AppTheme.primaryColor,
                    ),
                    SizedBox(width: AppTheme.spacing8),
                    Text(
                      timeFormat.format(appointment.appointmentDate),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),

                if (appointment.facility != null) ...[
                  SizedBox(height: AppTheme.spacing12),
                  Row(
                    children: [
                      Icon(
                        Icons.local_hospital_rounded,
                        size: isTablet ? 20 : 16,
                        color: AppTheme.primaryColor,
                      ),
                      SizedBox(width: AppTheme.spacing8),
                      Expanded(
                        child: Text(
                          appointment.facility!.name,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                ],

                if (appointment.reason != null &&
                    appointment.reason!.isNotEmpty) ...[
                  SizedBox(height: AppTheme.spacing12),
                  Text(
                    'Impamvu: ${appointment.reason}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],

                // Action buttons
                if (appointment.status == AppointmentStatus.scheduled ||
                    appointment.status == AppointmentStatus.confirmed) ...[
                  SizedBox(height: AppTheme.spacing16),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _rescheduleAppointment(appointment),
                          icon: const Icon(Icons.edit_calendar_rounded),
                          label: const Text('Hindura'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppTheme.primaryColor,
                            side: BorderSide(color: AppTheme.primaryColor),
                          ),
                        ),
                      ),
                      SizedBox(width: AppTheme.spacing12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _cancelAppointment(appointment),
                          icon: const Icon(Icons.cancel_rounded),
                          label: const Text('Hagarika'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.errorColor,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.scheduled:
        return AppTheme.warningColor;
      case AppointmentStatus.confirmed:
        return AppTheme.infoColor;
      case AppointmentStatus.completed:
        return AppTheme.successColor;
      case AppointmentStatus.cancelled:
        return AppTheme.errorColor;
      case AppointmentStatus.noShow:
        return Colors.grey;
      case AppointmentStatus.rescheduled:
        return AppTheme.primaryColor;
      case AppointmentStatus.checkedIn:
        return AppTheme.infoColor;
      case AppointmentStatus.inProgress:
        return AppTheme.primaryColor;
    }
  }

  String _getStatusText(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.scheduled:
        return 'Biteguwe';
      case AppointmentStatus.confirmed:
        return 'Byemejwe';
      case AppointmentStatus.completed:
        return 'Byarangiye';
      case AppointmentStatus.cancelled:
        return 'Byahagaritswe';
      case AppointmentStatus.noShow:
        return 'Ntabwo yaje';
      case AppointmentStatus.rescheduled:
        return 'Byahinduwe';
      case AppointmentStatus.checkedIn:
        return 'Yinjiye';
      case AppointmentStatus.inProgress:
        return 'Biragenda';
    }
  }

  String _getAppointmentTypeText(AppointmentType type) {
    switch (type) {
      case AppointmentType.familyPlanning:
        return 'Ubwiyunge bw\'umuryango';
      case AppointmentType.prenatalCare:
        return 'Kwita ku bana bato';
      case AppointmentType.postnatalCare:
        return 'Nyuma yo kubyara';
      case AppointmentType.contraceptionConsultation:
        return 'Inama ku kurinda inda';
      case AppointmentType.stiScreening:
        return 'Isuzuma ry\'indwara zandurira';
      case AppointmentType.generalConsultation:
        return 'Inama rusange';
      case AppointmentType.followUp:
        return 'Gukurikirana';
      case AppointmentType.emergency:
        return 'Byihutirwa';
      case AppointmentType.vaccination:
        return 'Gukingira';
      case AppointmentType.healthEducation:
        return 'Kwigisha ku buzima';
      case AppointmentType.counseling:
        return 'Ubujyanama';
      case AppointmentType.laboratoryTests:
        return 'Ibizamini';
    }
  }

  void _navigateToBooking() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => const AppointmentBookingScreen(),
          ),
        )
        .then((_) => _loadAppointments());
  }

  Future<void> _cancelAppointment(Appointment appointment) async {
    final reason = await _showCancelDialog();
    if (reason == null) return;

    try {
      final success = await _appointmentService.cancelAppointment(
        appointment.id,
        reason: reason.isNotEmpty ? reason : null,
      );

      if (success) {
        _showSuccessSnackBar('Gahunda yahagaritswe neza');
        _loadAppointments();
      } else {
        _showErrorSnackBar('Habaye ikosa mu guhagarika gahunda');
      }
    } catch (e) {
      _showErrorSnackBar('Habaye ikosa mu guhagarika gahunda');
    }
  }

  Future<void> _rescheduleAppointment(Appointment appointment) async {
    final newDate = await _showRescheduleDialog();
    if (newDate == null) return;

    try {
      final updatedAppointment = await _appointmentService
          .rescheduleAppointment(
            appointmentId: appointment.id,
            newScheduledDate: newDate,
            reason: 'Gahunda yahinduwe na mukiriya',
          );

      if (updatedAppointment != null) {
        _showSuccessSnackBar('Gahunda yahinduwe neza');
        _loadAppointments();
      } else {
        _showErrorSnackBar('Habaye ikosa mu guhindura gahunda');
      }
    } catch (e) {
      _showErrorSnackBar('Habaye ikosa mu guhindura gahunda');
    }
  }

  Future<String?> _showCancelDialog() async {
    final reasonController = TextEditingController();

    return showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Hagarika gahunda'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Urashaka rwose guhagarika iyi gahunda?'),
                const SizedBox(height: 16),
                TextField(
                  controller: reasonController,
                  decoration: const InputDecoration(
                    labelText: 'Impamvu (ntabwo ari ngombwa)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Bireke'),
              ),
              ElevatedButton(
                onPressed:
                    () => Navigator.of(context).pop(reasonController.text),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Hagarika'),
              ),
            ],
          ),
    );
  }

  Future<DateTime?> _showRescheduleDialog() async {
    DateTime? selectedDate;
    TimeOfDay? selectedTime;

    return showDialog<DateTime>(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('Hindura gahunda'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text('Hitamo itariki n\'igihe gishya:'),
                      const SizedBox(height: 16),
                      ListTile(
                        leading: const Icon(Icons.calendar_today),
                        title: Text(
                          selectedDate != null
                              ? DateFormat(
                                'EEEE, MMMM d, y',
                                'rw',
                              ).format(selectedDate!)
                              : 'Hitamo itariki',
                        ),
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: DateTime.now().add(
                              const Duration(days: 1),
                            ),
                            firstDate: DateTime.now(),
                            lastDate: DateTime.now().add(
                              const Duration(days: 365),
                            ),
                          );
                          if (date != null) {
                            setState(() => selectedDate = date);
                          }
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.access_time),
                        title: Text(
                          selectedTime != null
                              ? selectedTime!.format(context)
                              : 'Hitamo igihe',
                        ),
                        onTap: () async {
                          final time = await showTimePicker(
                            context: context,
                            initialTime: const TimeOfDay(hour: 9, minute: 0),
                          );
                          if (time != null) {
                            setState(() => selectedTime = time);
                          }
                        },
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Bireke'),
                    ),
                    ElevatedButton(
                      onPressed:
                          selectedDate != null && selectedTime != null
                              ? () {
                                final newDateTime = DateTime(
                                  selectedDate!.year,
                                  selectedDate!.month,
                                  selectedDate!.day,
                                  selectedTime!.hour,
                                  selectedTime!.minute,
                                );
                                Navigator.of(context).pop(newDateTime);
                              }
                              : null,
                      child: const Text('Hindura'),
                    ),
                  ],
                ),
          ),
    );
  }

  void _handleVoiceCommand(String command) {
    final lowerCommand = command.toLowerCase();
    if (lowerCommand.contains('gahunda') || lowerCommand.contains('book')) {
      _navigateToBooking();
    } else if (lowerCommand.contains('kuvugurura') ||
        lowerCommand.contains('refresh')) {
      _loadAppointments();
    }
  }
}
