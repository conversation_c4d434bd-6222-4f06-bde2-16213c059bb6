import 'package:json_annotation/json_annotation.dart';

part 'education_lesson_model.g.dart';

@JsonSerializable()
class EducationLesson {
  final String id;
  final String title;
  final String? description;
  final String content;
  final String category;
  final String level;
  final int orderIndex;
  final bool isPublished;
  final String? imageUrl;
  final String? videoUrl;
  final int? estimatedDuration;
  final DateTime createdAt;
  final DateTime? updatedAt;

  EducationLesson({
    required this.id,
    required this.title,
    this.description,
    required this.content,
    required this.category,
    required this.level,
    required this.orderIndex,
    required this.isPublished,
    this.imageUrl,
    this.videoUrl,
    this.estimatedDuration,
    required this.createdAt,
    this.updatedAt,
  });

  factory EducationLesson.fromJson(Map<String, dynamic> json) =>
      _$EducationLessonFromJson(json);

  Map<String, dynamic> toJson() => _$EducationLessonToJson(this);
}
