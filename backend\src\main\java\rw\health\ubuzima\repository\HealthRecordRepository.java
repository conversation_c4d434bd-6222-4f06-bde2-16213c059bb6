package rw.health.ubuzima.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.HealthRecord;


import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface HealthRecordRepository extends JpaRepository<HealthRecord, Long> {

    List<HealthRecord> findByUserIdOrderByRecordedAtDesc(Long userId);

    List<HealthRecord> findByUserIdAndRecordTypeOrderByRecordedAtDesc(Long userId, RecordType recordType);

    @Query("SELECT hr FROM HealthRecord hr WHERE hr.user.id = :userId AND hr.recordedAt BETWEEN :startDate AND :endDate ORDER BY hr.recordedAt DESC")
    List<HealthRecord> findByUserIdAndDateRange(@Param("userId") Long userId,
                                               @Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate);

    @Query("SELECT hr FROM HealthRecord hr WHERE hr.recordType = :recordType ORDER BY hr.recordedAt DESC")
    List<HealthRecord> findByRecordTypeOrderByRecordedAtDesc(@Param("recordType") RecordType recordType);

    @Query("SELECT hr FROM HealthRecord hr WHERE hr.recordedAt BETWEEN :startDate AND :endDate ORDER BY hr.recordedAt DESC")
    List<HealthRecord> findByDateRangeOrderByRecordedAtDesc(@Param("startDate") LocalDateTime startDate,
                                                           @Param("endDate") LocalDateTime endDate);
}
