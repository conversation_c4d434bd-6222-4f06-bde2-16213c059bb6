import 'package:json_annotation/json_annotation.dart';

part 'menstrual_cycle_model.g.dart';

@JsonSerializable()
class MenstrualCycle {
  final String id;
  final String userId;
  final DateTime startDate;
  final DateTime? endDate;
  final int? cycleLength;
  final int? flowIntensity;
  final List<String>? symptoms;
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;

  MenstrualCycle({
    required this.id,
    required this.userId,
    required this.startDate,
    this.endDate,
    this.cycleLength,
    this.flowIntensity,
    this.symptoms,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  factory MenstrualCycle.fromJson(Map<String, dynamic> json) =>
      _$MenstrualCycleFromJson(json);

  Map<String, dynamic> toJson() => _$MenstrualCycleToJson(this);
}
