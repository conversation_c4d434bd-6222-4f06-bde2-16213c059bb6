2025-07-16 17:11:44 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 15344 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-16 17:11:44 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 17:11:44 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-16 17:11:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 17:11:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 105 ms. Found 23 JPA repository interfaces.
2025-07-16 17:11:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8080 (http)
2025-07-16 17:11:48 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 17:11:48 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 17:11:48 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-16 17:11:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3709 ms
2025-07-16 17:11:48 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 17:11:48 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 17:11:48 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 17:11:49 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 17:11:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 17:11:49 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@674e4c82
2025-07-16 17:11:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 17:11:49 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 17:11:51 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 17:11:51 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 17:11:51 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 17:11:53 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 17:11:53 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: b6a34967-91e2-4a09-aa7e-eabba0f17aee

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-16 17:11:54 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-16 17:11:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6a2f88fa, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@22960fe4, org.springframework.security.web.context.SecurityContextHolderFilter@49b79071, org.springframework.security.web.header.HeaderWriterFilter@3236e30, org.springframework.web.filter.CorsFilter@254ae9bc, org.springframework.security.web.authentication.logout.LogoutFilter@7bc488b7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@440500f8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@107a9490, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2245103b, org.springframework.security.web.session.SessionManagementFilter@433b1d60, org.springframework.security.web.access.ExceptionTranslationFilter@685f7315, org.springframework.security.web.access.intercept.AuthorizationFilter@67676e5b]
2025-07-16 17:11:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-16 17:11:56 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 12.395 seconds (process running for 20.596)
2025-07-16 17:11:57 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-16 17:11:57 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-16 17:11:57 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-16 17:11:57 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-16 17:11:57 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0
2025-07-16 17:11:57 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-16 17:11:57 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-16 17:12:22 [http-nio-0.0.0.0-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 17:12:22 [http-nio-0.0.0.0-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 17:12:22 [http-nio-0.0.0.0-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-16 17:12:22 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-16 17:12:22 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:12:22 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-16 17:12:22 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 17:12:22 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:12:23 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 17:12:23 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 17:12:23 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* update
        for rw.health.ubuzima.entity.User */update users 
    set
        cell=?,
        date_of_birth=?,
        district=?,
        email=?,
        email_verified=?,
        emergency_contact=?,
        facility_id=?,
        gender=?,
        last_login_at=?,
        name=?,
        password_hash=?,
        phone=?,
        phone_verified=?,
        preferred_language=?,
        profile_picture_url=?,
        role=?,
        sector=?,
        status=?,
        updated_at=?,
        version=?,
        village=? 
    where
        id=? 
        and version=?
2025-07-16 17:12:24 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-16 17:12:24 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:12:24 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* dynamic native SQL query */ SELECT
        DISTINCT u.* 
    FROM
        users u 
    WHERE
        u.id IN (SELECT
            DISTINCT m.receiver_id 
        FROM
            messages m 
        WHERE
            m.sender_id = ? 
        UNION
        SELECT
            DISTINCT m.sender_id 
        FROM
            messages m 
        WHERE
            m.receiver_id = ? 
            AND m.sender_id != ?)
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/available-users?userId=3
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/available-users?userId=3
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.role<>? 
        and u1_0.id<>?
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:12:31 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:13:03 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health-records
2025-07-16 17:13:03 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:13:03 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health-records
2025-07-16 17:13:03 [http-nio-0.0.0.0-8080-exec-7] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource health-records.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-16 17:13:03 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments
2025-07-16 17:13:03 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:13:03 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments
2025-07-16 17:13:03 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-16 17:30:16 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 17:30:16 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-16 17:30:16 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-16 17:30:28 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 27420 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-16 17:30:28 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 17:30:28 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-16 17:30:30 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 17:30:30 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 108 ms. Found 23 JPA repository interfaces.
2025-07-16 17:30:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-16 17:30:31 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 17:30:31 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 17:30:31 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-16 17:30:31 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2465 ms
2025-07-16 17:30:31 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 17:30:31 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 17:30:31 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 17:30:31 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 17:30:31 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 17:30:32 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5f4df55e
2025-07-16 17:30:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 17:30:32 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 17:30:33 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 17:30:34 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 17:30:34 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 17:30:36 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 17:30:36 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 06c3fa97-7d17-4d45-b3e2-dc7ec786f968

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-16 17:30:37 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-16 17:30:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6b374f4c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3ead82f5, org.springframework.security.web.context.SecurityContextHolderFilter@1df2e286, org.springframework.security.web.header.HeaderWriterFilter@5beff5f6, org.springframework.web.filter.CorsFilter@514e2aa9, org.springframework.security.web.authentication.logout.LogoutFilter@57f4f2aa, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@834e0d0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6f07db50, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@525b7b16, org.springframework.security.web.session.SessionManagementFilter@4f75de5d, org.springframework.security.web.access.ExceptionTranslationFilter@1936633d, org.springframework.security.web.access.intercept.AuthorizationFilter@7e4e4931]
2025-07-16 17:30:38 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-16 17:30:38 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 10.251 seconds (process running for 11.174)
2025-07-16 17:30:38 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-16 17:30:38 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-16 17:30:38 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-16 17:30:38 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-16 17:30:38 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-16 17:30:38 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0
2025-07-16 17:30:38 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-16 17:30:39 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 17:30:39 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 17:30:39 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-16 17:35:26 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-16 17:35:26 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:35:26 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-16 17:35:27 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health-records
2025-07-16 17:35:27 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:35:27 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health-records
2025-07-16 17:35:27 [http-nio-0.0.0.0-8080-exec-3] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource health-records.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-16 17:35:27 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments
2025-07-16 17:35:27 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:35:27 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments
2025-07-16 17:35:28 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-16 17:36:00 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-16 17:36:00 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:36:00 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-16 17:36:00 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 17:36:00 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:36:00 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 17:36:00 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 17:36:02 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-16 17:36:02 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:36:02 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    /* dynamic native SQL query */ SELECT
        DISTINCT u.* 
    FROM
        users u 
    WHERE
        u.id IN (SELECT
            DISTINCT m.receiver_id 
        FROM
            messages m 
        WHERE
            m.sender_id = ? 
        UNION
        SELECT
            DISTINCT m.sender_id 
        FROM
            messages m 
        WHERE
            m.receiver_id = ? 
            AND m.sender_id != ?)
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/available-users?userId=3
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/available-users?userId=3
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.role<>? 
        and u1_0.id<>?
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:36:07 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:36:36 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /messages
2025-07-16 17:36:36 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:36:36 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /messages
2025-07-16 17:36:36 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:36:36 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:36:36 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:36:36 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:36:36 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.Message */insert 
    into
        messages (content, conversation_id, created_at, is_emergency, is_read, message_type, metadata, priority, read_at, receiver_id, reply_to_id, sender_id, updated_at, version) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-16 17:36:36 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:36:36 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:45:00 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing POST /messages
2025-07-16 17:45:00 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:45:00 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured POST /messages
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.Message */insert 
    into
        messages (content, conversation_id, created_at, is_emergency, is_read, message_type, metadata, priority, read_at, receiver_id, reply_to_id, sender_id, updated_at, version) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /messages
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /messages
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.Message */insert 
    into
        messages (content, conversation_id, created_at, is_emergency, is_read, message_type, metadata, priority, read_at, receiver_id, reply_to_id, sender_id, updated_at, version) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:45:01 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:45:02 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /messages
2025-07-16 17:45:02 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:45:02 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /messages
2025-07-16 17:45:02 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:45:02 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:45:02 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:45:02 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:45:02 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.Message */insert 
    into
        messages (content, conversation_id, created_at, is_emergency, is_read, message_type, metadata, priority, read_at, receiver_id, reply_to_id, sender_id, updated_at, version) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-16 17:45:02 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:45:02 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:45:09 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /messages
2025-07-16 17:45:09 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:45:09 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /messages
2025-07-16 17:45:09 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:45:09 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:45:09 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:45:09 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:45:09 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.Message */insert 
    into
        messages (content, conversation_id, created_at, is_emergency, is_read, message_type, metadata, priority, read_at, receiver_id, reply_to_id, sender_id, updated_at, version) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-16 17:45:09 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:45:09 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/conversation/3/2?page=0&size=50
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/conversation/3/2?page=0&size=50
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    /* SELECT
        m 
    FROM
        Message m 
    WHERE
        (
            m.sender = :user1 
            AND m.receiver = :user2
        ) 
        OR (
            m.sender = :user2 
            AND m.receiver = :user1
        ) 
    ORDER BY
        m.createdAt DESC */ select
            m1_0.id,
            m1_0.content,
            m1_0.conversation_id,
            m1_0.created_at,
            m1_0.is_emergency,
            m1_0.is_read,
            m1_0.message_type,
            m1_0.metadata,
            m1_0.priority,
            m1_0.read_at,
            m1_0.receiver_id,
            m1_0.reply_to_id,
            m1_0.sender_id,
            m1_0.updated_at,
            m1_0.version 
        from
            messages m1_0 
        where
            (
                m1_0.sender_id=? 
                and m1_0.receiver_id=?
            ) 
            or (
                m1_0.sender_id=? 
                and m1_0.receiver_id=?
            ) 
        order by
            m1_0.created_at desc 
        offset
            ? rows 
        fetch
            first ? rows only
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 17:45:38 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/conversation/3/2?page=0&size=50
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/conversation/3/2?page=0&size=50
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* SELECT
        m 
    FROM
        Message m 
    WHERE
        (
            m.sender = :user1 
            AND m.receiver = :user2
        ) 
        OR (
            m.sender = :user2 
            AND m.receiver = :user1
        ) 
    ORDER BY
        m.createdAt DESC */ select
            m1_0.id,
            m1_0.content,
            m1_0.conversation_id,
            m1_0.created_at,
            m1_0.is_emergency,
            m1_0.is_read,
            m1_0.message_type,
            m1_0.metadata,
            m1_0.priority,
            m1_0.read_at,
            m1_0.receiver_id,
            m1_0.reply_to_id,
            m1_0.sender_id,
            m1_0.updated_at,
            m1_0.version 
        from
            messages m1_0 
        where
            (
                m1_0.sender_id=? 
                and m1_0.receiver_id=?
            ) 
            or (
                m1_0.sender_id=? 
                and m1_0.receiver_id=?
            ) 
        order by
            m1_0.created_at desc 
        offset
            ? rows 
        fetch
            first ? rows only
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 17:46:07 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 17:55:30 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing DELETE /messages/5?requesterId=3
2025-07-16 17:55:30 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:55:30 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured DELETE /messages/5?requesterId=3
2025-07-16 17:55:30 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        m1_0.id,
        m1_0.content,
        m1_0.conversation_id,
        m1_0.created_at,
        m1_0.is_emergency,
        m1_0.is_read,
        m1_0.message_type,
        m1_0.metadata,
        m1_0.priority,
        m1_0.read_at,
        m1_0.receiver_id,
        m1_0.reply_to_id,
        m1_0.sender_id,
        m1_0.updated_at,
        m1_0.version 
    from
        messages m1_0 
    where
        m1_0.id=?
2025-07-16 17:55:30 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* one-shot delete for rw.health.ubuzima.entity.Message.attachments */delete 
    from
        message_attachments 
    where
        message_id=?
2025-07-16 17:55:31 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* delete for rw.health.ubuzima.entity.Message */delete 
    from
        messages 
    where
        id=? 
        and version=?
2025-07-16 17:55:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing DELETE /messages/4?requesterId=3
2025-07-16 17:55:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:55:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured DELETE /messages/4?requesterId=3
2025-07-16 17:55:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        m1_0.id,
        m1_0.content,
        m1_0.conversation_id,
        m1_0.created_at,
        m1_0.is_emergency,
        m1_0.is_read,
        m1_0.message_type,
        m1_0.metadata,
        m1_0.priority,
        m1_0.read_at,
        m1_0.receiver_id,
        m1_0.reply_to_id,
        m1_0.sender_id,
        m1_0.updated_at,
        m1_0.version 
    from
        messages m1_0 
    where
        m1_0.id=?
2025-07-16 17:55:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* one-shot delete for rw.health.ubuzima.entity.Message.attachments */delete 
    from
        message_attachments 
    where
        message_id=?
2025-07-16 17:55:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* delete for rw.health.ubuzima.entity.Message */delete 
    from
        messages 
    where
        id=? 
        and version=?
2025-07-16 17:55:45 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing DELETE /messages/3?requesterId=3
2025-07-16 17:55:45 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:55:45 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured DELETE /messages/3?requesterId=3
2025-07-16 17:55:45 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        m1_0.id,
        m1_0.content,
        m1_0.conversation_id,
        m1_0.created_at,
        m1_0.is_emergency,
        m1_0.is_read,
        m1_0.message_type,
        m1_0.metadata,
        m1_0.priority,
        m1_0.read_at,
        m1_0.receiver_id,
        m1_0.reply_to_id,
        m1_0.sender_id,
        m1_0.updated_at,
        m1_0.version 
    from
        messages m1_0 
    where
        m1_0.id=?
2025-07-16 17:55:45 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* one-shot delete for rw.health.ubuzima.entity.Message.attachments */delete 
    from
        message_attachments 
    where
        message_id=?
2025-07-16 17:55:45 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* delete for rw.health.ubuzima.entity.Message */delete 
    from
        messages 
    where
        id=? 
        and version=?
2025-07-16 17:55:49 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing DELETE /messages/2?requesterId=3
2025-07-16 17:55:49 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:55:49 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured DELETE /messages/2?requesterId=3
2025-07-16 17:55:49 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        m1_0.id,
        m1_0.content,
        m1_0.conversation_id,
        m1_0.created_at,
        m1_0.is_emergency,
        m1_0.is_read,
        m1_0.message_type,
        m1_0.metadata,
        m1_0.priority,
        m1_0.read_at,
        m1_0.receiver_id,
        m1_0.reply_to_id,
        m1_0.sender_id,
        m1_0.updated_at,
        m1_0.version 
    from
        messages m1_0 
    where
        m1_0.id=?
2025-07-16 17:55:49 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    /* one-shot delete for rw.health.ubuzima.entity.Message.attachments */delete 
    from
        message_attachments 
    where
        message_id=?
2025-07-16 17:55:49 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    /* delete for rw.health.ubuzima.entity.Message */delete 
    from
        messages 
    where
        id=? 
        and version=?
2025-07-16 17:56:09 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /messages
2025-07-16 17:56:09 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:56:09 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /messages
2025-07-16 17:56:09 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:56:09 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:56:09 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:56:09 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:56:09 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.Message */insert 
    into
        messages (content, conversation_id, created_at, is_emergency, is_read, message_type, metadata, priority, read_at, receiver_id, reply_to_id, sender_id, updated_at, version) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-16 17:56:09 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:56:09 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:57:13 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing PUT /messages/6
2025-07-16 17:57:13 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 17:57:13 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured PUT /messages/6
2025-07-16 17:57:13 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        m1_0.id,
        m1_0.content,
        m1_0.conversation_id,
        m1_0.created_at,
        m1_0.is_emergency,
        m1_0.is_read,
        m1_0.message_type,
        m1_0.metadata,
        m1_0.priority,
        m1_0.read_at,
        m1_0.receiver_id,
        m1_0.reply_to_id,
        m1_0.sender_id,
        m1_0.updated_at,
        m1_0.version 
    from
        messages m1_0 
    where
        m1_0.id=?
2025-07-16 17:57:13 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* update
        for rw.health.ubuzima.entity.Message */update messages 
    set
        content=?,
        conversation_id=?,
        is_emergency=?,
        is_read=?,
        message_type=?,
        metadata=?,
        priority=?,
        read_at=?,
        receiver_id=?,
        reply_to_id=?,
        sender_id=?,
        updated_at=?,
        version=? 
    where
        id=? 
        and version=?
2025-07-16 17:57:13 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 17:57:13 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 17:57:13 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 17:57:14 [http-nio-0.0.0.0-8080-exec-8] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.http.converter.HttpMessageConversionException: Type definition error: [simple type, class org.hibernate.proxy.pojo.bytebuddy.ByteBuddyInterceptor]
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:489)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:114)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:297)
	at org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor.handleReturnValue(HttpEntityMethodProcessor.java:245)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:925)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:593)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidDefinitionException: No serializer found for class org.hibernate.proxy.pojo.bytebuddy.ByteBuddyInterceptor and no properties discovered to create BeanSerializer (to avoid exception, disable SerializationFeature.FAIL_ON_EMPTY_BEANS) (through reference chain: java.util.ImmutableCollections$MapN["data"]->rw.health.ubuzima.entity.Message["sender"]->rw.health.ubuzima.entity.User$HibernateProxy$KjtqMajU["hibernateLazyInitializer"])
	at com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:77)
	at com.fasterxml.jackson.databind.SerializerProvider.reportBadDefinition(SerializerProvider.java:1308)
	at com.fasterxml.jackson.databind.DatabindContext.reportBadDefinition(DatabindContext.java:414)
	at com.fasterxml.jackson.databind.ser.impl.UnknownSerializer.failForEmpty(UnknownSerializer.java:53)
	at com.fasterxml.jackson.databind.ser.impl.UnknownSerializer.serialize(UnknownSerializer.java:30)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:772)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:772)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serializeFields(MapSerializer.java:808)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serializeWithoutTypeInfo(MapSerializer.java:764)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serialize(MapSerializer.java:720)
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serialize(MapSerializer.java:35)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:479)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:399)
	at com.fasterxml.jackson.databind.ObjectWriter$Prefetch.serialize(ObjectWriter.java:1568)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1061)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:483)
	... 127 common frames omitted
2025-07-16 18:01:54 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 18:01:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-16 18:01:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-16 18:24:44 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 24644 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-16 18:24:44 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 18:24:44 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-16 18:24:45 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 18:24:45 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 108 ms. Found 23 JPA repository interfaces.
2025-07-16 18:24:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-16 18:24:46 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 18:24:46 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 18:24:46 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-16 18:24:46 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2584 ms
2025-07-16 18:24:47 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 18:24:47 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 18:24:47 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 18:24:47 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 18:24:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 18:24:47 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@54d2f5d3
2025-07-16 18:24:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 18:24:47 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 18:24:49 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 18:24:50 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 18:24:50 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 18:24:53 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 18:24:53 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: f1837dfd-a829-4834-a1e4-f4c2437b72fe

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-16 18:24:54 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-16 18:24:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@21d39bcf, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3866122f, org.springframework.security.web.context.SecurityContextHolderFilter@3aa6a6d8, org.springframework.security.web.header.HeaderWriterFilter@10a711c7, org.springframework.web.filter.CorsFilter@6a11e139, org.springframework.security.web.authentication.logout.LogoutFilter@44e88c1c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2abd838e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2b0e7fbe, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@a229120, org.springframework.security.web.session.SessionManagementFilter@19569e44, org.springframework.security.web.access.ExceptionTranslationFilter@3d610191, org.springframework.security.web.access.intercept.AuthorizationFilter@2a897bf5]
2025-07-16 18:24:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-16 18:24:55 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 12.115 seconds (process running for 13.079)
2025-07-16 18:24:55 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-16 18:24:55 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-16 18:24:55 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-16 18:24:55 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-16 18:24:55 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-16 18:24:55 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0
2025-07-16 18:24:55 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-16 18:24:56 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 18:24:56 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 18:24:56 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 13 ms
2025-07-16 18:39:02 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-16 18:39:03 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:39:03 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-16 18:39:03 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health-records
2025-07-16 18:39:03 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:39:03 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health-records
2025-07-16 18:39:03 [http-nio-0.0.0.0-8080-exec-1] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource health-records.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-16 18:39:03 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments
2025-07-16 18:39:03 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:39:03 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments
2025-07-16 18:39:04 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-16 18:39:39 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-16 18:39:39 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:39:39 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-16 18:39:40 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 18:39:40 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:39:40 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 18:39:40 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 18:39:41 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-16 18:39:41 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:39:41 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-16 18:39:44 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 18:39:44 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:39:44 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 18:39:44 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 18:39:44 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:39:45 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* dynamic native SQL query */ SELECT
        DISTINCT u.* 
    FROM
        users u 
    WHERE
        u.id IN (SELECT
            DISTINCT m.receiver_id 
        FROM
            messages m 
        WHERE
            m.sender_id = ? 
        UNION
        SELECT
            DISTINCT m.sender_id 
        FROM
            messages m 
        WHERE
            m.receiver_id = ? 
            AND m.sender_id != ?)
2025-07-16 18:39:45 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/available-users?userId=3
2025-07-16 18:39:45 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:39:45 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/available-users?userId=3
2025-07-16 18:39:45 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 18:39:45 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:39:45 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.role<>? 
        and u1_0.id<>?
2025-07-16 18:39:45 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:39:45 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 18:39:55 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/conversation/3/2?page=0&size=50
2025-07-16 18:39:55 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:39:55 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/conversation/3/2?page=0&size=50
2025-07-16 18:39:55 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 18:39:55 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:39:55 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 18:39:55 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:39:55 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* SELECT
        m 
    FROM
        Message m 
    WHERE
        (
            m.sender = :user1 
            AND m.receiver = :user2
        ) 
        OR (
            m.sender = :user2 
            AND m.receiver = :user1
        ) 
    ORDER BY
        m.createdAt DESC */ select
            m1_0.id,
            m1_0.content,
            m1_0.conversation_id,
            m1_0.created_at,
            m1_0.is_emergency,
            m1_0.is_read,
            m1_0.message_type,
            m1_0.metadata,
            m1_0.priority,
            m1_0.read_at,
            m1_0.receiver_id,
            m1_0.reply_to_id,
            m1_0.sender_id,
            m1_0.updated_at,
            m1_0.version 
        from
            messages m1_0 
        where
            (
                m1_0.sender_id=? 
                and m1_0.receiver_id=?
            ) 
            or (
                m1_0.sender_id=? 
                and m1_0.receiver_id=?
            ) 
        order by
            m1_0.created_at desc 
        offset
            ? rows 
        fetch
            first ? rows only
2025-07-16 18:39:55 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 18:39:55 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 18:40:23 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing DELETE /messages/1?requesterId=3
2025-07-16 18:40:23 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:40:23 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured DELETE /messages/1?requesterId=3
2025-07-16 18:40:23 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        m1_0.id,
        m1_0.content,
        m1_0.conversation_id,
        m1_0.created_at,
        m1_0.is_emergency,
        m1_0.is_read,
        m1_0.message_type,
        m1_0.metadata,
        m1_0.priority,
        m1_0.read_at,
        m1_0.receiver_id,
        m1_0.reply_to_id,
        m1_0.sender_id,
        m1_0.updated_at,
        m1_0.version 
    from
        messages m1_0 
    where
        m1_0.id=?
2025-07-16 18:40:23 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* one-shot delete for rw.health.ubuzima.entity.Message.attachments */delete 
    from
        message_attachments 
    where
        message_id=?
2025-07-16 18:40:23 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* delete for rw.health.ubuzima.entity.Message */delete 
    from
        messages 
    where
        id=? 
        and version=?
2025-07-16 18:41:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing PUT /messages/6
2025-07-16 18:41:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:41:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured PUT /messages/6
2025-07-16 18:41:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        m1_0.id,
        m1_0.content,
        m1_0.conversation_id,
        m1_0.created_at,
        m1_0.is_emergency,
        m1_0.is_read,
        m1_0.message_type,
        m1_0.metadata,
        m1_0.priority,
        m1_0.read_at,
        m1_0.receiver_id,
        m1_0.reply_to_id,
        m1_0.sender_id,
        m1_0.updated_at,
        m1_0.version 
    from
        messages m1_0 
    where
        m1_0.id=?
2025-07-16 18:41:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* update
        for rw.health.ubuzima.entity.Message */update messages 
    set
        content=?,
        conversation_id=?,
        is_emergency=?,
        is_read=?,
        message_type=?,
        metadata=?,
        priority=?,
        read_at=?,
        receiver_id=?,
        reply_to_id=?,
        sender_id=?,
        updated_at=?,
        version=? 
    where
        id=? 
        and version=?
2025-07-16 18:41:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* SELECT
        m 
    FROM
        Message m 
    LEFT JOIN
        
    FETCH
        m.sender 
    LEFT JOIN
        
    FETCH
        m.receiver 
    WHERE
        m.id = :messageId */ select
            m1_0.id,
            m1_0.content,
            m1_0.conversation_id,
            m1_0.created_at,
            m1_0.is_emergency,
            m1_0.is_read,
            m1_0.message_type,
            m1_0.metadata,
            m1_0.priority,
            m1_0.read_at,
            m1_0.receiver_id,
            r1_0.id,
            r1_0.cell,
            r1_0.created_at,
            r1_0.date_of_birth,
            r1_0.district,
            r1_0.email,
            r1_0.email_verified,
            r1_0.emergency_contact,
            r1_0.facility_id,
            r1_0.gender,
            r1_0.last_login_at,
            r1_0.name,
            r1_0.password_hash,
            r1_0.phone,
            r1_0.phone_verified,
            r1_0.preferred_language,
            r1_0.profile_picture_url,
            r1_0.role,
            r1_0.sector,
            r1_0.status,
            r1_0.updated_at,
            r1_0.version,
            r1_0.village,
            m1_0.reply_to_id,
            m1_0.sender_id,
            s1_0.id,
            s1_0.cell,
            s1_0.created_at,
            s1_0.date_of_birth,
            s1_0.district,
            s1_0.email,
            s1_0.email_verified,
            s1_0.emergency_contact,
            s1_0.facility_id,
            s1_0.gender,
            s1_0.last_login_at,
            s1_0.name,
            s1_0.password_hash,
            s1_0.phone,
            s1_0.phone_verified,
            s1_0.preferred_language,
            s1_0.profile_picture_url,
            s1_0.role,
            s1_0.sector,
            s1_0.status,
            s1_0.updated_at,
            s1_0.version,
            s1_0.village,
            m1_0.updated_at,
            m1_0.version 
        from
            messages m1_0 
        left join
            users s1_0 
                on s1_0.id=m1_0.sender_id 
        left join
            users r1_0 
                on r1_0.id=m1_0.receiver_id 
        where
            m1_0.id=?
2025-07-16 18:41:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:41:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:41:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        a1_0.message_id,
        a1_0.attachment_url 
    from
        message_attachments a1_0 
    where
        a1_0.message_id=?
2025-07-16 18:41:26 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /messages
2025-07-16 18:41:26 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:41:26 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /messages
2025-07-16 18:41:26 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 18:41:26 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:41:26 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 18:41:26 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:41:26 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.Message */insert 
    into
        messages (content, conversation_id, created_at, is_emergency, is_read, message_type, metadata, priority, read_at, receiver_id, reply_to_id, sender_id, updated_at, version) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    /* dynamic native SQL query */ SELECT
        DISTINCT u.* 
    FROM
        users u 
    WHERE
        u.id IN (SELECT
            DISTINCT m.receiver_id 
        FROM
            messages m 
        WHERE
            m.sender_id = ? 
        UNION
        SELECT
            DISTINCT m.sender_id 
        FROM
            messages m 
        WHERE
            m.receiver_id = ? 
            AND m.sender_id != ?)
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/available-users?userId=3
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/available-users?userId=3
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.role<>? 
        and u1_0.id<>?
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 18:43:33 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 19:29:13 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 19:29:13 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 19:29:13 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 19:29:13 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 19:29:13 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 19:29:13 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* dynamic native SQL query */ SELECT
        DISTINCT u.* 
    FROM
        users u 
    WHERE
        u.id IN (SELECT
            DISTINCT m.receiver_id 
        FROM
            messages m 
        WHERE
            m.sender_id = ? 
        UNION
        SELECT
            DISTINCT m.sender_id 
        FROM
            messages m 
        WHERE
            m.receiver_id = ? 
            AND m.sender_id != ?)
2025-07-16 19:29:14 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/available-users?userId=3
2025-07-16 19:29:14 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 19:29:14 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/available-users?userId=3
2025-07-16 19:29:14 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 19:29:14 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 19:29:14 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.role<>? 
        and u1_0.id<>?
2025-07-16 19:29:14 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 19:29:14 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 19:30:03 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /emergency-contacts
2025-07-16 19:30:03 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 19:30:03 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /emergency-contacts
2025-07-16 19:30:03 [http-nio-0.0.0.0-8080-exec-5] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource emergency-contacts.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-16 19:30:04 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /emergency-contacts/system
2025-07-16 19:30:04 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 19:30:04 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /emergency-contacts/system
2025-07-16 19:30:04 [http-nio-0.0.0.0-8080-exec-6] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource emergency-contacts/system.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-16 19:30:41 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /emergency-contacts
2025-07-16 19:30:41 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 19:30:41 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /emergency-contacts
2025-07-16 19:30:41 [http-nio-0.0.0.0-8080-exec-7] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource emergency-contacts.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-16 19:30:41 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /emergency-contacts/system
2025-07-16 19:30:41 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 19:30:41 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /emergency-contacts/system
2025-07-16 19:30:41 [http-nio-0.0.0.0-8080-exec-8] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource emergency-contacts/system.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-16 19:43:08 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /client/3/dashboard/stats
2025-07-16 19:43:08 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 19:43:08 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /client/3/dashboard/stats
2025-07-16 19:43:08 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 19:43:08 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 19:43:08 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 19:43:08 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* SELECT
        hr 
    FROM
        HealthRecord hr 
    WHERE
        hr.user.id = :userId 
    ORDER BY
        hr.lastUpdated DESC */ select
            hr1_0.id,
            hr1_0.assigned_health_worker_id,
            hr1_0.bmi,
            hr1_0.bp_unit,
            hr1_0.bp_value,
            hr1_0.created_at,
            hr1_0.health_status,
            hr1_0.heart_rate_unit,
            hr1_0.heart_rate_value,
            hr1_0.height_unit,
            hr1_0.height_value,
            hr1_0.is_verified,
            hr1_0.kg_unit,
            hr1_0.kg_value,
            hr1_0.last_updated,
            hr1_0.notes,
            hr1_0.recorded_by,
            hr1_0.temp_unit,
            hr1_0.temp_value,
            hr1_0.updated_at,
            hr1_0.user_id,
            hr1_0.version 
        from
            health_records hr1_0 
        where
            hr1_0.user_id=? 
        order by
            hr1_0.last_updated desc
2025-07-16 19:50:41 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /client/3/dashboard/stats
2025-07-16 19:50:41 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 19:50:41 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /client/3/dashboard/stats
2025-07-16 19:50:41 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 19:50:41 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 19:50:41 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 19:50:41 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* SELECT
        hr 
    FROM
        HealthRecord hr 
    WHERE
        hr.user.id = :userId 
    ORDER BY
        hr.lastUpdated DESC */ select
            hr1_0.id,
            hr1_0.assigned_health_worker_id,
            hr1_0.bmi,
            hr1_0.bp_unit,
            hr1_0.bp_value,
            hr1_0.created_at,
            hr1_0.health_status,
            hr1_0.heart_rate_unit,
            hr1_0.heart_rate_value,
            hr1_0.height_unit,
            hr1_0.height_value,
            hr1_0.is_verified,
            hr1_0.kg_unit,
            hr1_0.kg_value,
            hr1_0.last_updated,
            hr1_0.notes,
            hr1_0.recorded_by,
            hr1_0.temp_unit,
            hr1_0.temp_value,
            hr1_0.updated_at,
            hr1_0.user_id,
            hr1_0.version 
        from
            health_records hr1_0 
        where
            hr1_0.user_id=? 
        order by
            hr1_0.last_updated desc
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* dynamic native SQL query */ SELECT
        DISTINCT u.* 
    FROM
        users u 
    WHERE
        u.id IN (SELECT
            DISTINCT m.receiver_id 
        FROM
            messages m 
        WHERE
            m.sender_id = ? 
        UNION
        SELECT
            DISTINCT m.sender_id 
        FROM
            messages m 
        WHERE
            m.receiver_id = ? 
            AND m.sender_id != ?)
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/available-users?userId=3
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/available-users?userId=3
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.role<>? 
        and u1_0.id<>?
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 19:56:40 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 20:22:45 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-16 20:22:45 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 20:22:45 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-16 20:22:45 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health-records
2025-07-16 20:22:45 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 20:22:45 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health-records
2025-07-16 20:22:45 [http-nio-0.0.0.0-8080-exec-1] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource health-records.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-16 20:22:45 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments
2025-07-16 20:22:45 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 20:22:45 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments
2025-07-16 20:22:46 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-16 20:24:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-16 20:24:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 20:24:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-16 20:24:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 20:24:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 20:24:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 20:24:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 20:24:17 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-16 20:24:17 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 20:24:17 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-16 21:27:46 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-16 21:27:46 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 21:27:46 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-16 21:27:47 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health-records
2025-07-16 21:27:47 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 21:27:47 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health-records
2025-07-16 21:27:47 [http-nio-0.0.0.0-8080-exec-2] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource health-records.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-16 21:27:48 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments
2025-07-16 21:27:48 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 21:27:48 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments
2025-07-16 21:27:48 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-16 21:29:16 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-16 21:29:16 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 21:29:16 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-16 21:29:16 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 21:29:16 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 21:29:16 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 21:29:16 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-16 21:29:18 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-16 21:29:18 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 21:29:18 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/conversations?userId=3&page=0&limit=20
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* dynamic native SQL query */ SELECT
        DISTINCT u.* 
    FROM
        users u 
    WHERE
        u.id IN (SELECT
            DISTINCT m.receiver_id 
        FROM
            messages m 
        WHERE
            m.sender_id = ? 
        UNION
        SELECT
            DISTINCT m.sender_id 
        FROM
            messages m 
        WHERE
            m.receiver_id = ? 
            AND m.sender_id != ?)
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /messages/available-users?userId=3
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /messages/available-users?userId=3
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.role<>? 
        and u1_0.id<>?
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-16 21:29:35 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-16 21:29:45 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /education/lessons?page=0&limit=20
2025-07-16 21:29:45 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 21:29:45 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /education/lessons?page=0&limit=20
2025-07-16 21:29:46 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0 
    where
        el1_0.is_published 
    order by
        el1_0.order_index
2025-07-16 21:29:46 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        t1_0.lesson_id,
        t1_0.tag 
    from
        lesson_tags t1_0 
    where
        t1_0.lesson_id=?
2025-07-16 21:29:46 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        iu1_0.lesson_id,
        iu1_0.image_url 
    from
        lesson_images iu1_0 
    where
        iu1_0.lesson_id=?
2025-07-16 21:29:46 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        t1_0.lesson_id,
        t1_0.tag 
    from
        lesson_tags t1_0 
    where
        t1_0.lesson_id=?
2025-07-16 21:29:46 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        iu1_0.lesson_id,
        iu1_0.image_url 
    from
        lesson_images iu1_0 
    where
        iu1_0.lesson_id=?
2025-07-16 21:29:46 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        t1_0.lesson_id,
        t1_0.tag 
    from
        lesson_tags t1_0 
    where
        t1_0.lesson_id=?
2025-07-16 21:29:46 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        iu1_0.lesson_id,
        iu1_0.image_url 
    from
        lesson_images iu1_0 
    where
        iu1_0.lesson_id=?
2025-07-16 21:29:46 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /education/progress/3
2025-07-16 21:29:46 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 21:29:46 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /education/progress/3
2025-07-16 21:29:52 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /contraception/active
2025-07-16 21:29:52 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 21:29:52 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /contraception/active
2025-07-16 21:29:52 [http-nio-0.0.0.0-8080-exec-1] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValueInternal(RequestParamMethodArgumentResolver.java:220)
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValue(RequestParamMethodArgumentResolver.java:196)
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:126)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:224)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
