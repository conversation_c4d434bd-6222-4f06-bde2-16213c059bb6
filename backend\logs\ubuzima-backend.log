2025-07-13 18:00:21 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 32436 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-13 18:00:21 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-13 18:00:21 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-13 18:00:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-13 18:00:27 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 247 ms. Found 19 JPA repository interfaces.
2025-07-13 18:00:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8080 (http)
2025-07-13 18:00:28 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-13 18:00:28 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-13 18:00:29 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-13 18:00:29 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7714 ms
2025-07-13 18:00:29 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-13 18:00:29 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-13 18:00:30 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-13 18:00:30 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-13 18:00:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-13 18:00:31 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3aab42d6
2025-07-13 18:00:31 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-13 18:00:31 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-13 18:00:35 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-13 18:00:35 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-13 18:00:36 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-13 18:00:39 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-13 18:00:39 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 1b148c09-d94e-4273-a826-b06ca0a6a867

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-13 18:00:40 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-13 18:00:40 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@22f302ff, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4944315e, org.springframework.security.web.context.SecurityContextHolderFilter@46daaaab, org.springframework.security.web.header.HeaderWriterFilter@72388b94, org.springframework.web.filter.CorsFilter@5b64d32c, org.springframework.security.web.authentication.logout.LogoutFilter@12bef203, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@64664211, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@435bc916, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@22a83da, org.springframework.security.web.session.SessionManagementFilter@2dd21f40, org.springframework.security.web.access.ExceptionTranslationFilter@4287bd46, org.springframework.security.web.access.intercept.AuthorizationFilter@7d213ae5]
2025-07-13 18:00:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-13 18:00:41 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 21.154 seconds (process running for 32.043)
2025-07-13 18:00:41 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-13 18:00:41 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-13 18:00:41 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-13 18:00:41 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-13 18:00:41 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-13 18:00:41 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0
2025-07-13 18:00:41 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-13 18:00:42 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-13 18:00:42 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-13 18:00:42 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
