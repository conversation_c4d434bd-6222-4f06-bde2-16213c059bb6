2025-07-14 13:19:07 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 37728 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-14 13:19:07 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-14 13:19:07 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-14 13:19:11 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-14 13:19:11 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 195 ms. Found 19 JPA repository interfaces.
2025-07-14 13:19:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8080 (http)
2025-07-14 13:19:13 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-14 13:19:13 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-14 13:19:13 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-14 13:19:13 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6127 ms
2025-07-14 13:19:14 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-14 13:19:14 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-14 13:19:14 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-14 13:19:14 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-14 13:19:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-14 13:19:15 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@56e8a8a0
2025-07-14 13:19:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-14 13:19:15 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-14 13:19:17 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-14 13:19:18 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-14 13:19:19 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-14 13:19:22 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-14 13:19:22 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: d036ab0b-ec1d-4101-9211-4614f21ec09e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-14 13:19:23 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-14 13:19:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@701bb19c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@31a4dd80, org.springframework.security.web.context.SecurityContextHolderFilter@38e2b7f4, org.springframework.security.web.header.HeaderWriterFilter@52181f10, org.springframework.web.filter.CorsFilter@3f3dac49, org.springframework.security.web.authentication.logout.LogoutFilter@10c64b0b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3a88e385, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5e1ebcb0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1d8fec8a, org.springframework.security.web.session.SessionManagementFilter@741a0b56, org.springframework.security.web.access.ExceptionTranslationFilter@55593de5, org.springframework.security.web.access.intercept.AuthorizationFilter@327751eb]
2025-07-14 13:19:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-14 13:19:25 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 18.795 seconds (process running for 29.183)
2025-07-14 13:19:25 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-14 13:19:25 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 13:19:25 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 13:19:25 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 13:19:25 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-14 13:19:25 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0
2025-07-14 13:19:25 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-14 13:19:26 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 13:19:26 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-14 13:19:26 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-14 13:19:56 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-14 13:19:56 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:19:56 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-14 13:19:56 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 13:19:56 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:19:56 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 13:19:56 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 13:19:57 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* update
        for rw.health.ubuzima.entity.User */update users 
    set
        cell=?,
        date_of_birth=?,
        district=?,
        email=?,
        email_verified=?,
        emergency_contact=?,
        facility_id=?,
        gender=?,
        last_login_at=?,
        name=?,
        password_hash=?,
        phone=?,
        phone_verified=?,
        preferred_language=?,
        profile_picture_url=?,
        role=?,
        sector=?,
        status=?,
        updated_at=?,
        version=?,
        village=? 
    where
        id=? 
        and version=?
2025-07-14 13:19:58 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-14 13:19:58 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:19:58 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-14 13:20:06 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:20:06 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:20:06 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:20:06 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:20:06 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:20:06 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:20:30 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:20:30 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:20:30 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:20:30 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:20:30 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:20:30 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:22:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&status=SCHEDULED&userId=3
2025-07-14 13:22:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:22:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&status=SCHEDULED&userId=3
2025-07-14 13:22:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:22:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:22:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:22:27 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&status=CONFIRMED&userId=3
2025-07-14 13:22:27 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:22:27 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&status=CONFIRMED&userId=3
2025-07-14 13:22:27 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:22:27 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:22:27 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:22:29 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&status=SCHEDULED&userId=3
2025-07-14 13:22:29 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:22:29 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&status=SCHEDULED&userId=3
2025-07-14 13:22:29 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:22:29 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:22:29 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:22:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:22:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:22:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:22:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:22:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:22:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:29:08 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&status=SCHEDULED&userId=3
2025-07-14 13:29:08 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:29:08 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&status=SCHEDULED&userId=3
2025-07-14 13:29:08 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:29:08 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:29:08 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:29:13 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:29:13 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:29:13 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:29:13 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:29:13 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:29:13 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:29:18 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /facilities?page=0&limit=10
2025-07-14 13:29:18 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:29:18 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /facilities?page=0&limit=10
2025-07-14 13:29:18 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.is_active
2025-07-14 13:29:18 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:29:18 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:29:18 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:29:18 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:29:18 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:38:33 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-14 13:38:33 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-14 13:38:33 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-14 13:38:39 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 24964 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-14 13:38:39 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-14 13:38:39 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-14 13:38:41 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-14 13:38:41 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 165 ms. Found 19 JPA repository interfaces.
2025-07-14 13:38:42 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-14 13:38:42 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-14 13:38:42 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-14 13:38:43 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-14 13:38:43 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3439 ms
2025-07-14 13:38:43 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-14 13:38:43 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-14 13:38:43 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-14 13:38:44 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-14 13:38:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-14 13:38:44 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@bea283b
2025-07-14 13:38:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-14 13:38:44 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-14 13:38:46 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-14 13:38:47 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-14 13:38:48 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-14 13:38:50 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-14 13:38:50 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: f63ff8d8-91bf-4643-9fc5-b4742b58caeb

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-14 13:38:51 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-14 13:38:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@28bb278a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7b04ea0d, org.springframework.security.web.context.SecurityContextHolderFilter@7447e4aa, org.springframework.security.web.header.HeaderWriterFilter@50b0cd7, org.springframework.web.filter.CorsFilter@4060c7fe, org.springframework.security.web.authentication.logout.LogoutFilter@4f48e01f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2275d9b4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7fea956b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5879efdb, org.springframework.security.web.session.SessionManagementFilter@4d2cdda2, org.springframework.security.web.access.ExceptionTranslationFilter@58288d3, org.springframework.security.web.access.intercept.AuthorizationFilter@6f4201f2]
2025-07-14 13:38:52 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-14 13:38:52 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 13.964 seconds (process running for 15.169)
2025-07-14 13:38:53 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 13:38:53 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 13:38:53 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 13:38:53 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-14 13:38:53 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0
2025-07-14 13:38:53 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-14 13:38:53 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-14 13:38:54 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 13:38:54 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-14 13:38:54 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-14 13:41:12 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-14 13:41:12 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:41:12 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-14 13:41:13 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health-records
2025-07-14 13:41:13 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:41:13 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health-records
2025-07-14 13:41:13 [http-nio-0.0.0.0-8080-exec-3] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource health-records.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-14 13:41:13 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments
2025-07-14 13:41:13 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:41:13 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments
2025-07-14 13:41:13 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-14 13:43:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-14 13:43:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:43:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-14 13:43:17 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 13:43:17 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:43:17 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 13:43:17 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 13:43:19 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-14 13:43:19 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:43:19 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-14 13:43:24 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:43:24 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:43:24 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:43:24 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:43:24 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:43:24 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:43:28 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /facilities?page=0&limit=10
2025-07-14 13:43:28 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:43:28 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /facilities?page=0&limit=10
2025-07-14 13:43:28 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.is_active
2025-07-14 13:43:28 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:43:28 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:43:28 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:43:28 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:43:28 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:44:09 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:44:09 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:44:09 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:44:09 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:44:09 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:44:09 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:44:12 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&status=SCHEDULED&userId=3
2025-07-14 13:44:12 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:44:12 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&status=SCHEDULED&userId=3
2025-07-14 13:44:12 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:44:12 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:44:12 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:44:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&status=CONFIRMED&userId=3
2025-07-14 13:44:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:44:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&status=CONFIRMED&userId=3
2025-07-14 13:44:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:44:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:44:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:44:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:44:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:44:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&userId=3
2025-07-14 13:44:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 13:44:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 13:44:16 [http-nio-0.0.0.0-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 13:44:18 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /facilities?page=0&limit=10
2025-07-14 13:44:18 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:44:18 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /facilities?page=0&limit=10
2025-07-14 13:44:18 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.is_active
2025-07-14 13:44:18 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:44:18 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:44:18 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:44:19 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:44:19 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 13:59:15 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-14 13:59:15 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:59:15 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-14 13:59:16 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health-records
2025-07-14 13:59:16 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:59:16 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health-records
2025-07-14 13:59:16 [http-nio-0.0.0.0-8080-exec-1] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource health-records.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-14 13:59:16 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments
2025-07-14 13:59:16 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 13:59:16 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments
2025-07-14 13:59:17 [http-nio-0.0.0.0-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-14 14:05:39 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-14 14:05:39 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-14 14:05:39 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-14 14:05:44 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 26272 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-14 14:05:44 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-14 14:05:44 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-14 14:05:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-14 14:05:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 188 ms. Found 19 JPA repository interfaces.
2025-07-14 14:05:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-14 14:05:47 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-14 14:05:47 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-14 14:05:48 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-14 14:05:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3569 ms
2025-07-14 14:05:48 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-14 14:05:48 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-14 14:05:48 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-14 14:05:49 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-14 14:05:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-14 14:05:49 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5b2c7186
2025-07-14 14:05:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-14 14:05:49 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-14 14:05:52 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-14 14:05:53 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-14 14:05:53 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-14 14:05:57 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-14 14:05:57 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 5a0083ca-06b3-4d6b-9d36-b850e500f071

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-14 14:05:58 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-14 14:05:58 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4b047aba, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6f4201f2, org.springframework.security.web.context.SecurityContextHolderFilter@4d2cdda2, org.springframework.security.web.header.HeaderWriterFilter@587c6919, org.springframework.web.filter.CorsFilter@3b4d0434, org.springframework.security.web.authentication.logout.LogoutFilter@1aed62f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@317c357f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@39db0f18, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3fb09dfe, org.springframework.security.web.session.SessionManagementFilter@7e1d3c6e, org.springframework.security.web.access.ExceptionTranslationFilter@5f3350d9, org.springframework.security.web.access.intercept.AuthorizationFilter@6ef3e83e]
2025-07-14 14:05:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-14 14:05:59 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 15.897 seconds (process running for 17.235)
2025-07-14 14:05:59 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 14:05:59 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 14:05:59 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 14:05:59 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-14 14:05:59 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0
2025-07-14 14:05:59 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-14 14:05:59 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-14 14:06:00 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 14:06:00 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-14 14:06:00 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-14 14:09:11 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-14 14:09:11 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:09:11 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-14 14:09:24 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /facilities
2025-07-14 14:09:24 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:09:24 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /facilities
2025-07-14 14:09:24 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.is_active
2025-07-14 14:09:24 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:09:24 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:09:24 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:09:24 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:09:24 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:10:34 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-14 14:10:34 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:10:34 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-14 14:10:34 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health-records
2025-07-14 14:10:34 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:10:34 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health-records
2025-07-14 14:10:35 [http-nio-0.0.0.0-8080-exec-1] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource health-records.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-14 14:10:35 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments
2025-07-14 14:10:35 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:10:35 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments
2025-07-14 14:10:36 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-14 14:11:27 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-14 14:11:27 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:11:27 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-14 14:11:28 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 14:11:28 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 14:11:28 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 14:11:28 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 14:11:30 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-14 14:11:30 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:11:30 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /education/lessons?page=0&limit=20
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /education/lessons?page=0&limit=20
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0 
    where
        el1_0.is_published 
    order by
        el1_0.order_index
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        t1_0.lesson_id,
        t1_0.tag 
    from
        lesson_tags t1_0 
    where
        t1_0.lesson_id=?
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        iu1_0.lesson_id,
        iu1_0.image_url 
    from
        lesson_images iu1_0 
    where
        iu1_0.lesson_id=?
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        t1_0.lesson_id,
        t1_0.tag 
    from
        lesson_tags t1_0 
    where
        t1_0.lesson_id=?
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        iu1_0.lesson_id,
        iu1_0.image_url 
    from
        lesson_images iu1_0 
    where
        iu1_0.lesson_id=?
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        t1_0.lesson_id,
        t1_0.tag 
    from
        lesson_tags t1_0 
    where
        t1_0.lesson_id=?
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        iu1_0.lesson_id,
        iu1_0.image_url 
    from
        lesson_images iu1_0 
    where
        iu1_0.lesson_id=?
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /education/progress/3
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:11:43 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /education/progress/3
2025-07-14 14:11:51 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /user-centric-health/record/3
2025-07-14 14:11:51 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:11:51 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /user-centric-health/record/3
2025-07-14 14:11:51 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        hr1_0.id,
        hr1_0.assigned_health_worker_id,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        u1_0.id=?
2025-07-14 14:11:51 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 14:11:51 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 14:12:10 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&userId=3
2025-07-14 14:12:10 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:12:10 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&userId=3
2025-07-14 14:12:10 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 14:12:10 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 14:12:10 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 14:12:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /facilities?page=0&limit=10
2025-07-14 14:12:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:12:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /facilities?page=0&limit=10
2025-07-14 14:12:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.is_active
2025-07-14 14:12:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:12:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:12:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:12:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:12:14 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:12:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /facilities/1/health-workers
2025-07-14 14:12:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:12:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /facilities/1/health-workers
2025-07-14 14:12:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.id=?
2025-07-14 14:12:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* SELECT
        u 
    FROM
        User u 
    WHERE
        u.facilityId = :facilityId 
        AND u.role = 'HEALTH_WORKER' */ select
            u1_0.id,
            u1_0.cell,
            u1_0.created_at,
            u1_0.date_of_birth,
            u1_0.district,
            u1_0.email,
            u1_0.email_verified,
            u1_0.emergency_contact,
            u1_0.facility_id,
            u1_0.gender,
            u1_0.last_login_at,
            u1_0.name,
            u1_0.password_hash,
            u1_0.phone,
            u1_0.phone_verified,
            u1_0.preferred_language,
            u1_0.profile_picture_url,
            u1_0.role,
            u1_0.sector,
            u1_0.status,
            u1_0.updated_at,
            u1_0.version,
            u1_0.village 
        from
            users u1_0 
        where
            u1_0.facility_id=? 
            and u1_0.role='HEALTH_WORKER'
2025-07-14 14:12:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 14:12:23 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-14 14:13:05 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments/available-slots?facilityId=1&date=2025-07-15&healthWorkerId=2
2025-07-14 14:13:05 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:13:05 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments/available-slots?facilityId=1&date=2025-07-15&healthWorkerId=2
2025-07-14 14:13:05 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.id=?
2025-07-14 14:13:05 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 14:13:05 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 14:13:05 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    /* SELECT
        ts 
    FROM
        TimeSlot ts 
    WHERE
        ts.healthFacility = :facility 
        AND ts.healthWorker = :healthWorker 
        AND ts.startTime BETWEEN :startDate AND :endDate 
        AND ts.isAvailable = true */ select
            ts1_0.id,
            ts1_0.created_at,
            ts1_0.current_appointments,
            ts1_0.end_time,
            ts1_0.health_facility_id,
            ts1_0.health_worker_id,
            ts1_0.is_available,
            ts1_0.max_appointments,
            ts1_0.reason,
            ts1_0.start_time,
            ts1_0.updated_at,
            ts1_0.version 
        from
            time_slots ts1_0 
        where
            ts1_0.health_facility_id=? 
            and ts1_0.health_worker_id=? 
            and ts1_0.start_time between ? and ? 
            and ts1_0.is_available=true
2025-07-14 14:13:05 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:13:23 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments/available-slots?facilityId=1&date=2025-07-16&healthWorkerId=2
2025-07-14 14:13:23 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:13:23 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments/available-slots?facilityId=1&date=2025-07-16&healthWorkerId=2
2025-07-14 14:13:23 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.id=?
2025-07-14 14:13:23 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 14:13:23 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 14:13:23 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* SELECT
        ts 
    FROM
        TimeSlot ts 
    WHERE
        ts.healthFacility = :facility 
        AND ts.healthWorker = :healthWorker 
        AND ts.startTime BETWEEN :startDate AND :endDate 
        AND ts.isAvailable = true */ select
            ts1_0.id,
            ts1_0.created_at,
            ts1_0.current_appointments,
            ts1_0.end_time,
            ts1_0.health_facility_id,
            ts1_0.health_worker_id,
            ts1_0.is_available,
            ts1_0.max_appointments,
            ts1_0.reason,
            ts1_0.start_time,
            ts1_0.updated_at,
            ts1_0.version 
        from
            time_slots ts1_0 
        where
            ts1_0.health_facility_id=? 
            and ts1_0.health_worker_id=? 
            and ts1_0.start_time between ? and ? 
            and ts1_0.is_available=true
2025-07-14 14:13:23 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:13:38 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments/available-slots?facilityId=1&date=2025-07-16&healthWorkerId=2
2025-07-14 14:13:38 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:13:38 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments/available-slots?facilityId=1&date=2025-07-16&healthWorkerId=2
2025-07-14 14:13:38 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.id=?
2025-07-14 14:13:38 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 14:13:38 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 14:13:38 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* SELECT
        ts 
    FROM
        TimeSlot ts 
    WHERE
        ts.healthFacility = :facility 
        AND ts.healthWorker = :healthWorker 
        AND ts.startTime BETWEEN :startDate AND :endDate 
        AND ts.isAvailable = true */ select
            ts1_0.id,
            ts1_0.created_at,
            ts1_0.current_appointments,
            ts1_0.end_time,
            ts1_0.health_facility_id,
            ts1_0.health_worker_id,
            ts1_0.is_available,
            ts1_0.max_appointments,
            ts1_0.reason,
            ts1_0.start_time,
            ts1_0.updated_at,
            ts1_0.version 
        from
            time_slots ts1_0 
        where
            ts1_0.health_facility_id=? 
            and ts1_0.health_worker_id=? 
            and ts1_0.start_time between ? and ? 
            and ts1_0.is_available=true
2025-07-14 14:13:38 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 14:14:09 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&userId=3
2025-07-14 14:14:09 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:14:09 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&userId=3
2025-07-14 14:14:09 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 14:14:09 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 14:14:09 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 14:30:06 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-14 14:30:07 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:30:07 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-14 14:30:08 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health-records
2025-07-14 14:30:08 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:30:08 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health-records
2025-07-14 14:30:08 [http-nio-0.0.0.0-8080-exec-9] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource health-records.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-14 14:30:08 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments
2025-07-14 14:30:08 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 14:30:08 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments
2025-07-14 14:30:09 [http-nio-0.0.0.0-8080-exec-10] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-14 17:52:32 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-14 17:52:32 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:52:32 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-14 17:52:32 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 17:52:32 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 17:52:33 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 17:52:33 [http-nio-0.0.0.0-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-14 17:52:33 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-14 17:52:33 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:52:33 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-14 17:52:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /user-centric-health/record/3
2025-07-14 17:52:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:52:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /user-centric-health/record/3
2025-07-14 17:52:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        hr1_0.id,
        hr1_0.assigned_health_worker_id,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        u1_0.id=?
2025-07-14 17:52:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 17:52:37 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 17:52:57 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&userId=3
2025-07-14 17:52:57 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:52:57 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&userId=3
2025-07-14 17:52:57 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 17:52:57 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 17:52:57 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 17:53:01 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /facilities?page=0&limit=10
2025-07-14 17:53:01 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:53:01 [http-nio-0.0.0.0-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /facilities?page=0&limit=10
2025-07-14 17:53:01 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.is_active
2025-07-14 17:53:01 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 17:53:01 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 17:53:01 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 17:53:01 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 17:53:01 [http-nio-0.0.0.0-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 17:53:13 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /facilities/1/health-workers
2025-07-14 17:53:13 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:53:13 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /facilities/1/health-workers
2025-07-14 17:53:13 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.id=?
2025-07-14 17:53:13 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* SELECT
        u 
    FROM
        User u 
    WHERE
        u.facilityId = :facilityId 
        AND u.role = 'HEALTH_WORKER' */ select
            u1_0.id,
            u1_0.cell,
            u1_0.created_at,
            u1_0.date_of_birth,
            u1_0.district,
            u1_0.email,
            u1_0.email_verified,
            u1_0.emergency_contact,
            u1_0.facility_id,
            u1_0.gender,
            u1_0.last_login_at,
            u1_0.name,
            u1_0.password_hash,
            u1_0.phone,
            u1_0.phone_verified,
            u1_0.preferred_language,
            u1_0.profile_picture_url,
            u1_0.role,
            u1_0.sector,
            u1_0.status,
            u1_0.updated_at,
            u1_0.version,
            u1_0.village 
        from
            users u1_0 
        where
            u1_0.facility_id=? 
            and u1_0.role='HEALTH_WORKER'
2025-07-14 17:53:13 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 17:53:13 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        a1_0.user_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=?
2025-07-14 17:53:33 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments/available-slots?facilityId=1&date=2025-07-16&healthWorkerId=2
2025-07-14 17:53:33 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:53:33 [http-nio-0.0.0.0-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments/available-slots?facilityId=1&date=2025-07-16&healthWorkerId=2
2025-07-14 17:53:33 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        hf1_0.id,
        hf1_0.address,
        hf1_0.created_at,
        hf1_0.email,
        hf1_0.emergency_contact,
        hf1_0.facility_type,
        hf1_0.is_active,
        hf1_0.latitude,
        hf1_0.longitude,
        hf1_0.name,
        hf1_0.operating_hours,
        hf1_0.phone_number,
        hf1_0.services_offered,
        hf1_0.updated_at,
        hf1_0.version,
        hf1_0.website_url 
    from
        health_facilities hf1_0 
    where
        hf1_0.id=?
2025-07-14 17:53:33 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 17:53:33 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 17:53:33 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* SELECT
        ts 
    FROM
        TimeSlot ts 
    WHERE
        ts.healthFacility = :facility 
        AND ts.healthWorker = :healthWorker 
        AND ts.startTime BETWEEN :startDate AND :endDate 
        AND ts.isAvailable = true */ select
            ts1_0.id,
            ts1_0.created_at,
            ts1_0.current_appointments,
            ts1_0.end_time,
            ts1_0.health_facility_id,
            ts1_0.health_worker_id,
            ts1_0.is_available,
            ts1_0.max_appointments,
            ts1_0.reason,
            ts1_0.start_time,
            ts1_0.updated_at,
            ts1_0.version 
        from
            time_slots ts1_0 
        where
            ts1_0.health_facility_id=? 
            and ts1_0.health_worker_id=? 
            and ts1_0.start_time between ? and ? 
            and ts1_0.is_available=true
2025-07-14 17:53:33 [http-nio-0.0.0.0-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        a1_0.health_facility_id,
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.health_facility_id=?
2025-07-14 17:55:01 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&userId=3
2025-07-14 17:55:01 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:55:01 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&userId=3
2025-07-14 17:55:01 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 17:55:01 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 17:55:01 [http-nio-0.0.0.0-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 17:55:04 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&status=SCHEDULED&userId=3
2025-07-14 17:55:04 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:55:04 [http-nio-0.0.0.0-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&status=SCHEDULED&userId=3
2025-07-14 17:55:04 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 17:55:04 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 17:55:04 [http-nio-0.0.0.0-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 17:55:05 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments?page=0&limit=10&status=CONFIRMED&userId=3
2025-07-14 17:55:05 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:55:05 [http-nio-0.0.0.0-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments?page=0&limit=10&status=CONFIRMED&userId=3
2025-07-14 17:55:05 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.id=?
2025-07-14 17:55:05 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        hr1_0.id,
        ahw1_0.id,
        ahw1_0.cell,
        ahw1_0.created_at,
        ahw1_0.date_of_birth,
        ahw1_0.district,
        ahw1_0.email,
        ahw1_0.email_verified,
        ahw1_0.emergency_contact,
        ahw1_0.facility_id,
        ahw1_0.gender,
        ahw1_0.last_login_at,
        ahw1_0.name,
        ahw1_0.password_hash,
        ahw1_0.phone,
        ahw1_0.phone_verified,
        ahw1_0.preferred_language,
        ahw1_0.profile_picture_url,
        ahw1_0.role,
        ahw1_0.sector,
        ahw1_0.status,
        ahw1_0.updated_at,
        ahw1_0.version,
        ahw1_0.village,
        hr1_0.bmi,
        hr1_0.bp_unit,
        hr1_0.bp_value,
        hr1_0.created_at,
        hr1_0.health_status,
        hr1_0.heart_rate_unit,
        hr1_0.heart_rate_value,
        hr1_0.height_unit,
        hr1_0.height_value,
        hr1_0.is_verified,
        hr1_0.kg_unit,
        hr1_0.kg_value,
        hr1_0.last_updated,
        hr1_0.notes,
        hr1_0.recorded_by,
        hr1_0.temp_unit,
        hr1_0.temp_value,
        hr1_0.updated_at,
        hr1_0.user_id,
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village,
        hr1_0.version 
    from
        health_records hr1_0 
    left join
        users ahw1_0 
            on ahw1_0.id=hr1_0.assigned_health_worker_id 
    join
        users u1_0 
            on u1_0.id=hr1_0.user_id 
    where
        hr1_0.user_id=?
2025-07-14 17:55:05 [http-nio-0.0.0.0-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    where
        a1_0.user_id=? 
    order by
        a1_0.scheduled_date desc
2025-07-14 17:55:33 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /contraception/active
2025-07-14 17:55:33 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:55:33 [http-nio-0.0.0.0-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /contraception/active
2025-07-14 17:55:33 [http-nio-0.0.0.0-8080-exec-10] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValueInternal(RequestParamMethodArgumentResolver.java:220)
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValue(RequestParamMethodArgumentResolver.java:196)
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:126)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:224)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-14 17:56:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /education/lessons?page=0&limit=20
2025-07-14 17:56:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:56:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /education/lessons?page=0&limit=20
2025-07-14 17:56:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0 
    where
        el1_0.is_published 
    order by
        el1_0.order_index
2025-07-14 17:56:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        t1_0.lesson_id,
        t1_0.tag 
    from
        lesson_tags t1_0 
    where
        t1_0.lesson_id=?
2025-07-14 17:56:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        iu1_0.lesson_id,
        iu1_0.image_url 
    from
        lesson_images iu1_0 
    where
        iu1_0.lesson_id=?
2025-07-14 17:56:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        t1_0.lesson_id,
        t1_0.tag 
    from
        lesson_tags t1_0 
    where
        t1_0.lesson_id=?
2025-07-14 17:56:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        iu1_0.lesson_id,
        iu1_0.image_url 
    from
        lesson_images iu1_0 
    where
        iu1_0.lesson_id=?
2025-07-14 17:56:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        t1_0.lesson_id,
        t1_0.tag 
    from
        lesson_tags t1_0 
    where
        t1_0.lesson_id=?
2025-07-14 17:56:31 [http-nio-0.0.0.0-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        iu1_0.lesson_id,
        iu1_0.image_url 
    from
        lesson_images iu1_0 
    where
        iu1_0.lesson_id=?
2025-07-14 17:56:32 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /education/progress/3
2025-07-14 17:56:32 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:56:32 [http-nio-0.0.0.0-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /education/progress/3
2025-07-14 17:56:37 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /contraception/active
2025-07-14 17:56:37 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 17:56:37 [http-nio-0.0.0.0-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /contraception/active
2025-07-14 17:56:37 [http-nio-0.0.0.0-8080-exec-4] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValueInternal(RequestParamMethodArgumentResolver.java:220)
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValue(RequestParamMethodArgumentResolver.java:196)
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:126)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:224)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-14 18:11:44 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-14 18:11:44 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 18:11:44 [http-nio-0.0.0.0-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-14 18:11:56 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /community/overview
2025-07-14 18:11:56 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-14 18:11:56 [http-nio-0.0.0.0-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /community/overview
2025-07-14 18:11:56 [http-nio-0.0.0.0-8080-exec-6] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource community/overview.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-14 18:12:41 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-14 18:12:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-14 18:12:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-14 18:12:50 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 20912 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-14 18:12:50 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-14 18:12:50 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-14 18:12:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-14 18:12:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 96 ms. Found 23 JPA repository interfaces.
2025-07-14 18:12:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-14 18:12:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-14 18:12:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-14 18:12:54 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-14 18:12:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3567 ms
2025-07-14 18:12:54 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-14 18:12:54 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-14 18:12:54 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-14 18:12:55 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-14 18:12:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-14 18:12:55 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3211cc84
2025-07-14 18:12:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-14 18:12:55 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-14 18:12:57 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-14 18:12:57 [main] DEBUG org.hibernate.SQL - 
    create table community_events (
        id bigserial not null,
        contact_info varchar(255),
        created_at timestamp(6) not null,
        current_participants integer not null,
        description TEXT,
        end_date timestamp(6),
        event_date timestamp(6) not null,
        is_active boolean not null,
        is_cancelled boolean not null,
        is_virtual boolean not null,
        location varchar(255) not null,
        max_participants integer,
        registration_deadline timestamp(6),
        registration_required boolean not null,
        title varchar(255) not null,
        type varchar(255) not null,
        updated_at timestamp(6) not null,
        virtual_link varchar(255),
        organizer_id bigint not null,
        primary key (id)
    )
2025-07-14 18:12:57 [main] DEBUG org.hibernate.SQL - 
    create table forum_topic_tags (
        topic_id bigint not null,
        tag varchar(255)
    )
2025-07-14 18:12:57 [main] DEBUG org.hibernate.SQL - 
    create table forum_topics (
        id bigserial not null,
        category varchar(255) not null,
        content TEXT not null,
        created_at timestamp(6) not null,
        is_active boolean not null,
        is_locked boolean not null,
        is_pinned boolean not null,
        last_activity_at timestamp(6),
        reply_count integer not null,
        title varchar(255) not null,
        updated_at timestamp(6) not null,
        view_count integer not null,
        author_id bigint not null,
        last_reply_by bigint,
        primary key (id)
    )
2025-07-14 18:12:57 [main] DEBUG org.hibernate.SQL - 
    create table support_group_members (
        id bigserial not null,
        is_active boolean not null,
        joined_at timestamp(6) not null,
        last_activity_at timestamp(6),
        role varchar(255) not null,
        group_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-14 18:12:57 [main] DEBUG org.hibernate.SQL - 
    create table support_group_tags (
        group_id bigint not null,
        tag varchar(255)
    )
2025-07-14 18:12:57 [main] DEBUG org.hibernate.SQL - 
    create table support_groups (
        id bigserial not null,
        category varchar(255) not null,
        contact_info varchar(255),
        created_at timestamp(6) not null,
        description TEXT,
        is_active boolean not null,
        is_private boolean not null,
        max_members integer,
        meeting_location varchar(255),
        meeting_schedule varchar(255),
        member_count integer not null,
        name varchar(255) not null,
        updated_at timestamp(6) not null,
        creator_id bigint not null,
        primary key (id)
    )
2025-07-14 18:12:57 [main] DEBUG org.hibernate.SQL - 
    alter table if exists community_events 
       add constraint FKkeq3avrlvone34ttma8ve1toy 
       foreign key (organizer_id) 
       references users
2025-07-14 18:12:58 [main] DEBUG org.hibernate.SQL - 
    alter table if exists forum_topic_tags 
       add constraint FKmfqxk3ejuqfcs28a5vr2upwj0 
       foreign key (topic_id) 
       references forum_topics
2025-07-14 18:12:58 [main] DEBUG org.hibernate.SQL - 
    alter table if exists forum_topics 
       add constraint FKtta5vol30lrio44tjjqsbnyi 
       foreign key (author_id) 
       references users
2025-07-14 18:12:58 [main] DEBUG org.hibernate.SQL - 
    alter table if exists forum_topics 
       add constraint FKf8xvmhxkmfnyby8fw6d5kls8c 
       foreign key (last_reply_by) 
       references users
2025-07-14 18:12:58 [main] DEBUG org.hibernate.SQL - 
    alter table if exists support_group_members 
       add constraint FK3n3o0dj3yntb0f26fb5ndfiuc 
       foreign key (group_id) 
       references support_groups
2025-07-14 18:12:58 [main] DEBUG org.hibernate.SQL - 
    alter table if exists support_group_members 
       add constraint FK148iw3kg18jhp9i6qjw9ntlsd 
       foreign key (user_id) 
       references users
2025-07-14 18:12:58 [main] DEBUG org.hibernate.SQL - 
    alter table if exists support_group_tags 
       add constraint FKhrlhe7lhhp7l0v66lguv3eir6 
       foreign key (group_id) 
       references support_groups
2025-07-14 18:12:58 [main] DEBUG org.hibernate.SQL - 
    alter table if exists support_groups 
       add constraint FK3iq5kndvo5m57q9gltcc49e1p 
       foreign key (creator_id) 
       references users
2025-07-14 18:12:58 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-14 18:12:58 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-14 18:13:01 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-14 18:13:01 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 5f47ca7d-455a-4fbe-84b5-210f6432fe94

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-14 18:13:02 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-14 18:13:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2f38d100, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@51a27faa, org.springframework.security.web.context.SecurityContextHolderFilter@629680ec, org.springframework.security.web.header.HeaderWriterFilter@86ecc81, org.springframework.web.filter.CorsFilter@15ad83a5, org.springframework.security.web.authentication.logout.LogoutFilter@1d226f03, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4bfcb524, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3592cfcb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6e7bd826, org.springframework.security.web.session.SessionManagementFilter@268f1c26, org.springframework.security.web.access.ExceptionTranslationFilter@73492bc7, org.springframework.security.web.access.intercept.AuthorizationFilter@30d379ea]
2025-07-14 18:13:03 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-14 18:13:03 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 13.359 seconds (process running for 14.299)
2025-07-14 18:13:03 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-14 18:13:03 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 18:13:03 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 18:13:03 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-14 18:13:03 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-14 18:13:03 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0
2025-07-14 18:13:03 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-14 18:13:05 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 18:13:05 [RMI TCP Connection(1)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-14 18:13:05 [RMI TCP Connection(1)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
