2025-07-12 00:46:53 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 18772 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-12 00:46:53 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-12 00:46:53 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-12 00:46:57 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 00:46:57 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 211 ms. Found 19 JPA repository interfaces.
2025-07-12 00:46:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8080 (http)
2025-07-12 00:46:59 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 00:46:59 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-12 00:46:59 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-12 00:46:59 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5950 ms
2025-07-12 00:47:00 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 00:47:00 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-12 00:47:00 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-12 00:47:00 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 00:47:00 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-12 00:47:01 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@30c03473
2025-07-12 00:47:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-12 00:47:01 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 00:47:04 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 00:47:04 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 00:47:05 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-12 00:47:08 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-12 00:47:08 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 92fb5598-9667-4963-8129-93c18e7492a8

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-12 00:47:09 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-12 00:47:09 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@68f027a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4ed2dced, org.springframework.security.web.context.SecurityContextHolderFilter@2fde59bd, org.springframework.security.web.header.HeaderWriterFilter@3b3a4f9c, org.springframework.web.filter.CorsFilter@7a9fb1a4, org.springframework.security.web.authentication.logout.LogoutFilter@4578b8cb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7e0d4ba1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@383cf547, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@43ac1f2, org.springframework.security.web.session.SessionManagementFilter@4551aa4, org.springframework.security.web.access.ExceptionTranslationFilter@73cacff3, org.springframework.security.web.access.intercept.AuthorizationFilter@23c54fbb]
2025-07-12 00:47:11 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-12 00:47:11 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 18.918 seconds (process running for 21.848)
2025-07-12 00:47:11 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-12 00:47:11 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-12 00:47:11 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-12 00:47:11 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-12 00:47:11 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0
2025-07-12 00:47:11 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-12 00:47:11 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-12 01:40:45 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 01:40:45 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-12 01:40:45 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-12 01:40:55 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 14632 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-12 01:40:55 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-12 01:40:55 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-12 01:40:57 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-12 01:40:57 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 161 ms. Found 19 JPA repository interfaces.
2025-07-12 01:40:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-12 01:40:59 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 01:40:59 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-12 01:40:59 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-12 01:40:59 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3382 ms
2025-07-12 01:40:59 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-12 01:40:59 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-12 01:41:00 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-12 01:41:00 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-12 01:41:00 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-12 01:41:00 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2a5efbb9
2025-07-12 01:41:00 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-12 01:41:00 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-12 01:41:03 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-12 01:41:03 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 01:41:04 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-12 01:41:06 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-12 01:41:06 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 1814dfd4-c6b6-4694-bc55-905514b305bb

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-12 01:41:07 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-12 01:41:07 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@641ccc76, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7b6c8584, org.springframework.security.web.context.SecurityContextHolderFilter@f2ba7f9, org.springframework.security.web.header.HeaderWriterFilter@76b5c127, org.springframework.web.filter.CorsFilter@49f079ee, org.springframework.security.web.authentication.logout.LogoutFilter@5a10c302, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7a5ca80d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@44e88c1c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@62ac24b5, org.springframework.security.web.session.SessionManagementFilter@706b8129, org.springframework.security.web.access.ExceptionTranslationFilter@409140b2, org.springframework.security.web.access.intercept.AuthorizationFilter@4c59f5e8]
2025-07-12 01:41:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-12 01:41:08 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 13.435 seconds (process running for 14.563)
2025-07-12 01:41:08 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-12 01:41:08 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-12 01:41:08 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-12 01:41:08 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-12 01:41:08 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        el1_0.id,
        el1_0.audio_url,
        el1_0.author,
        el1_0.category,
        el1_0.content,
        el1_0.created_at,
        el1_0.description,
        el1_0.duration_minutes,
        el1_0.is_published,
        el1_0.language,
        el1_0.level,
        el1_0.order_index,
        el1_0.title,
        el1_0.updated_at,
        el1_0.version,
        el1_0.video_url,
        el1_0.view_count 
    from
        education_lessons el1_0
2025-07-12 01:41:08 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-12 01:41:08 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-12 01:41:09 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 01:41:09 [RMI TCP Connection(5)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 01:41:09 [RMI TCP Connection(5)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-12 01:45:45 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-12 01:45:45 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-12 01:45:45 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
