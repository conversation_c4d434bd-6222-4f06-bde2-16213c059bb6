import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/family_planning_service.dart';
import '../../widgets/voice_button.dart';

enum ReminderType {
  medication,
  appointment,
  cycleTracking,
  healthCheck,
  contraceptionRefill,
  partnerCommunication,
}

extension ReminderTypeExtension on ReminderType {
  String get label {
    switch (this) {
      case ReminderType.medication:
        return 'Imiti';
      case ReminderType.appointment:
        return 'Gahunda';
      case ReminderType.cycleTracking:
        return '<PERSON><PERSON><PERSON><PERSON><PERSON>';
      case ReminderType.healthCheck:
        return 'Gusuzuma Ubuzima';
      case ReminderType.contraceptionRefill:
        return 'Gusimbura Imiti';
      case ReminderType.partnerCommunication:
        return 'Itumanaho n\'Umukunzi';
    }
  }

  IconData get icon {
    switch (this) {
      case ReminderType.medication:
        return Icons.medication_rounded;
      case ReminderType.appointment:
        return Icons.event_rounded;
      case ReminderType.cycleTracking:
        return Icons.calendar_month_rounded;
      case ReminderType.healthCheck:
        return Icons.health_and_safety_rounded;
      case ReminderType.contraceptionRefill:
        return Icons.refresh_rounded;
      case ReminderType.partnerCommunication:
        return Icons.chat_rounded;
    }
  }

  Color get color {
    switch (this) {
      case ReminderType.medication:
        return Colors.blue.shade400;
      case ReminderType.appointment:
        return Colors.green.shade400;
      case ReminderType.cycleTracking:
        return Colors.pink.shade400;
      case ReminderType.healthCheck:
        return Colors.orange.shade400;
      case ReminderType.contraceptionRefill:
        return Colors.purple.shade400;
      case ReminderType.partnerCommunication:
        return Colors.indigo.shade400;
    }
  }
}

class ComprehensiveReminderSystem extends StatefulWidget {
  const ComprehensiveReminderSystem({super.key});

  @override
  State<ComprehensiveReminderSystem> createState() =>
      _ComprehensiveReminderSystemState();
}

class _ComprehensiveReminderSystemState
    extends State<ComprehensiveReminderSystem>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final FamilyPlanningService _familyPlanningService = FamilyPlanningService();

  List<Map<String, dynamic>> _activeReminders = [];
  List<Map<String, dynamic>> _upcomingReminders = [];
  List<Map<String, dynamic>> _reminderHistory = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReminderData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReminderData() async {
    setState(() => _isLoading = true);

    try {
      // Simulate loading reminder data
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _activeReminders = [
          {
            'id': '1',
            'title': 'Ibiyobyabwenge',
            'description': 'Nywa ibiyobyabwenge bya buri munsi',
            'type': ReminderType.medication,
            'time': '08:00',
            'isActive': true,
            'nextReminder': DateTime.now().add(const Duration(hours: 2)),
          },
          {
            'id': '2',
            'title': 'Gahunda y\'ubuvuzi',
            'description': 'Gusura muganga ku cyumweru gitaha',
            'type': ReminderType.appointment,
            'time': '14:00',
            'isActive': true,
            'nextReminder': DateTime.now().add(const Duration(days: 3)),
          },
        ];

        _upcomingReminders = [
          {
            'id': '3',
            'title': 'Gukurikirana Imihango',
            'description': 'Kwandika imihango yawe',
            'type': ReminderType.cycleTracking,
            'scheduledDate': DateTime.now().add(const Duration(days: 1)),
          },
        ];

        _reminderHistory = [
          {
            'id': '4',
            'title': 'Gusuzuma Ubuzima',
            'description': 'Gusuzuma ubuzima rusange',
            'type': ReminderType.healthCheck,
            'completedDate': DateTime.now().subtract(const Duration(days: 2)),
            'status': 'completed',
          },
        ];

        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Habaye ikosa mu gufata amakuru: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Ibyibutsa n\'Amakuru'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          VoiceButton(
            heroTag: 'reminder_voice',
            prompt: 'Ibyibutsa',
            onResult: (result) {
              debugPrint('Voice result: $result');
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh_rounded),
            onPressed: _loadReminderData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.notifications_active_rounded), text: 'Bikora'),
            Tab(icon: Icon(Icons.schedule_rounded), text: 'Bitegereje'),
            Tab(icon: Icon(Icons.history_rounded), text: 'Amateka'),
          ],
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                controller: _tabController,
                children: [
                  _buildActiveRemindersTab(isTablet),
                  _buildUpcomingRemindersTab(isTablet),
                  _buildHistoryTab(isTablet),
                ],
              ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddReminderDialog(),
        backgroundColor: AppTheme.primaryColor,
        icon: const Icon(Icons.add_alert_rounded, color: Colors.white),
        label: const Text(
          'Ongeraho Ikibutsa',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildActiveRemindersTab(bool isTablet) {
    return RefreshIndicator(
      onRefresh: _loadReminderData,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeCard(isTablet),
            SizedBox(height: AppTheme.spacing24),
            _buildQuickStatsCard(isTablet),
            SizedBox(height: AppTheme.spacing24),
            _buildActiveRemindersSection(isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.primaryColor.withValues(alpha: 0.1), Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.notifications_active_rounded,
                color: AppTheme.primaryColor,
                size: isTablet ? 32 : 28,
              ),
              SizedBox(width: AppTheme.spacing12),
              Expanded(
                child: Text(
                  'Ibyibutsa n\'Amakuru',
                  style: AppTheme.headingMedium.copyWith(
                    fontSize: isTablet ? 20 : 18,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppTheme.spacing12),
          Text(
            'Dufasha mu kwibuka ibikorwa by\'ingenzi by\'ubwiyunge bw\'umuryango.',
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.black87,
              fontSize: isTablet ? 14 : 13,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms);
  }

  Widget _buildQuickStatsCard(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              'Bikora',
              '${_activeReminders.length}',
              Icons.notifications_active_rounded,
              AppTheme.successColor,
              isTablet,
            ),
          ),
          Container(width: 1, height: 40, color: Colors.grey.shade300),
          Expanded(
            child: _buildStatItem(
              'Bitegereje',
              '${_upcomingReminders.length}',
              Icons.schedule_rounded,
              AppTheme.warningColor,
              isTablet,
            ),
          ),
          Container(width: 1, height: 40, color: Colors.grey.shade300),
          Expanded(
            child: _buildStatItem(
              'Byarangiye',
              '${_reminderHistory.length}',
              Icons.check_circle_rounded,
              AppTheme.infoColor,
              isTablet,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 800.ms, delay: 200.ms);
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
    bool isTablet,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: isTablet ? 28 : 24),
        SizedBox(height: AppTheme.spacing8),
        Text(
          value,
          style: AppTheme.headingLarge.copyWith(
            color: color,
            fontSize: isTablet ? 20 : 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTheme.bodySmall.copyWith(
            color: Colors.black54,
            fontSize: isTablet ? 12 : 11,
          ),
        ),
      ],
    );
  }

  Widget _buildActiveRemindersSection(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Ibyibutsa Bikora',
              style: AppTheme.headingMedium.copyWith(
                fontSize: isTablet ? 20 : 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton.icon(
              onPressed: () => _showAddReminderDialog(),
              icon: const Icon(Icons.add_rounded),
              label: const Text('Ongeraho'),
            ),
          ],
        ),
        SizedBox(height: AppTheme.spacing16),
        if (_activeReminders.isEmpty)
          _buildEmptyRemindersCard(isTablet)
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _activeReminders.length,
            itemBuilder: (context, index) {
              final reminder = _activeReminders[index];
              return _buildReminderCard(reminder, isTablet, index);
            },
          ),
      ],
    ).animate().fadeIn(duration: 1000.ms, delay: 400.ms);
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }

  void _showAddReminderDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Ongeraho Ikibutsa'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Hitamo ubwoko bw\'ikibutsa:'),
                  const SizedBox(height: 16),
                  ...ReminderType.values
                      .map(
                        (type) => ListTile(
                          leading: Icon(type.icon, color: type.color),
                          title: Text(type.label),
                          onTap: () => _addReminder(type),
                        ),
                      )
                      .toList(),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Siga'),
              ),
            ],
          ),
    );
  }

  void _addReminder(ReminderType type) {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Ikibutsa cy\'${type.label} cyongerewe!'),
        backgroundColor: AppTheme.successColor,
      ),
    );
    _loadReminderData();
  }

  void _toggleReminder(String id, bool isActive) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isActive ? 'Ikibutsa cyakorowe' : 'Ikibutsa cyahagaritswe',
        ),
        backgroundColor:
            isActive ? AppTheme.successColor : AppTheme.warningColor,
      ),
    );
  }

  void _editReminder(Map<String, dynamic> reminder) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Guhindura ikibutsa: ${reminder['title']}'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _markAsCompleted(Map<String, dynamic> reminder) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Ikibutsa cyarangiye: ${reminder['title']}'),
        backgroundColor: AppTheme.successColor,
      ),
    );
    _loadReminderData();
  }

  Widget _buildHistoryCard(
    Map<String, dynamic> reminder,
    bool isTablet,
    int index,
  ) {
    final title = reminder['title'] ?? '';
    final description = reminder['description'] ?? '';
    final type = reminder['type'] as ReminderType;
    final completedDate = reminder['completedDate'] as DateTime?;

    return Container(
          margin: EdgeInsets.only(bottom: AppTheme.spacing12),
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(AppTheme.spacing8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                ),
                child: Icon(
                  type.icon,
                  color: Colors.grey.shade600,
                  size: isTablet ? 20 : 18,
                ),
              ),
              SizedBox(width: AppTheme.spacing12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTheme.bodyLarge.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: isTablet ? 16 : 14,
                      ),
                    ),
                    Text(
                      description,
                      style: AppTheme.bodyMedium.copyWith(
                        color: Colors.black54,
                        fontSize: isTablet ? 13 : 12,
                      ),
                    ),
                    if (completedDate != null)
                      Text(
                        'Byarangiye: ${_formatDate(completedDate)}',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.successColor,
                          fontSize: isTablet ? 12 : 11,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.check_circle_rounded,
                color: AppTheme.successColor,
                size: isTablet ? 24 : 20,
              ),
            ],
          ),
        )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .slideX(begin: 0.3);
  }

  // Missing methods for remaining tabs
  Widget _buildUpcomingRemindersTab(bool isTablet) {
    return Center(
      child: Text(
        'Ibyibutsa Bitegereje - Hazongera',
        style: AppTheme.headingMedium,
      ),
    );
  }

  Widget _buildHistoryTab(bool isTablet) {
    return Center(
      child: Text(
        'Amateka y\'Ibyibutsa - Hazongera',
        style: AppTheme.headingMedium,
      ),
    );
  }

  Widget _buildReminderCard(
    Map<String, dynamic> reminder,
    bool isTablet,
    int index,
  ) {
    final title = reminder['title'] ?? '';
    final type = reminder['type'] as ReminderType;

    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacing12),
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Icon(type.icon, color: type.color),
        title: Text(title),
        subtitle: Text(type.label),
        trailing: Switch(
          value: true,
          onChanged: (value) => _toggleReminder(reminder['id'], value),
        ),
      ),
    );
  }

  Widget _buildUpcomingReminderCard(
    Map<String, dynamic> reminder,
    bool isTablet,
    int index,
  ) {
    return _buildReminderCard(reminder, isTablet, index);
  }

  Widget _buildEmptyRemindersCard(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing24),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.notifications_off_outlined,
              size: isTablet ? 64 : 48,
              color: Colors.grey.shade400,
            ),
            SizedBox(height: AppTheme.spacing16),
            Text(
              'Nta byibutsa bikora',
              style: AppTheme.headingSmall.copyWith(
                color: Colors.black54,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppTheme.spacing8),
            Text(
              'Ongeraho ibyibutsa kugira ngo utibagirwe ibikorwa by\'ingenzi.',
              style: AppTheme.bodyMedium.copyWith(color: Colors.black54),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppTheme.spacing16),
            ElevatedButton.icon(
              onPressed: () => _showAddReminderDialog(),
              icon: const Icon(Icons.add_alert_rounded),
              label: const Text('Ongeraho Ikibutsa'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
