// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contraception_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ContraceptionHistory _$ContraceptionHistoryFromJson(
  Map<String, dynamic> json,
) => ContraceptionHistory(
  id: json['id'] as String,
  userId: json['userId'] as String,
  methodType: $enumDecode(_$ContraceptionTypeEnumMap, json['methodType']),
  startDate: DateTime.parse(json['startDate'] as String),
  endDate:
      json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
  reasonForStopping: json['reasonForStopping'] as String?,
  sideEffects: json['sideEffects'] as String?,
  satisfactionRating: (json['satisfactionRating'] as num?)?.toInt(),
  notes: json['notes'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt:
      json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$ContraceptionHistoryToJson(
  ContraceptionHistory instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'methodType': _$ContraceptionTypeEnumMap[instance.methodType]!,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate?.toIso8601String(),
  'reasonForStopping': instance.reasonForStopping,
  'sideEffects': instance.sideEffects,
  'satisfactionRating': instance.satisfactionRating,
  'notes': instance.notes,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

const _$ContraceptionTypeEnumMap = {
  ContraceptionType.pill: 'pill',
  ContraceptionType.iud: 'iud',
  ContraceptionType.implant: 'implant',
  ContraceptionType.injection: 'injection',
  ContraceptionType.patch: 'patch',
  ContraceptionType.ring: 'ring',
  ContraceptionType.condom: 'condom',
  ContraceptionType.diaphragm: 'diaphragm',
  ContraceptionType.spermicide: 'spermicide',
  ContraceptionType.naturalFamilyPlanning: 'naturalFamilyPlanning',
  ContraceptionType.sterilization: 'sterilization',
  ContraceptionType.emergency: 'emergency',
};
