enum PregnancyPlanStatus {
  planning,
  trying,
  conceived,
  postponed,
  cancelled,
}

extension PregnancyPlanStatusExtension on PregnancyPlanStatus {
  String get label {
    switch (this) {
      case PregnancyPlanStatus.planning:
        return '<PERSON><PERSON><PERSON><PERSON>';
      case PregnancyPlanStatus.trying:
        return '<PERSON><PERSON>rageza';
      case PregnancyPlanStatus.conceived:
        return 'Gusanga';
      case PregnancyPlanStatus.postponed:
        return 'Guhinduranya';
      case PregnancyPlanStatus.cancelled:
        return 'Guhaga<PERSON>';
    }
  }
}
