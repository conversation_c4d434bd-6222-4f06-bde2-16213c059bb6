// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'menstrual_cycle_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MenstrualCycle _$MenstrualCycleFromJson(Map<String, dynamic> json) =>
    MenstrualCycle(
      id: json['id'] as String,
      userId: json['userId'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate:
          json['endDate'] == null
              ? null
              : DateTime.parse(json['endDate'] as String),
      cycleLength: (json['cycleLength'] as num?)?.toInt(),
      flowIntensity: (json['flowIntensity'] as num?)?.toInt(),
      symptoms:
          (json['symptoms'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt:
          json['updatedAt'] == null
              ? null
              : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$MenstrualCycleToJson(MenstrualCycle instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'cycleLength': instance.cycleLength,
      'flowIntensity': instance.flowIntensity,
      'symptoms': instance.symptoms,
      'notes': instance.notes,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
