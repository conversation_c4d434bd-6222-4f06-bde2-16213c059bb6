import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_theme.dart';

enum SuccessType {
  create,
  update,
  delete,
  save,
  sync,
  complete,
  reminder,
  appointment,
}

class AppSuccess {
  final SuccessType type;
  final String title;
  final String message;
  final DateTime timestamp;

  AppSuccess({
    required this.type,
    required this.title,
    required this.message,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory AppSuccess.create(String item) {
    return AppSuccess(
      type: SuccessType.create,
      title: 'Byakoze!',
      message: '$item byakoze neza.',
    );
  }

  factory AppSuccess.update(String item) {
    return AppSuccess(
      type: SuccessType.update,
      title: 'Byahinduwe!',
      message: '$item byahinduwe neza.',
    );
  }

  factory AppSuccess.delete(String item) {
    return AppSuccess(
      type: SuccessType.delete,
      title: 'Byasibwe!',
      message: '$item byasibwe neza.',
    );
  }

  factory AppSuccess.save(String item) {
    return AppSuccess(
      type: SuccessType.save,
      title: 'Byabitswe!',
      message: '$item byabitswe neza.',
    );
  }

  factory AppSuccess.sync() {
    return AppSuccess(
      type: SuccessType.sync,
      title: 'Byahurijwe!',
      message: 'Amakuru yawe yahurijwe neza.',
    );
  }

  factory AppSuccess.complete(String task) {
    return AppSuccess(
      type: SuccessType.complete,
      title: 'Byarangiye!',
      message: '$task byarangiye neza.',
    );
  }

  factory AppSuccess.reminder() {
    return AppSuccess(
      type: SuccessType.reminder,
      title: 'Ikibutsa Cyashyizweho!',
      message: 'Ikibutsa cyawe cyashyizweho neza.',
    );
  }

  factory AppSuccess.appointment() {
    return AppSuccess(
      type: SuccessType.appointment,
      title: 'Gahunda Yashyizweho!',
      message: 'Gahunda yawe yashyizweho neza.',
    );
  }

  Color get color {
    switch (type) {
      case SuccessType.create:
        return AppTheme.successColor;
      case SuccessType.update:
        return AppTheme.infoColor;
      case SuccessType.delete:
        return AppTheme.warningColor;
      case SuccessType.save:
        return AppTheme.primaryColor;
      case SuccessType.sync:
        return Colors.blue.shade600;
      case SuccessType.complete:
        return Colors.green.shade600;
      case SuccessType.reminder:
        return Colors.purple.shade600;
      case SuccessType.appointment:
        return Colors.orange.shade600;
    }
  }

  IconData get icon {
    switch (type) {
      case SuccessType.create:
        return Icons.add_circle_rounded;
      case SuccessType.update:
        return Icons.edit_rounded;
      case SuccessType.delete:
        return Icons.delete_rounded;
      case SuccessType.save:
        return Icons.save_rounded;
      case SuccessType.sync:
        return Icons.sync_rounded;
      case SuccessType.complete:
        return Icons.check_circle_rounded;
      case SuccessType.reminder:
        return Icons.notifications_rounded;
      case SuccessType.appointment:
        return Icons.event_rounded;
    }
  }
}

class SuccessHandler {
  static void showSnackBar(BuildContext context, AppSuccess success) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(success.icon, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    success.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    success.message,
                    style: const TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: success.color,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        ),
      ),
    );
  }

  static void showSuccessDialog(BuildContext context, AppSuccess success) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(success.icon, color: success.color, size: 28),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    success.title,
                    style: TextStyle(
                      color: success.color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            content: Text(success.message),
            actions: [
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: success.color,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Sawa'),
              ),
            ],
          ),
    );
  }

  static Widget buildSuccessWidget(
    AppSuccess success, {
    VoidCallback? onContinue,
    String? continueText,
  }) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: success.color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(success.icon, size: 64, color: success.color),
          ).animate().scale(duration: 600.ms, curve: Curves.elasticOut),
          const SizedBox(height: 24),
          Text(
            success.title,
            style: AppTheme.headingLarge.copyWith(
              color: success.color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ).animate(delay: 200.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3),
          const SizedBox(height: 12),
          Text(
            success.message,
            style: AppTheme.bodyLarge.copyWith(color: Colors.black87),
            textAlign: TextAlign.center,
          ).animate(delay: 400.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3),
          if (onContinue != null) ...[
            const SizedBox(height: 32),
            ElevatedButton.icon(
                  onPressed: onContinue,
                  icon: const Icon(Icons.arrow_forward_rounded),
                  label: Text(continueText ?? 'Komeza'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: success.color,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                )
                .animate(delay: 600.ms)
                .fadeIn(duration: 600.ms)
                .scale(begin: const Offset(0.8, 0.8)),
          ],
        ],
      ),
    );
  }

  static void showBottomSheet(BuildContext context, AppSuccess success) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),
                Icon(success.icon, size: 48, color: success.color),
                const SizedBox(height: 16),
                Text(
                  success.title,
                  style: AppTheme.headingMedium.copyWith(
                    color: success.color,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  success.message,
                  style: AppTheme.bodyMedium.copyWith(color: Colors.black87),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: success.color,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Sawa'),
                  ),
                ),
              ],
            ),
          ).animate().slideY(
            begin: 1.0,
            duration: 300.ms,
            curve: Curves.easeOut,
          ),
    );
  }

  static Future<void> showWithDelay(
    BuildContext context,
    AppSuccess success, {
    Duration delay = const Duration(milliseconds: 500),
    bool useSnackBar = true,
  }) async {
    await Future.delayed(delay);

    if (context.mounted) {
      if (useSnackBar) {
        showSnackBar(context, success);
      } else {
        showSuccessDialog(context, success);
      }
    }
  }

  static void logSuccess(AppSuccess success) {
    debugPrint('=== SUCCESS LOG ===');
    debugPrint('Type: ${success.type}');
    debugPrint('Title: ${success.title}');
    debugPrint('Message: ${success.message}');
    debugPrint('Timestamp: ${success.timestamp}');
    debugPrint('==================');
  }
}
