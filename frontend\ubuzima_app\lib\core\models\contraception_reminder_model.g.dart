// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contraception_reminder_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ContraceptionReminder _$ContraceptionReminderFromJson(
  Map<String, dynamic> json,
) => ContraceptionReminder(
  id: json['id'] as String,
  userId: json['userId'] as String,
  title: json['title'] as String,
  description: json['description'] as String?,
  type: $enumDecode(_$ReminderTypeEnumMap, json['type']),
  reminderTime: DateTime.parse(json['reminderTime'] as String),
  isActive: json['isActive'] as bool,
  isRepeating: json['isRepeating'] as bool,
  repeatInterval: json['repeatInterval'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt:
      json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$ContraceptionReminderToJson(
  ContraceptionReminder instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'title': instance.title,
  'description': instance.description,
  'type': _$ReminderTypeEnumMap[instance.type]!,
  'reminderTime': instance.reminderTime.toIso8601String(),
  'isActive': instance.isActive,
  'isRepeating': instance.isRepeating,
  'repeatInterval': instance.repeatInterval,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

const _$ReminderTypeEnumMap = {
  ReminderType.dailyPill: 'dailyPill',
  ReminderType.weeklyPatch: 'weeklyPatch',
  ReminderType.monthlyRing: 'monthlyRing',
  ReminderType.quarterlyInjection: 'quarterlyInjection',
  ReminderType.appointment: 'appointment',
  ReminderType.refill: 'refill',
  ReminderType.sideEffectCheck: 'sideEffectCheck',
};
