import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/family_planning_service.dart';
import '../../core/models/menstrual_cycle_model.dart';

class CycleTrackingForm extends StatefulWidget {
  final Function(MenstrualCycle) onCycleSaved;

  const CycleTrackingForm({
    super.key,
    required this.onCycleSaved,
  });

  @override
  State<CycleTrackingForm> createState() => _CycleTrackingFormState();
}

class _CycleTrackingFormState extends State<CycleTrackingForm> {
  final _formKey = GlobalKey<FormState>();
  final FamilyPlanningService _familyPlanningService = FamilyPlanningService();
  
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  int _flowIntensity = 2;
  List<String> _selectedSymptoms = [];
  final TextEditingController _notesController = TextEditingController();
  bool _isLoading = false;

  final List<String> _availableSymptoms = [
    'Ububabare bw\'inda',
    '<PERSON>rag<PERSON> ubwoba',
    '<PERSON>uh<PERSON><PERSON> kw\'amarangamutima',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON> kw\'amabere',
    'Kuraguza ubwoba',
    'Guhinduka kw\'ibiryo',
    'Kubabara kw\'umugongo',
  ];

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
      ),
      child: Container(
        padding: EdgeInsets.all(AppTheme.spacing20),
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Andika Imihango',
                style: AppTheme.headingMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: AppTheme.spacing20),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDateSection(),
                      SizedBox(height: AppTheme.spacing20),
                      _buildFlowIntensitySection(),
                      SizedBox(height: AppTheme.spacing20),
                      _buildSymptomsSection(),
                      SizedBox(height: AppTheme.spacing20),
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
              SizedBox(height: AppTheme.spacing20),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Itariki',
          style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppTheme.spacing12),
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                'Itariki yo gutangira',
                _startDate,
                (date) => setState(() => _startDate = date),
              ),
            ),
            SizedBox(width: AppTheme.spacing16),
            Expanded(
              child: _buildDateField(
                'Itariki yo guhagarika',
                _endDate,
                (date) => setState(() => _endDate = date),
                isOptional: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField(
    String label,
    DateTime? date,
    Function(DateTime) onDateSelected, {
    bool isOptional = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label + (isOptional ? ' (Bitari ngombwa)' : ''),
          style: AppTheme.bodySmall.copyWith(color: Colors.black87),
        ),
        SizedBox(height: AppTheme.spacing8),
        InkWell(
          onTap: () => _selectDate(onDateSelected),
          child: Container(
            padding: EdgeInsets.all(AppTheme.spacing12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
            ),
            child: Row(
              children: [
                Icon(Icons.calendar_today_rounded, 
                     color: AppTheme.accentColor, size: 20),
                SizedBox(width: AppTheme.spacing8),
                Text(
                  date != null 
                      ? '${date.day}/${date.month}/${date.year}'
                      : 'Hitamo itariki',
                  style: AppTheme.bodyMedium.copyWith(
                    color: date != null ? Colors.black87 : Colors.black54,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFlowIntensitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ubwinshi bw\'amaraso',
          style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppTheme.spacing12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildIntensityOption(1, 'Gake', Colors.green),
            _buildIntensityOption(2, 'Busanzwe', Colors.orange),
            _buildIntensityOption(3, 'Byinshi', Colors.red),
            _buildIntensityOption(4, 'Byinshi cyane', Colors.red.shade800),
          ],
        ),
      ],
    );
  }

  Widget _buildIntensityOption(int value, String label, Color color) {
    final isSelected = _flowIntensity == value;
    
    return GestureDetector(
      onTap: () => setState(() => _flowIntensity = value),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppTheme.spacing12,
          vertical: AppTheme.spacing8,
        ),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(height: AppTheme.spacing4),
            Text(
              label,
              style: AppTheme.bodySmall.copyWith(
                color: isSelected ? color : Colors.black87,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSymptomsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ibimenyetso',
          style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppTheme.spacing12),
        Wrap(
          spacing: AppTheme.spacing8,
          runSpacing: AppTheme.spacing8,
          children: _availableSymptoms.map((symptom) {
            final isSelected = _selectedSymptoms.contains(symptom);
            return FilterChip(
              label: Text(symptom),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedSymptoms.add(symptom);
                  } else {
                    _selectedSymptoms.remove(symptom);
                  }
                });
              },
              selectedColor: AppTheme.accentColor.withOpacity(0.2),
              checkmarkColor: AppTheme.accentColor,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Inyandiko',
          style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppTheme.spacing12),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Andika inyandiko zinyuranye...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
              borderSide: BorderSide(color: AppTheme.accentColor),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Siga'),
        ),
        SizedBox(width: AppTheme.spacing12),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveCycle,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.accentColor,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('Bika'),
        ),
      ],
    );
  }

  Future<void> _selectDate(Function(DateTime) onDateSelected) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    
    if (date != null) {
      onDateSelected(date);
    }
  }

  Future<void> _saveCycle() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final cycle = await _familyPlanningService.recordMenstrualCycle(
        startDate: _startDate,
        endDate: _endDate,
        flowIntensity: _flowIntensity,
        symptoms: _selectedSymptoms,
        notes: _notesController.text.trim().isNotEmpty 
            ? _notesController.text.trim() 
            : null,
      );

      if (cycle != null && mounted) {
        widget.onCycleSaved(cycle);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Imihango yabitswe neza!'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Habaye ikosa mu kubika imihango'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ikosa: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
