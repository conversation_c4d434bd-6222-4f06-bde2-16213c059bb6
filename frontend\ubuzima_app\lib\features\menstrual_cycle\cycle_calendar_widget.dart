import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import '../../core/theme/app_theme.dart';
import '../../core/models/menstrual_cycle_model.dart';

enum CyclePhase { menstrual, follicular, ovulation, luteal }

extension CyclePhaseExtension on CyclePhase {
  String get label {
    switch (this) {
      case CyclePhase.menstrual:
        return 'Imihango';
      case CyclePhase.follicular:
        return 'Gutegura';
      case CyclePhase.ovulation:
        return '<PERSON>ohora';
      case CyclePhase.luteal:
        return 'Gutegereza';
    }
  }

  Color get color {
    switch (this) {
      case CyclePhase.menstrual:
        return Colors.red.shade400;
      case CyclePhase.follicular:
        return Colors.blue.shade300;
      case CyclePhase.ovulation:
        return Colors.green.shade400;
      case CyclePhase.luteal:
        return Colors.orange.shade300;
    }
  }
}

class CycleCalendarWidget extends StatefulWidget {
  final List<MenstrualCycle> cycles;
  final Map<String, dynamic> currentCycle;
  final Function(DateTime) onDateSelected;

  const CycleCalendarWidget({
    super.key,
    required this.cycles,
    required this.currentCycle,
    required this.onDateSelected,
  });

  @override
  State<CycleCalendarWidget> createState() => _CycleCalendarWidgetState();
}

class _CycleCalendarWidgetState extends State<CycleCalendarWidget> {
  late final ValueNotifier<List<DateTime>> _selectedEvents;
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();
    _selectedEvents = ValueNotifier(_getEventsForDay(_selectedDay!));
  }

  @override
  void dispose() {
    _selectedEvents.dispose();
    super.dispose();
  }

  List<DateTime> _getEventsForDay(DateTime day) {
    // Return cycle events for the given day
    return widget.cycles
        .where((cycle) => isSameDay(cycle.startDate, day))
        .map((cycle) => cycle.startDate)
        .toList();
  }

  CyclePhase? _getPhaseForDay(DateTime day) {
    for (final cycle in widget.cycles) {
      final cycleStart = cycle.startDate;
      final cycleLength = cycle.cycleLength ?? 28;

      final daysSinceStart = day.difference(cycleStart).inDays;

      if (daysSinceStart >= 0 && daysSinceStart < cycleLength) {
        if (daysSinceStart < 5) return CyclePhase.menstrual;
        if (daysSinceStart < 13) return CyclePhase.follicular;
        if (daysSinceStart < 16) return CyclePhase.ovulation;
        return CyclePhase.luteal;
      }
    }
    return null;
  }

  bool _isPredictedPeriod(DateTime day) {
    if (widget.cycles.isEmpty) return false;

    final lastCycle = widget.cycles.first;
    final avgCycleLength = _calculateAverageCycleLength();
    final nextPeriodStart = lastCycle.startDate.add(
      Duration(days: avgCycleLength),
    );

    return day.isAfter(nextPeriodStart.subtract(const Duration(days: 1))) &&
        day.isBefore(nextPeriodStart.add(const Duration(days: 5)));
  }

  bool _isPredictedOvulation(DateTime day) {
    if (widget.cycles.isEmpty) return false;

    final lastCycle = widget.cycles.first;
    final avgCycleLength = _calculateAverageCycleLength();
    final nextOvulation = lastCycle.startDate.add(
      Duration(days: avgCycleLength - 14),
    );

    return isSameDay(day, nextOvulation) ||
        (day.isAfter(nextOvulation.subtract(const Duration(days: 1))) &&
            day.isBefore(nextOvulation.add(const Duration(days: 2))));
  }

  int _calculateAverageCycleLength() {
    if (widget.cycles.length < 2) return 28;

    int totalLength = 0;
    for (int i = 0; i < widget.cycles.length - 1; i++) {
      totalLength +=
          widget.cycles[i].startDate
              .difference(widget.cycles[i + 1].startDate)
              .inDays;
    }

    return (totalLength / (widget.cycles.length - 1)).round();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TableCalendar<DateTime>(
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: _focusedDay,
        calendarFormat: _calendarFormat,
        eventLoader: _getEventsForDay,
        startingDayOfWeek: StartingDayOfWeek.monday,
        calendarStyle: CalendarStyle(
          outsideDaysVisible: false,
          weekendTextStyle: AppTheme.bodyMedium.copyWith(color: Colors.red),
          holidayTextStyle: AppTheme.bodyMedium.copyWith(color: Colors.red),
          selectedDecoration: BoxDecoration(
            color: AppTheme.accentColor,
            shape: BoxShape.circle,
          ),
          todayDecoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.5),
            shape: BoxShape.circle,
          ),
          markerDecoration: BoxDecoration(
            color: Colors.red,
            shape: BoxShape.circle,
          ),
        ),
        headerStyle: HeaderStyle(
          formatButtonVisible: true,
          titleCentered: true,
          formatButtonShowsNext: false,
          formatButtonDecoration: BoxDecoration(
            color: AppTheme.accentColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
          ),
          formatButtonTextStyle: AppTheme.bodySmall.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        onDaySelected: (selectedDay, focusedDay) {
          if (!isSameDay(_selectedDay, selectedDay)) {
            setState(() {
              _selectedDay = selectedDay;
              _focusedDay = focusedDay;
            });
            _selectedEvents.value = _getEventsForDay(selectedDay);
            widget.onDateSelected(selectedDay);
          }
        },
        onFormatChanged: (format) {
          if (_calendarFormat != format) {
            setState(() {
              _calendarFormat = format;
            });
          }
        },
        onPageChanged: (focusedDay) {
          _focusedDay = focusedDay;
        },
        selectedDayPredicate: (day) {
          return isSameDay(_selectedDay, day);
        },
      ),
    );
  }
}
