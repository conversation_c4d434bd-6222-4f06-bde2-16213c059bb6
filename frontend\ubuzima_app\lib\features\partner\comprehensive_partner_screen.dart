import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/family_planning_service.dart';
import '../../widgets/voice_button.dart';

class ComprehensivePartnerScreen extends StatefulWidget {
  const ComprehensivePartnerScreen({super.key});

  @override
  State<ComprehensivePartnerScreen> createState() =>
      _ComprehensivePartnerScreenState();
}

class _ComprehensivePartnerScreenState extends State<ComprehensivePartnerScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final FamilyPlanningService _familyPlanningService = FamilyPlanningService();

  Map<String, dynamic> _partnerData = {};
  List<Map<String, dynamic>> _sharedPlans = [];
  List<Map<String, dynamic>> _communications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadPartnerData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadPartnerData() async {
    setState(() => _isLoading = true);

    try {
      // Load partner involvement data
      final partnerData = await _familyPlanningService.getPartnerInvolvement();

      setState(() {
        _partnerData = partnerData['partner'] ?? {};
        _sharedPlans = List<Map<String, dynamic>>.from(
          partnerData['sharedPlans'] ?? [],
        );
        _communications = List<Map<String, dynamic>>.from(
          partnerData['communications'] ?? [],
        );
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Habaye ikosa mu gufata amakuru: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Kwishyira Hamwe n\'Umukunzi'),
        backgroundColor: AppTheme.infoColor,
        foregroundColor: Colors.white,
        actions: [
          VoiceButton(
            heroTag: 'partner_voice',
            prompt: 'Kwishyira hamwe n\'umukunzi',
            onResult: (result) {
              debugPrint('Voice result: $result');
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh_rounded),
            onPressed: _loadPartnerData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.person_rounded), text: 'Umukunzi'),
            Tab(icon: Icon(Icons.share_rounded), text: 'Gahunda'),
            Tab(icon: Icon(Icons.chat_rounded), text: 'Itumanaho'),
            Tab(icon: Icon(Icons.analytics_rounded), text: 'Ibipimo'),
          ],
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                controller: _tabController,
                children: [
                  _buildPartnerProfileTab(isTablet),
                  _buildSharedPlansTab(isTablet),
                  _buildCommunicationTab(isTablet),
                  _buildAnalyticsTab(isTablet),
                ],
              ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showInvitePartnerDialog(),
        backgroundColor: AppTheme.infoColor,
        icon: const Icon(Icons.person_add_rounded, color: Colors.white),
        label: const Text(
          'Tuma Ubutumire',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildPartnerProfileTab(bool isTablet) {
    return RefreshIndicator(
      onRefresh: _loadPartnerData,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeCard(isTablet),
            SizedBox(height: AppTheme.spacing24),
            if (_partnerData.isEmpty)
              _buildNoPartnerCard(isTablet)
            else
              _buildPartnerInfoCard(isTablet),
            SizedBox(height: AppTheme.spacing24),
            _buildPartnerBenefitsCard(isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.infoColor.withValues(alpha: 0.1), Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: AppTheme.infoColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.people_rounded,
                color: AppTheme.infoColor,
                size: isTablet ? 32 : 28,
              ),
              SizedBox(width: AppTheme.spacing12),
              Expanded(
                child: Text(
                  'Kwishyira Hamwe n\'Umukunzi',
                  style: AppTheme.headingMedium.copyWith(
                    fontSize: isTablet ? 20 : 18,
                    color: AppTheme.infoColor,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppTheme.spacing12),
          Text(
            'Gufatanya n\'umukunzi wawe mu gufata ibyemezo by\'ubwiyunge bw\'umuryango ni ingenzi cyane.',
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.black87,
              fontSize: isTablet ? 14 : 13,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms);
  }

  Widget _buildNoPartnerCard(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing24),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.person_add_outlined,
              size: isTablet ? 64 : 48,
              color: Colors.grey.shade400,
            ),
            SizedBox(height: AppTheme.spacing16),
            Text(
              'Nta mukunzi wafatanije nawe',
              style: AppTheme.headingSmall.copyWith(
                color: Colors.black54,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppTheme.spacing8),
            Text(
              'Tuma ubutumire umukunzi wawe kugira ngo mufatanye mu gufata ibyemezo by\'ubwiyunge.',
              style: AppTheme.bodyMedium.copyWith(color: Colors.black54),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppTheme.spacing16),
            ElevatedButton.icon(
              onPressed: () => _showInvitePartnerDialog(),
              icon: const Icon(Icons.person_add_rounded),
              label: const Text('Tuma Ubutumire'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.infoColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPartnerInfoCard(bool isTablet) {
    final partnerName = _partnerData['name'] ?? 'Umukunzi';
    final partnerEmail = _partnerData['email'] ?? '';
    final joinedDate = _partnerData['joinedDate'] ?? '';
    final isActive = _partnerData['isActive'] ?? false;

    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: isTablet ? 30 : 25,
                backgroundColor: AppTheme.infoColor.withValues(alpha: 0.2),
                child: Icon(
                  Icons.person_rounded,
                  color: AppTheme.infoColor,
                  size: isTablet ? 32 : 28,
                ),
              ),
              SizedBox(width: AppTheme.spacing16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      partnerName,
                      style: AppTheme.headingMedium.copyWith(
                        fontSize: isTablet ? 18 : 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (partnerEmail.isNotEmpty)
                      Text(
                        partnerEmail,
                        style: AppTheme.bodyMedium.copyWith(
                          color: Colors.black54,
                          fontSize: isTablet ? 13 : 12,
                        ),
                      ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing8,
                  vertical: AppTheme.spacing4,
                ),
                decoration: BoxDecoration(
                  color: isActive ? AppTheme.successColor : Colors.grey,
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                ),
                child: Text(
                  isActive ? 'Birakora' : 'Ntibikora',
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white,
                    fontSize: isTablet ? 11 : 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          if (joinedDate.isNotEmpty) ...[
            SizedBox(height: AppTheme.spacing12),
            Row(
              children: [
                Icon(
                  Icons.calendar_today_rounded,
                  color: Colors.black54,
                  size: isTablet ? 16 : 14,
                ),
                SizedBox(width: AppTheme.spacing4),
                Text(
                  'Yinjiye ku: $joinedDate',
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.black54,
                    fontSize: isTablet ? 12 : 11,
                  ),
                ),
              ],
            ),
          ],
          SizedBox(height: AppTheme.spacing16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showPartnerSettingsDialog(),
                  icon: const Icon(Icons.settings_rounded),
                  label: const Text('Amagenamiterere'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.infoColor,
                    side: BorderSide(color: AppTheme.infoColor),
                  ),
                ),
              ),
              SizedBox(width: AppTheme.spacing8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _startCommunication(),
                  icon: const Icon(Icons.chat_rounded),
                  label: const Text('Itumanaho'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.infoColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 800.ms, delay: 200.ms);
  }

  Widget _buildPartnerBenefitsCard(bool isTablet) {
    final benefits = [
      {
        'icon': Icons.share_rounded,
        'title': 'Gusangira Gahunda',
        'description': 'Mufatanye mu gushyiraho gahunda z\'ubwiyunge',
      },
      {
        'icon': Icons.chat_rounded,
        'title': 'Itumanaho Ryoroshye',
        'description': 'Muvugane ku bibazo by\'ubwiyunge',
      },
      {
        'icon': Icons.analytics_rounded,
        'title': 'Gukurikirana Hamwe',
        'description': 'Murebe uko gahunda zihagaze hamwe',
      },
      {
        'icon': Icons.support_rounded,
        'title': 'Gufashanya',
        'description': 'Mufashanyane mu gufata ibyemezo',
      },
    ];

    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Inyungu zo Gufatanya',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 18 : 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          ...benefits.asMap().entries.map((entry) {
            final index = entry.key;
            final benefit = entry.value;

            return Container(
                  margin: EdgeInsets.only(bottom: AppTheme.spacing12),
                  padding: EdgeInsets.all(AppTheme.spacing12),
                  decoration: BoxDecoration(
                    color: AppTheme.infoColor.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                    border: Border.all(
                      color: AppTheme.infoColor.withValues(alpha: 0.1),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(AppTheme.spacing8),
                        decoration: BoxDecoration(
                          color: AppTheme.infoColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(
                            AppTheme.radiusSmall,
                          ),
                        ),
                        child: Icon(
                          benefit['icon'] as IconData,
                          color: AppTheme.infoColor,
                          size: isTablet ? 20 : 18,
                        ),
                      ),
                      SizedBox(width: AppTheme.spacing12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              benefit['title'] as String,
                              style: AppTheme.bodyMedium.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: isTablet ? 14 : 13,
                              ),
                            ),
                            Text(
                              benefit['description'] as String,
                              style: AppTheme.bodySmall.copyWith(
                                color: Colors.black54,
                                fontSize: isTablet ? 12 : 11,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
                .animate(delay: (index * 100).ms)
                .fadeIn(duration: 600.ms)
                .slideX(begin: 0.3);
          }).toList(),
        ],
      ),
    ).animate().fadeIn(duration: 1000.ms, delay: 400.ms);
  }

  // Placeholder methods for remaining tabs
  Widget _buildSharedPlansTab(bool isTablet) {
    return Center(
      child: Text(
        'Gahunda Dusangiye - Hazongera',
        style: AppTheme.headingMedium,
      ),
    );
  }

  Widget _buildCommunicationTab(bool isTablet) {
    return Center(
      child: Text('Itumanaho - Hazongera', style: AppTheme.headingMedium),
    );
  }

  Widget _buildAnalyticsTab(bool isTablet) {
    return Center(
      child: Text('Ibipimo - Hazongera', style: AppTheme.headingMedium),
    );
  }

  // Helper methods
  void _showInvitePartnerDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Tuma Ubutumire Umukunzi'),
            content: const Text(
              'Aha hazongera form yo kohereza ubutumire umukunzi.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Siga'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Ubutumire bwoherejwe!'),
                      backgroundColor: AppTheme.successColor,
                    ),
                  );
                },
                child: const Text('Ohereza'),
              ),
            ],
          ),
    );
  }

  void _showPartnerSettingsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Amagenamiterere y\'Umukunzi'),
            content: const Text('Aha hazongera amagenamiterere y\'ubufatanye.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Siga'),
              ),
            ],
          ),
    );
  }

  void _startCommunication() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Itumanaho ryatangiye!'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }
}
