import 'package:json_annotation/json_annotation.dart';
import 'pregnancy_plan_enums.dart';

part 'pregnancy_plan_model.g.dart';

@JsonSerializable()
class PregnancyPlan {
  final String id;
  final String userId;
  final String planName;
  final DateTime targetDate;
  final DateTime targetConceptionDate;
  final String? description;
  final PregnancyPlanStatus status;
  final String currentStatus;
  final String? partnerInvolvement;
  final List<String> preparationSteps;
  final Map<String, bool> healthChecks;
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;

  PregnancyPlan({
    required this.id,
    required this.userId,
    required this.planName,
    required this.targetDate,
    DateTime? targetConceptionDate,
    this.description,
    PregnancyPlanStatus? status,
    required this.currentStatus,
    this.partnerInvolvement,
    List<String>? preparationSteps,
    Map<String, bool>? healthChecks,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  }) : targetConceptionDate = targetConceptionDate ?? targetDate,
       status = status ?? PregnancyPlanStatus.planning,
       preparationSteps = preparationSteps ?? [],
       healthChecks = healthChecks ?? {};

  factory PregnancyPlan.fromJson(Map<String, dynamic> json) =>
      _$PregnancyPlanFromJson(json);

  Map<String, dynamic> toJson() => _$PregnancyPlanToJson(this);
}
