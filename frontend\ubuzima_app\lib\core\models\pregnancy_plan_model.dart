import 'package:json_annotation/json_annotation.dart';

part 'pregnancy_plan_model.g.dart';

@JsonSerializable()
class PregnancyPlan {
  final String id;
  final String userId;
  final String planName;
  final DateTime targetDate;
  final String? description;
  final String currentStatus;
  final String? partnerInvolvement;
  final DateTime createdAt;
  final DateTime? updatedAt;

  PregnancyPlan({
    required this.id,
    required this.userId,
    required this.planName,
    required this.targetDate,
    this.description,
    required this.currentStatus,
    this.partnerInvolvement,
    required this.createdAt,
    this.updatedAt,
  });

  factory PregnancyPlan.fromJson(Map<String, dynamic> json) =>
      _$PregnancyPlanFromJson(json);

  Map<String, dynamic> toJson() => _$PregnancyPlanToJson(this);
}
