import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../core/models/message.dart';
import '../../core/models/user_model.dart';
import '../../core/services/message_service.dart';
import '../../core/services/auth_service.dart';
import '../../core/constants/app_constants.dart';
import '../../widgets/voice_button.dart';
import 'chat_screen.dart';

class MessagingScreen extends StatefulWidget {
  const MessagingScreen({super.key});

  @override
  State<MessagingScreen> createState() => _MessagingScreenState();
}

class _MessagingScreenState extends State<MessagingScreen> {
  final MessageService _messageService = MessageService();
  final AuthService _authService = AuthService();

  List<ChatContact> _contacts = [];
  bool _isLoading = true;
  String? _error;
  int _totalUnreadCount = 0;

  @override
  void initState() {
    super.initState();
    _loadConversations();
  }

  Future<void> _loadConversations() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final currentUser =
          _authService.currentUser ??
          await _authService.getCurrentUserProfile();
      if (currentUser == null) {
        setState(() {
          _error = 'Ntabwo winjiye muri sisitemu';
          _isLoading = false;
        });
        return;
      }

      // Load conversations from API
      final conversations = await _messageService.getConversations();

      // Convert conversations to chat contacts
      final contacts =
          conversations
              .map(
                (conversation) =>
                    ChatContact.fromConversation(conversation, currentUser.id),
              )
              .toList();

      // Calculate total unread count
      final totalUnread = contacts.fold<int>(
        0,
        (sum, contact) => sum + contact.unreadCount,
      );

      setState(() {
        _contacts = contacts;
        _totalUnreadCount = totalUnread;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Ikosa mu gufata ubutumwa: $e';
        _isLoading = false;
      });
    }
  }

  void _handleVoiceCommand(String command) {
    final lowerCommand = command.toLowerCase();
    if (lowerCommand.contains('muganga') || lowerCommand.contains('doctor')) {
      // Filter to show only doctors
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Abaganga bose...')));
    } else if (lowerCommand.contains('umuforomo') ||
        lowerCommand.contains('nurse')) {
      // Filter to show only nurses
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Abaforomo bose...')));
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // Custom App Bar
          _buildAppBar(isTablet),

          // Quick Actions
          SliverToBoxAdapter(child: _buildQuickActions(isTablet)),

          // Contacts List
          SliverToBoxAdapter(child: _buildContactsList(isTablet)),

          // Bottom Padding
          SliverToBoxAdapter(child: SizedBox(height: AppTheme.spacing64)),
        ],
      ),
      floatingActionButton: VoiceButton(
        prompt:
            'Vuga: "Muganga" kugira ngo ugere ku baganga, cyangwa "Umuforomo" kugira ngo ugere ku baforomo',
        onResult: _handleVoiceCommand,
        tooltip: 'Shakisha abaganga mu ijwi',
      ),
    );
  }

  Widget _buildAppBar(bool isTablet) {
    return SliverAppBar(
      expandedHeight: isTablet ? 200 : 160,
      floating: false,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.accentColor,
                AppTheme.accentColor.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(32),
              bottomRight: Radius.circular(32),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.all(
                isTablet ? AppTheme.spacing32 : AppTheme.spacing24,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      Container(
                        width: isTablet ? 60 : 50,
                        height: isTablet ? 60 : 50,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(
                            isTablet ? 30 : 25,
                          ),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          Icons.chat_rounded,
                          color: Colors.white,
                          size: isTablet ? 32 : 28,
                        ),
                      ),
                      SizedBox(width: AppTheme.spacing16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Ubutumwa',
                              style: AppTheme.headingLarge.copyWith(
                                color: Colors.white,
                                fontSize: isTablet ? 28 : 24,
                              ),
                            ),
                            Text(
                              'Vugana n\'abaganga n\'abaforomo',
                              style: AppTheme.bodyLarge.copyWith(
                                color: Colors.white.withValues(alpha: 0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Notification Badge
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: AppTheme.spacing12,
                          vertical: AppTheme.spacing4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(
                            AppTheme.radiusRound,
                          ),
                        ),
                        child: Text(
                          _totalUnreadCount > 0
                              ? '$_totalUnreadCount bishya'
                              : 'Ntamutumwa',
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.accentColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions(bool isTablet) {
    return Container(
      margin: EdgeInsets.all(
        isTablet ? AppTheme.spacing32 : AppTheme.spacing24,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ibikorwa byihuse',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 24 : 20,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionCard(
                  'Gusaba ubufasha bw\'ihutirwa',
                  'Hamagara abaganga',
                  Icons.emergency_rounded,
                  AppTheme.errorColor,
                  isTablet,
                  () {
                    // Emergency contact
                    _showEmergencyDialog();
                  },
                ),
              ),
              SizedBox(width: AppTheme.spacing12),
              Expanded(
                child: _buildQuickActionCard(
                  'Gushyiraho gahunda',
                  'Gahunda y\'ubuzima',
                  Icons.calendar_today_rounded,
                  AppTheme.primaryColor,
                  isTablet,
                  () {
                    // Schedule appointment
                    _showAppointmentDialog();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(delay: 200.ms).slideY(begin: 0.3, duration: 600.ms);
  }

  Widget _buildQuickActionCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    bool isTablet,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        boxShadow: AppTheme.softShadow,
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          child: Padding(
            padding: EdgeInsets.all(
              isTablet ? AppTheme.spacing20 : AppTheme.spacing16,
            ),
            child: Column(
              children: [
                Container(
                  width: isTablet ? 60 : 50,
                  height: isTablet ? 60 : 50,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(isTablet ? 30 : 25),
                  ),
                  child: Icon(icon, color: color, size: isTablet ? 28 : 24),
                ),
                SizedBox(height: AppTheme.spacing12),
                Text(
                  title,
                  style: AppTheme.labelLarge.copyWith(
                    fontSize: isTablet ? 16 : 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  subtitle,
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContactsList(bool isTablet) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: isTablet ? AppTheme.spacing32 : AppTheme.spacing24,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Abaganga n\'abaforomo',
                style: AppTheme.headingMedium.copyWith(
                  fontSize: isTablet ? 24 : 20,
                ),
              ),
              if (_isLoading)
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.primaryColor,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: AppTheme.spacing16),

          if (_error != null)
            _buildErrorWidget(isTablet)
          else if (_isLoading && _contacts.isEmpty)
            _buildLoadingWidget(isTablet)
          else if (_contacts.isEmpty)
            _buildEmptyWidget(isTablet)
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _contacts.length,
              itemBuilder: (context, index) {
                final contact = _contacts[index];
                return _buildContactCard(contact, isTablet, index);
              },
            ),
        ],
      ),
    ).animate().fadeIn(delay: 400.ms).slideY(begin: 0.3, duration: 600.ms);
  }

  Widget _buildContactCard(ChatContact contact, bool isTablet, int index) {
    return Container(
          margin: EdgeInsets.only(bottom: AppTheme.spacing12),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: AppTheme.softShadow,
            border:
                contact.unreadCount > 0
                    ? Border.all(
                      color: AppTheme.primaryColor.withValues(alpha: 0.3),
                    )
                    : null,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                Navigator.of(context).push(
                  PageRouteBuilder(
                    pageBuilder:
                        (context, animation, secondaryAnimation) =>
                            ChatScreen(contact: contact),
                    transitionsBuilder: (
                      context,
                      animation,
                      secondaryAnimation,
                      child,
                    ) {
                      return SlideTransition(
                        position: Tween<Offset>(
                          begin: const Offset(1.0, 0.0),
                          end: Offset.zero,
                        ).animate(animation),
                        child: child,
                      );
                    },
                    transitionDuration: AppConstants.mediumAnimation,
                  ),
                );
              },
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
              child: Padding(
                padding: EdgeInsets.all(
                  isTablet ? AppTheme.spacing20 : AppTheme.spacing16,
                ),
                child: Row(
                  children: [
                    // Avatar with online status
                    Stack(
                      children: [
                        Container(
                          width: isTablet ? 60 : 50,
                          height: isTablet ? 60 : 50,
                          decoration: BoxDecoration(
                            gradient: AppTheme.primaryGradient,
                            borderRadius: BorderRadius.circular(
                              isTablet ? 30 : 25,
                            ),
                          ),
                          child: Icon(
                            Icons.person_rounded,
                            color: Colors.white,
                            size: isTablet ? 32 : 28,
                          ),
                        ),
                        if (contact.isOnline)
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              width: 16,
                              height: 16,
                              decoration: BoxDecoration(
                                color: AppTheme.successColor,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: AppTheme.surfaceColor,
                                  width: 2,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),

                    SizedBox(width: AppTheme.spacing16),

                    // Contact Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                contact.name,
                                style: AppTheme.labelLarge.copyWith(
                                  fontSize: isTablet ? 16 : 14,
                                  fontWeight:
                                      contact.unreadCount > 0
                                          ? FontWeight.bold
                                          : FontWeight.w600,
                                ),
                              ),
                              Text(
                                _formatTime(contact.lastMessageTime),
                                style: AppTheme.bodySmall.copyWith(
                                  color:
                                      contact.unreadCount > 0
                                          ? AppTheme.primaryColor
                                          : AppTheme.textTertiary,
                                  fontWeight:
                                      contact.unreadCount > 0
                                          ? FontWeight.w600
                                          : FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: AppTheme.spacing4),
                          Text(
                            contact.role,
                            style: AppTheme.bodySmall.copyWith(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: AppTheme.spacing4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  contact.lastMessage,
                                  style: AppTheme.bodySmall.copyWith(
                                    color:
                                        contact.unreadCount > 0
                                            ? AppTheme.textPrimary
                                            : AppTheme.textSecondary,
                                    fontWeight:
                                        contact.unreadCount > 0
                                            ? FontWeight.w500
                                            : FontWeight.normal,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (contact.unreadCount > 0)
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: AppTheme.spacing8,
                                    vertical: AppTheme.spacing4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor,
                                    borderRadius: BorderRadius.circular(
                                      AppTheme.radiusRound,
                                    ),
                                  ),
                                  child: Text(
                                    '${contact.unreadCount}',
                                    style: AppTheme.bodySmall.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        )
        .animate(delay: (index * 100).ms)
        .fadeIn()
        .slideX(begin: 0.3, duration: 600.ms);
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else {
      return '${difference.inDays}d';
    }
  }

  void _showEmergencyDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Gusaba ubufasha bw\'ihutirwa'),
            content: const Text('Urashaka guhamagara abaganga b\'ihutirwa?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Kuraguza'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Make emergency call
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorColor,
                ),
                child: const Text(
                  'Hamagara',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  void _showAppointmentDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Gushyiraho gahunda'),
            content: const Text('Urashaka gushyiraho gahunda y\'ubuzima?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Kuraguza'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Schedule appointment
                },
                child: const Text('Shyiraho'),
              ),
            ],
          ),
    );
  }

  Widget _buildLoadingWidget(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing32),
      child: Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          SizedBox(height: AppTheme.spacing16),
          Text(
            'Gufata ubutumwa...',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing24),
      decoration: BoxDecoration(
        color: AppTheme.errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        border: Border.all(color: AppTheme.errorColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            color: AppTheme.errorColor,
            size: isTablet ? 48 : 40,
          ),
          SizedBox(height: AppTheme.spacing12),
          Text(
            _error ?? 'Ikosa ryabaye',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.errorColor),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppTheme.spacing16),
          ElevatedButton(
            onPressed: _loadConversations,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: Text('Ongera ugerageze'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing32),
      child: Column(
        children: [
          Icon(
            Icons.chat_bubble_outline,
            color: AppTheme.textSecondary,
            size: isTablet ? 64 : 48,
          ),
          SizedBox(height: AppTheme.spacing16),
          Text(
            'Ntamutumwa',
            style: AppTheme.headingMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
          SizedBox(height: AppTheme.spacing8),
          Text(
            'Ntabutumwa bwabonetse. Tangira ikiganiro gishya n\'abakozi b\'ubuzima.',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class ChatContact {
  final String id;
  final String name;
  final String role;
  final String lastMessage;
  final DateTime lastMessageTime;
  final int unreadCount;
  final bool isOnline;
  final String avatar;

  ChatContact({
    required this.id,
    required this.name,
    required this.role,
    required this.lastMessage,
    required this.lastMessageTime,
    required this.unreadCount,
    required this.isOnline,
    required this.avatar,
  });

  factory ChatContact.fromConversation(
    Conversation conversation,
    String currentUserId,
  ) {
    // Get the other participant (not the current user)
    final otherParticipant = conversation.participants.firstWhere(
      (user) => user.id != currentUserId,
      orElse: () => conversation.participants.first,
    );

    return ChatContact(
      id: conversation.id,
      name: otherParticipant.name,
      role: _getRoleDisplayName(otherParticipant.role),
      lastMessage: conversation.lastMessage?.content ?? 'Ntamutumwa',
      lastMessageTime: conversation.lastMessage?.createdAt ?? DateTime.now(),
      unreadCount: conversation.unreadCount,
      isOnline: false, // TODO: Implement online status
      avatar:
          otherParticipant.profileImageUrl ??
          'assets/images/default_avatar.jpg',
    );
  }

  static String _getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'Umuyobozi';
      case UserRole.healthWorker:
        return 'Umukozi w\'ubuzima';
      case UserRole.client:
        return 'Umukiriya';
    }
  }
}
