import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/family_planning_service.dart';
import '../../core/models/contraception_method_model.dart';
import '../../widgets/voice_button.dart';

class ComprehensiveContraceptionScreen extends StatefulWidget {
  const ComprehensiveContraceptionScreen({super.key});

  @override
  State<ComprehensiveContraceptionScreen> createState() =>
      _ComprehensiveContraceptionScreenState();
}

class _ComprehensiveContraceptionScreenState
    extends State<ComprehensiveContraceptionScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final FamilyPlanningService _familyPlanningService = FamilyPlanningService();

  List<ContraceptionMethod> _activeMethods = [];
  List<ContraceptionMethod> _methodHistory = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadContraceptionData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadContraceptionData() async {
    setState(() => _isLoading = true);

    try {
      final activeMethods = await _familyPlanningService
          .getContraceptionMethods(activeOnly: true);
      final allMethods = await _familyPlanningService.getContraceptionMethods(
        activeOnly: false,
      );

      setState(() {
        _activeMethods = activeMethods;
        _methodHistory = allMethods.where((m) => !m.isActive).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Habaye ikosa mu gufata amakuru: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Gukurikirana Kurinda Inda'),
        backgroundColor: AppTheme.secondaryColor,
        foregroundColor: Colors.white,
        actions: [
          VoiceButton(
            heroTag: 'contraception_voice',
            prompt: 'Kurinda inda',
            onResult: (result) {
              debugPrint('Voice result: $result');
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh_rounded),
            onPressed: _loadContraceptionData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.shield_rounded), text: 'Bikoresha'),
            Tab(icon: Icon(Icons.notifications_rounded), text: 'Ibyibutsa'),
            Tab(icon: Icon(Icons.history_rounded), text: 'Amateka'),
            Tab(icon: Icon(Icons.analytics_rounded), text: 'Ibipimo'),
          ],
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                controller: _tabController,
                children: [
                  _buildActiveMethodsTab(isTablet),
                  _buildRemindersTab(isTablet),
                  _buildHistoryTab(isTablet),
                  _buildAnalyticsTab(isTablet),
                ],
              ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddMethodDialog(),
        backgroundColor: AppTheme.secondaryColor,
        icon: const Icon(Icons.add_rounded, color: Colors.white),
        label: const Text(
          'Ongeraho Uburyo',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildActiveMethodsTab(bool isTablet) {
    return RefreshIndicator(
      onRefresh: _loadContraceptionData,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeCard(isTablet),
            SizedBox(height: AppTheme.spacing24),
            _buildCurrentMethodsSection(isTablet),
            SizedBox(height: AppTheme.spacing24),
            _buildMethodComparisonCard(isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.secondaryColor.withValues(alpha: 0.1),
            Colors.white,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(
          color: AppTheme.secondaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.shield_rounded,
                color: AppTheme.secondaryColor,
                size: isTablet ? 32 : 28,
              ),
              SizedBox(width: AppTheme.spacing12),
              Expanded(
                child: Text(
                  'Gukurikirana Uburyo bwo Kurinda Inda',
                  style: AppTheme.headingMedium.copyWith(
                    fontSize: isTablet ? 20 : 18,
                    color: AppTheme.secondaryColor,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppTheme.spacing12),
          Text(
            'Hitamo uburyo bukwiye ubuzima bwawe kandi ukabukurikirana neza. Dufasha mu gufata ibyemezo byiza.',
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.black87,
              fontSize: isTablet ? 14 : 13,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms);
  }

  Widget _buildCurrentMethodsSection(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Uburyo Bukoresha',
              style: AppTheme.headingMedium.copyWith(
                fontSize: isTablet ? 20 : 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (_activeMethods.isNotEmpty)
              TextButton.icon(
                onPressed: () => _showAddMethodDialog(),
                icon: const Icon(Icons.add_rounded),
                label: const Text('Ongeraho'),
              ),
          ],
        ),
        SizedBox(height: AppTheme.spacing16),
        if (_activeMethods.isEmpty)
          _buildEmptyMethodsCard(isTablet)
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _activeMethods.length,
            itemBuilder: (context, index) {
              final method = _activeMethods[index];
              return _buildMethodCard(method, isTablet, index);
            },
          ),
      ],
    ).animate().fadeIn(duration: 800.ms, delay: 200.ms);
  }

  Widget _buildEmptyMethodsCard(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing24),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.shield_outlined,
              size: isTablet ? 64 : 48,
              color: Colors.grey.shade400,
            ),
            SizedBox(height: AppTheme.spacing16),
            Text(
              'Nta buryo bwo kurinda inda bukoresha',
              style: AppTheme.headingSmall.copyWith(
                color: Colors.black54,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppTheme.spacing8),
            Text(
              'Ongeraho uburyo bushya bwo kurinda inda kugira ngo utangire gukurikirana.',
              style: AppTheme.bodyMedium.copyWith(color: Colors.black54),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppTheme.spacing16),
            ElevatedButton.icon(
              onPressed: () => _showAddMethodDialog(),
              icon: const Icon(Icons.add_rounded),
              label: const Text('Ongeraho Uburyo'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.secondaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMethodCard(
    ContraceptionMethod method,
    bool isTablet,
    int index,
  ) {
    return Container(
          margin: EdgeInsets.only(bottom: AppTheme.spacing12),
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: AppTheme.secondaryColor.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(AppTheme.spacing8),
                    decoration: BoxDecoration(
                      color: AppTheme.secondaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                    ),
                    child: Icon(
                      _getMethodIcon(method.methodType),
                      color: AppTheme.secondaryColor,
                      size: isTablet ? 24 : 20,
                    ),
                  ),
                  SizedBox(width: AppTheme.spacing12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          method.methodName,
                          style: AppTheme.bodyLarge.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: isTablet ? 16 : 14,
                          ),
                        ),
                        Text(
                          'Yatangiye: ${_formatDate(method.startDate)}',
                          style: AppTheme.bodySmall.copyWith(
                            color: Colors.black54,
                            fontSize: isTablet ? 12 : 11,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppTheme.spacing8,
                      vertical: AppTheme.spacing4,
                    ),
                    decoration: BoxDecoration(
                      color: _getEffectivenessColor(method.effectiveness),
                      borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                    ),
                    child: Text(
                      '${method.effectiveness.toInt()}%',
                      style: AppTheme.bodySmall.copyWith(
                        color: Colors.white,
                        fontSize: isTablet ? 11 : 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppTheme.spacing12),
              Text(
                method.instructions,
                style: AppTheme.bodyMedium.copyWith(
                  color: Colors.black87,
                  fontSize: isTablet ? 13 : 12,
                ),
              ),
              if (method.sideEffects.isNotEmpty) ...[
                SizedBox(height: AppTheme.spacing8),
                Wrap(
                  spacing: AppTheme.spacing4,
                  runSpacing: AppTheme.spacing4,
                  children:
                      method.sideEffects
                          .take(3)
                          .map(
                            (effect) => Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: AppTheme.spacing6,
                                vertical: AppTheme.spacing2,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.warningColor.withValues(
                                  alpha: 0.1,
                                ),
                                borderRadius: BorderRadius.circular(
                                  AppTheme.radiusSmall,
                                ),
                              ),
                              child: Text(
                                effect,
                                style: AppTheme.bodySmall.copyWith(
                                  color: AppTheme.warningColor,
                                  fontSize: isTablet ? 10 : 9,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                ),
              ],
              if (method.nextAppointment != null) ...[
                SizedBox(height: AppTheme.spacing8),
                Row(
                  children: [
                    Icon(
                      Icons.event_rounded,
                      color: AppTheme.primaryColor,
                      size: isTablet ? 16 : 14,
                    ),
                    SizedBox(width: AppTheme.spacing4),
                    Text(
                      'Gahunda itaha: ${_formatDate(method.nextAppointment!)}',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.primaryColor,
                        fontSize: isTablet ? 11 : 10,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .slideX(begin: 0.3);
  }

  Widget _buildMethodComparisonCard(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Uburyo bwo Kurinda Inda',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 18 : 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          _buildMethodOption(
            'Ibiyobyabwenge (Pills)',
            91.0,
            'Nywa buri munsi',
            isTablet,
          ),
          _buildMethodOption('IUD', 99.0, 'Gusuzuma buri myaka 5', isTablet),
          _buildMethodOption(
            'Implant',
            99.0,
            'Gusimbura nyuma y\'imyaka 3',
            isTablet,
          ),
          _buildMethodOption(
            'Urushinge (Injection)',
            94.0,
            'Urushinge buri mezi 3',
            isTablet,
          ),
          _buildMethodOption('Kondomu', 85.0, 'Gukoresha buri gihe', isTablet),
        ],
      ),
    ).animate().fadeIn(duration: 1000.ms, delay: 400.ms);
  }

  Widget _buildMethodOption(
    String name,
    double effectiveness,
    String instructions,
    bool isTablet,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacing8),
      padding: EdgeInsets.all(AppTheme.spacing12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: AppTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: isTablet ? 14 : 13,
                  ),
                ),
                Text(
                  instructions,
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.black54,
                    fontSize: isTablet ? 12 : 11,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppTheme.spacing8,
              vertical: AppTheme.spacing4,
            ),
            decoration: BoxDecoration(
              color: _getEffectivenessColor(effectiveness),
              borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
            ),
            child: Text(
              '${effectiveness.toInt()}%',
              style: AppTheme.bodySmall.copyWith(
                color: Colors.white,
                fontSize: isTablet ? 11 : 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRemindersTab(bool isTablet) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ibyibutsa',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 20 : 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          _buildReminderCard(
            'Ibiyobyabwenge',
            'Buri munsi saa 8:00',
            Icons.medication_rounded,
            isTablet,
          ),
          _buildReminderCard(
            'Gahunda y\'ubuvuzi',
            'Ku wa mbere w\'ukwezi gutaha',
            Icons.event_rounded,
            isTablet,
          ),
          _buildReminderCard(
            'Gusuzuma ubuzima',
            'Buri mezi 3',
            Icons.health_and_safety_rounded,
            isTablet,
          ),
        ],
      ),
    );
  }

  Widget _buildReminderCard(
    String title,
    String schedule,
    IconData icon,
    bool isTablet,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacing12),
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppTheme.spacing8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: isTablet ? 24 : 20,
            ),
          ),
          SizedBox(width: AppTheme.spacing12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTheme.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: isTablet ? 16 : 14,
                  ),
                ),
                Text(
                  schedule,
                  style: AppTheme.bodyMedium.copyWith(
                    color: Colors.black54,
                    fontSize: isTablet ? 13 : 12,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: true,
            onChanged: (value) {},
            activeColor: AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryTab(bool isTablet) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Amateka y\'Uburyo',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 20 : 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          if (_methodHistory.isEmpty)
            Container(
              padding: EdgeInsets.all(AppTheme.spacing20),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
              child: Center(
                child: Text(
                  'Nta mateka y\'uburyo bwo kurinda inda',
                  style: AppTheme.bodyMedium.copyWith(color: Colors.black54),
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _methodHistory.length,
              itemBuilder: (context, index) {
                final method = _methodHistory[index];
                return _buildHistoryCard(method, isTablet);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildHistoryCard(ContraceptionMethod method, bool isTablet) {
    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacing12),
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getMethodIcon(method.methodType),
                color: Colors.grey.shade600,
                size: isTablet ? 24 : 20,
              ),
              SizedBox(width: AppTheme.spacing12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      method.methodName,
                      style: AppTheme.bodyLarge.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: isTablet ? 16 : 14,
                      ),
                    ),
                    Text(
                      '${_formatDate(method.startDate)} - ${method.endDate != null ? _formatDate(method.endDate!) : 'Ubu'}',
                      style: AppTheme.bodySmall.copyWith(
                        color: Colors.black54,
                        fontSize: isTablet ? 12 : 11,
                      ),
                    ),
                  ],
                ),
              ),
              if (method.satisfactionRating != null)
                Row(
                  children: List.generate(
                    5,
                    (index) => Icon(
                      index < method.satisfactionRating!
                          ? Icons.star
                          : Icons.star_border,
                      color: AppTheme.warningColor,
                      size: isTablet ? 16 : 14,
                    ),
                  ),
                ),
            ],
          ),
          if (method.notes != null) ...[
            SizedBox(height: AppTheme.spacing8),
            Text(
              method.notes!,
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.black87,
                fontSize: isTablet ? 13 : 12,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab(bool isTablet) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ibipimo n\'Isesengura',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 20 : 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          _buildAnalyticsCard(
            'Uburyo Bukoresha',
            '${_activeMethods.length}',
            Icons.shield_rounded,
            AppTheme.primaryColor,
            isTablet,
          ),
          _buildAnalyticsCard(
            'Ubushobozi Rusange',
            _calculateAverageEffectiveness(),
            Icons.analytics_rounded,
            AppTheme.successColor,
            isTablet,
          ),
          _buildAnalyticsCard(
            'Igihe Cyose',
            _calculateTotalDuration(),
            Icons.schedule_rounded,
            AppTheme.infoColor,
            isTablet,
          ),
          _buildAnalyticsCard(
            'Ubwoba Rusange',
            _calculateAverageSatisfaction(),
            Icons.star_rounded,
            AppTheme.warningColor,
            isTablet,
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsCard(
    String title,
    String value,
    IconData icon,
    Color color,
    bool isTablet,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacing12),
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppTheme.spacing12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
            ),
            child: Icon(icon, color: color, size: isTablet ? 28 : 24),
          ),
          SizedBox(width: AppTheme.spacing16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: AppTheme.headingLarge.copyWith(
                    color: color,
                    fontSize: isTablet ? 24 : 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  title,
                  style: AppTheme.bodyMedium.copyWith(
                    color: Colors.black87,
                    fontSize: isTablet ? 14 : 13,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  IconData _getMethodIcon(String methodType) {
    switch (methodType.toLowerCase()) {
      case 'pill':
        return Icons.medication_rounded;
      case 'iud':
        return Icons.medical_services_rounded;
      case 'implant':
        return Icons.healing_rounded;
      case 'injection':
        return Icons.vaccines_rounded;
      case 'patch':
        return Icons.local_pharmacy_rounded;
      case 'ring':
        return Icons.circle_outlined;
      case 'condom':
        return Icons.shield_rounded;
      default:
        return Icons.health_and_safety_rounded;
    }
  }

  Color _getEffectivenessColor(double effectiveness) {
    if (effectiveness >= 95) return AppTheme.successColor;
    if (effectiveness >= 85) return AppTheme.warningColor;
    return AppTheme.errorColor;
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  String _calculateAverageEffectiveness() {
    if (_activeMethods.isEmpty) return '0%';
    final average =
        _activeMethods.map((m) => m.effectiveness).reduce((a, b) => a + b) /
        _activeMethods.length;
    return '${average.toInt()}%';
  }

  String _calculateTotalDuration() {
    if (_activeMethods.isEmpty) return '0 amezi';
    final totalDays = _activeMethods
        .map((m) => DateTime.now().difference(m.startDate).inDays)
        .reduce((a, b) => a + b);
    final months = (totalDays / 30).round();
    return '$months amezi';
  }

  String _calculateAverageSatisfaction() {
    final allMethods = [..._activeMethods, ..._methodHistory];
    final ratingsOnly =
        allMethods.where((m) => m.satisfactionRating != null).toList();
    if (ratingsOnly.isEmpty) return 'N/A';
    final average =
        ratingsOnly.map((m) => m.satisfactionRating!).reduce((a, b) => a + b) /
        ratingsOnly.length;
    return '${average.toStringAsFixed(1)}/5';
  }

  void _showAddMethodDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Ongeraho Uburyo bwo Kurinda Inda'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Hitamo uburyo bushya bwo kurinda inda:'),
                  const SizedBox(height: 16),
                  ListTile(
                    leading: Icon(
                      Icons.medication_rounded,
                      color: AppTheme.secondaryColor,
                    ),
                    title: const Text('Ibiyobyabwenge (Pills)'),
                    subtitle: const Text('91% ubushobozi'),
                    onTap: () => _addMethod('pill'),
                  ),
                  ListTile(
                    leading: Icon(
                      Icons.medical_services_rounded,
                      color: AppTheme.secondaryColor,
                    ),
                    title: const Text('IUD'),
                    subtitle: const Text('99% ubushobozi'),
                    onTap: () => _addMethod('iud'),
                  ),
                  ListTile(
                    leading: Icon(
                      Icons.healing_rounded,
                      color: AppTheme.secondaryColor,
                    ),
                    title: const Text('Implant'),
                    subtitle: const Text('99% ubushobozi'),
                    onTap: () => _addMethod('implant'),
                  ),
                  ListTile(
                    leading: Icon(
                      Icons.vaccines_rounded,
                      color: AppTheme.secondaryColor,
                    ),
                    title: const Text('Urushinge (Injection)'),
                    subtitle: const Text('94% ubushobozi'),
                    onTap: () => _addMethod('injection'),
                  ),
                  ListTile(
                    leading: Icon(
                      Icons.shield_rounded,
                      color: AppTheme.secondaryColor,
                    ),
                    title: const Text('Kondomu'),
                    subtitle: const Text('85% ubushobozi'),
                    onTap: () => _addMethod('condom'),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Siga'),
              ),
            ],
          ),
    );
  }

  void _addMethod(String methodType) {
    Navigator.of(context).pop();
    // Here you would typically show a detailed form to add the method
    // For now, we'll show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Uburyo bwo kurinda inda bwongerewe: $methodType'),
        backgroundColor: AppTheme.successColor,
      ),
    );
    // Reload data to show the new method
    _loadContraceptionData();
  }
}
