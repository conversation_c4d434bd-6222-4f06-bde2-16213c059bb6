import 'package:flutter/material.dart';
import '../models/health_record_model.dart';

class HealthStatusUtils {
  // Health Status Evaluation Methods
  static Map<String, dynamic> evaluateHeartRateStatus(int? heartRate) {
    if (heartRate == null || heartRate == 0) {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': Colors.grey,
        'colorCode': 0xFF9E9E9E,
      };
    }

    if (heartRate >= 60 && heartRate <= 100) {
      return {
        'status': 'normal',
        'message': 'Normal',
        'color': Colors.green,
        'colorCode': 0xFF4CAF50,
      };
    } else if ((heartRate >= 50 && heartRate < 60) ||
        (heartRate > 100 && heartRate <= 120)) {
      return {
        'status': 'caution',
        'message': 'Iyiteho',
        'color': Colors.orange,
        'colorCode': 0xFFFF9800,
      };
    } else {
      return {
        'status': 'critical',
        'message': 'Urarwaye',
        'color': Colors.red,
        'colorCode': 0xFFF44336,
      };
    }
  }

  static Map<String, dynamic> evaluateBloodPressureStatus(String? bpValue) {
    if (bpValue == null || bpValue.isEmpty || bpValue == '0/0') {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': Colors.grey,
        'colorCode': 0xFF9E9E9E,
      };
    }

    // Parse blood pressure string "120/80"
    List<String> bpParts = bpValue.split('/');
    if (bpParts.length != 2) {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': Colors.grey,
        'colorCode': 0xFF9E9E9E,
      };
    }

    int? systolic = int.tryParse(bpParts[0]);
    int? diastolic = int.tryParse(bpParts[1]);

    if (systolic == null || diastolic == null) {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': Colors.grey,
        'colorCode': 0xFF9E9E9E,
      };
    }

    // Normal: Systolic < 120 AND Diastolic < 80
    if (systolic < 120 && diastolic < 80) {
      return {
        'status': 'normal',
        'message': 'Normal',
        'color': Colors.green,
        'colorCode': 0xFF4CAF50,
      };
    }
    // Elevated/Pre-hypertension: Systolic 120-139 OR Diastolic 80-89
    else if ((systolic >= 120 && systolic <= 139) ||
        (diastolic >= 80 && diastolic <= 89)) {
      return {
        'status': 'caution',
        'message': 'Iyiteho',
        'color': Colors.orange,
        'colorCode': 0xFFFF9800,
      };
    }
    // High/Critical: Systolic >= 140 OR Diastolic >= 90
    else {
      return {
        'status': 'critical',
        'message': 'Urarwaye',
        'color': Colors.red,
        'colorCode': 0xFFF44336,
      };
    }
  }

  static Map<String, dynamic> evaluateTemperatureStatus(double? temperature) {
    if (temperature == null || temperature == 0) {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': Colors.grey,
        'colorCode': 0xFF9E9E9E,
      };
    }

    // Normal: 36.1°C - 37.2°C
    if (temperature >= 36.1 && temperature <= 37.2) {
      return {
        'status': 'normal',
        'message': 'Normal',
        'color': Colors.green,
        'colorCode': 0xFF4CAF50,
      };
    }
    // Caution: Low fever 37.3°C - 38.0°C or slightly low 35.5°C - 36.0°C
    else if ((temperature >= 37.3 && temperature <= 38.0) ||
        (temperature >= 35.5 && temperature < 36.1)) {
      return {
        'status': 'caution',
        'message': 'Iyiteho',
        'color': Colors.orange,
        'colorCode': 0xFFFF9800,
      };
    }
    // Critical: High fever > 38.0°C or hypothermia < 35.5°C
    else {
      return {
        'status': 'critical',
        'message': 'Urarwaye',
        'color': Colors.red,
        'colorCode': 0xFFF44336,
      };
    }
  }

  static Map<String, dynamic> evaluateWeightStatus(double? weight, double? height) {
    if (weight == null || height == null || weight == 0 || height == 0) {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': Colors.grey,
        'colorCode': 0xFF9E9E9E,
      };
    }

    // Calculate BMI
    double heightInMeters = height / 100;
    double bmi = weight / (heightInMeters * heightInMeters);

    // Normal BMI: 18.5 - 24.9
    if (bmi >= 18.5 && bmi <= 24.9) {
      return {
        'status': 'normal',
        'message': 'Normal',
        'color': Colors.green,
        'colorCode': 0xFF4CAF50,
      };
    }
    // Caution: Underweight (< 18.5) or Overweight (25.0 - 29.9)
    else if (bmi < 18.5 || (bmi >= 25.0 && bmi <= 29.9)) {
      return {
        'status': 'caution',
        'message': 'Iyiteho',
        'color': Colors.orange,
        'colorCode': 0xFFFF9800,
      };
    }
    // Critical: Obese (>= 30.0)
    else {
      return {
        'status': 'critical',
        'message': 'Urarwaye',
        'color': Colors.red,
        'colorCode': 0xFFF44336,
      };
    }
  }

  // Overall health status based on all metrics
  static Map<String, dynamic> evaluateOverallHealthStatus(HealthRecord? record) {
    if (record == null) {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': Colors.grey,
        'colorCode': 0xFF9E9E9E,
      };
    }

    List<String> statuses = [];

    // Check heart rate
    if (record.heartRateValue > 0) {
      statuses.add(evaluateHeartRateStatus(record.heartRateValue)['status']);
    }

    // Check blood pressure
    if (record.bpValue.isNotEmpty && record.bpValue != '0/0') {
      statuses.add(evaluateBloodPressureStatus(record.bpValue)['status']);
    }

    // Check temperature
    if (record.tempValue > 0) {
      statuses.add(evaluateTemperatureStatus(record.tempValue)['status']);
    }

    // Check weight/BMI
    if (record.kgValue > 0 && record.heightValue > 0) {
      statuses.add(
        evaluateWeightStatus(record.kgValue, record.heightValue)['status'],
      );
    }

    // Determine overall status
    if (statuses.contains('critical')) {
      return {
        'status': 'critical',
        'message': 'Urarwaye',
        'color': Colors.red,
        'colorCode': 0xFFF44336,
      };
    } else if (statuses.contains('caution')) {
      return {
        'status': 'caution',
        'message': 'Iyiteho',
        'color': Colors.orange,
        'colorCode': 0xFFFF9800,
      };
    } else if (statuses.contains('normal')) {
      return {
        'status': 'normal',
        'message': 'Normal',
        'color': Colors.green,
        'colorCode': 0xFF4CAF50,
      };
    } else {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': Colors.grey,
        'colorCode': 0xFF9E9E9E,
      };
    }
  }

  // Helper method to get status color
  static Color getStatusColor(String status) {
    switch (status) {
      case 'normal':
        return Colors.green;
      case 'caution':
        return Colors.orange;
      case 'critical':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // Helper method to get status message in Kinyarwanda
  static String getStatusMessage(String status) {
    switch (status) {
      case 'normal':
        return 'Normal';
      case 'caution':
        return 'Iyiteho';
      case 'critical':
        return 'Urarwaye';
      default:
        return 'Ntabwo bihari';
    }
  }
}
