// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pregnancy_plan_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PregnancyPlan _$PregnancyPlanFromJson(Map<String, dynamic> json) =>
    PregnancyPlan(
      id: json['id'] as String,
      userId: json['userId'] as String,
      planName: json['planName'] as String,
      targetDate: DateTime.parse(json['targetDate'] as String),
      targetConceptionDate:
          json['targetConceptionDate'] == null
              ? null
              : DateTime.parse(json['targetConceptionDate'] as String),
      description: json['description'] as String?,
      status: $enumDecodeNullable(_$PregnancyPlanStatusEnumMap, json['status']),
      currentStatus: json['currentStatus'] as String,
      partnerInvolvement: json['partnerInvolvement'] as String?,
      preparationSteps:
          (json['preparationSteps'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      healthChecks: (json['healthChecks'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as bool),
      ),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt:
          json['updatedAt'] == null
              ? null
              : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PregnancyPlanToJson(PregnancyPlan instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'planName': instance.planName,
      'targetDate': instance.targetDate.toIso8601String(),
      'targetConceptionDate': instance.targetConceptionDate.toIso8601String(),
      'description': instance.description,
      'status': _$PregnancyPlanStatusEnumMap[instance.status]!,
      'currentStatus': instance.currentStatus,
      'partnerInvolvement': instance.partnerInvolvement,
      'preparationSteps': instance.preparationSteps,
      'healthChecks': instance.healthChecks,
      'notes': instance.notes,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$PregnancyPlanStatusEnumMap = {
  PregnancyPlanStatus.planning: 'planning',
  PregnancyPlanStatus.trying: 'trying',
  PregnancyPlanStatus.conceived: 'conceived',
  PregnancyPlanStatus.postponed: 'postponed',
  PregnancyPlanStatus.cancelled: 'cancelled',
};
