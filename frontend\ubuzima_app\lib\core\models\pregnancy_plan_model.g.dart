// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pregnancy_plan_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PregnancyPlan _$PregnancyPlanFromJson(Map<String, dynamic> json) =>
    PregnancyPlan(
      id: json['id'] as String,
      userId: json['userId'] as String,
      planName: json['planName'] as String,
      targetDate: DateTime.parse(json['targetDate'] as String),
      description: json['description'] as String?,
      currentStatus: json['currentStatus'] as String,
      partnerInvolvement: json['partnerInvolvement'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt:
          json['updatedAt'] == null
              ? null
              : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PregnancyPlanToJson(PregnancyPlan instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'planName': instance.planName,
      'targetDate': instance.targetDate.toIso8601String(),
      'description': instance.description,
      'currentStatus': instance.currentStatus,
      'partnerInvolvement': instance.partnerInvolvement,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
