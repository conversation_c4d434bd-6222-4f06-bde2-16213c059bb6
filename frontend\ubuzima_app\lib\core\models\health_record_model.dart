// User-centric health record model matching backend structure
class HealthRecord {
  final String id;
  final String userId;

  // Heart rate fields
  final int? heartRateValue;
  final String? heartRateUnit;

  // Blood pressure fields
  final String? bpValue;
  final String? bpUnit;

  // Weight fields
  final double? kgValue;
  final String? kgUnit;

  // Temperature fields
  final double? tempValue;
  final String? tempUnit;

  // Height fields
  final double? heightValue;
  final String? heightUnit;

  // Computed fields
  final double? bmi;
  final String? healthStatus;

  // Metadata
  final String? notes;
  final bool isVerified;
  final String? recordedBy;
  final String? assignedHealthWorkerId;
  final DateTime? lastUpdated;
  final DateTime createdAt;
  final DateTime updatedAt;

  HealthRecord({
    required this.id,
    required this.userId,
    this.heartRateValue,
    this.heartRateUnit,
    this.bpValue,
    this.bpUnit,
    this.kgValue,
    this.kgUnit,
    this.tempValue,
    this.tempUnit,
    this.heightValue,
    this.heightUnit,
    this.bmi,
    this.healthStatus,
    this.notes,
    this.isVerified = false,
    this.recordedBy,
    this.assignedHealthWorkerId,
    this.lastUpdated,
    required this.createdAt,
    required this.updatedAt,
  });

  factory HealthRecord.fromJson(Map<String, dynamic> json) {
    return HealthRecord(
      id: json['id']?.toString() ?? '',
      userId:
          json['user']?['id']?.toString() ?? json['userId']?.toString() ?? '',

      // Heart rate fields
      heartRateValue:
          json['heartRateValue'] != null
              ? int.tryParse(json['heartRateValue'].toString())
              : null,
      heartRateUnit: json['heartRateUnit']?.toString() ?? 'bpm',

      // Blood pressure fields
      bpValue: json['bpValue']?.toString(),
      bpUnit: json['bpUnit']?.toString() ?? 'mmHg',

      // Weight fields
      kgValue:
          json['kgValue'] != null
              ? double.tryParse(json['kgValue'].toString())
              : null,
      kgUnit: json['kgUnit']?.toString() ?? 'kg',

      // Temperature fields
      tempValue:
          json['tempValue'] != null
              ? double.tryParse(json['tempValue'].toString())
              : null,
      tempUnit: json['tempUnit']?.toString() ?? '°C',

      // Height fields
      heightValue:
          json['heightValue'] != null
              ? double.tryParse(json['heightValue'].toString())
              : null,
      heightUnit: json['heightUnit']?.toString() ?? 'cm',

      // Computed fields
      bmi: json['bmi'] != null ? double.tryParse(json['bmi'].toString()) : null,
      healthStatus: json['healthStatus']?.toString() ?? 'normal',

      // Metadata
      notes: json['notes']?.toString(),
      isVerified: json['isVerified'] ?? false,
      recordedBy: json['recordedBy']?.toString(),
      assignedHealthWorkerId: json['assignedHealthWorker']?['id']?.toString(),
      lastUpdated:
          json['lastUpdated'] != null
              ? DateTime.tryParse(json['lastUpdated'])
              : null,
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt'] ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,

      // Heart rate fields
      'heartRateValue': heartRateValue,
      'heartRateUnit': heartRateUnit,

      // Blood pressure fields
      'bpValue': bpValue,
      'bpUnit': bpUnit,

      // Weight fields
      'kgValue': kgValue,
      'kgUnit': kgUnit,

      // Temperature fields
      'tempValue': tempValue,
      'tempUnit': tempUnit,

      // Height fields
      'heightValue': heightValue,
      'heightUnit': heightUnit,

      // Computed fields
      'bmi': bmi,
      'healthStatus': healthStatus,

      // Metadata
      'notes': notes,
      'isVerified': isVerified,
      'recordedBy': recordedBy,
      'assignedHealthWorkerId': assignedHealthWorkerId,
      'lastUpdated': lastUpdated?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Convenience methods for easier access
  int? get systolic {
    if (bpValue != null && bpValue!.contains('/')) {
      try {
        return int.parse(bpValue!.split('/')[0]);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  int? get diastolic {
    if (bpValue != null && bpValue!.contains('/')) {
      try {
        return int.parse(bpValue!.split('/')[1]);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  void setBloodPressure(int systolic, int diastolic) {
    // This would be used in a mutable version of the class
    // For now, it's just a helper method signature
  }

  // Check if any vital signs are present
  bool get hasVitalSigns {
    return heartRateValue != null ||
        bpValue != null ||
        kgValue != null ||
        tempValue != null;
  }

  // Get a summary of available metrics
  List<String> get availableMetrics {
    List<String> metrics = [];
    if (heartRateValue != null) metrics.add('Heart Rate');
    if (bpValue != null) metrics.add('Blood Pressure');
    if (kgValue != null) metrics.add('Weight');
    if (tempValue != null) metrics.add('Temperature');
    if (heightValue != null) metrics.add('Height');
    return metrics;
  }
}

class MenstrualCycle {
  final String id;
  final String userId;
  final DateTime startDate;
  final DateTime? endDate;
  final int cycleLength;
  final int flowDuration;
  final FlowIntensity flowIntensity;
  final List<String> symptoms;
  final String? notes;
  final bool isPredicted;
  final DateTime createdAt;
  final DateTime updatedAt;

  MenstrualCycle({
    required this.id,
    required this.userId,
    required this.startDate,
    this.endDate,
    required this.cycleLength,
    required this.flowDuration,
    required this.flowIntensity,
    this.symptoms = const [],
    this.notes,
    this.isPredicted = false,
    required this.createdAt,
    required this.updatedAt,
  });

  DateTime get nextPeriodDate => startDate.add(Duration(days: cycleLength));
  DateTime get ovulationDate => startDate.add(Duration(days: cycleLength ~/ 2));
  DateTime get fertileWindowStart =>
      ovulationDate.subtract(const Duration(days: 5));
  DateTime get fertileWindowEnd => ovulationDate.add(const Duration(days: 1));
}

class Medication {
  final String id;
  final String userId;
  final String name;
  final String dosage;
  final String frequency;
  final DateTime startDate;
  final DateTime? endDate;
  final String prescribedBy;
  final String purpose;
  final String? instructions;
  final List<String> sideEffects;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Medication({
    required this.id,
    required this.userId,
    required this.name,
    required this.dosage,
    required this.frequency,
    required this.startDate,
    this.endDate,
    required this.prescribedBy,
    required this.purpose,
    this.instructions,
    this.sideEffects = const [],
    this.isActive = true,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });
}

class HealthWorker {
  final String id;
  final String name;
  final String specialization;
  final String facilityId;
  final String phone;
  final String email;
  final List<String> qualifications;
  final List<String> languages;
  final bool isAvailable;
  final bool isActive;
  final double rating;
  final DateTime createdAt;
  final DateTime updatedAt;

  HealthWorker({
    required this.id,
    required this.name,
    required this.specialization,
    required this.facilityId,
    required this.phone,
    required this.email,
    this.qualifications = const [],
    this.languages = const [],
    this.isAvailable = true,
    this.isActive = true,
    this.rating = 0.0,
    required this.createdAt,
    required this.updatedAt,
  });
}

class EducationContent {
  final String id;
  final String title;
  final String content;
  final String category;
  final List<String> tags;
  final String language;
  final String? audioUrl;
  final String? videoUrl;
  final List<String> images;
  final int readingTime;
  final bool isPublished;
  final DateTime createdAt;
  final DateTime updatedAt;

  EducationContent({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    this.tags = const [],
    required this.language,
    this.audioUrl,
    this.videoUrl,
    this.images = const [],
    required this.readingTime,
    this.isPublished = true,
    required this.createdAt,
    required this.updatedAt,
  });
}

class UserProgress {
  final String id;
  final String userId;
  final String contentId;
  final double progressPercentage;
  final DateTime lastAccessed;
  final bool isCompleted;
  final int timeSpent;
  final Map<String, dynamic> quizResults;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProgress({
    required this.id,
    required this.userId,
    required this.contentId,
    required this.progressPercentage,
    required this.lastAccessed,
    this.isCompleted = false,
    this.timeSpent = 0,
    this.quizResults = const {},
    required this.createdAt,
    required this.updatedAt,
  });
}

class Message {
  final String id;
  final String senderId;
  final String receiverId;
  final String content;
  final MessageType type;
  final DateTime sentAt;
  final DateTime? readAt;
  final bool isDelivered;
  final String? attachmentUrl;
  final MessagePriority priority;
  final String? replyToId;
  final DateTime createdAt;

  Message({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    required this.type,
    required this.sentAt,
    this.readAt,
    this.isDelivered = false,
    this.attachmentUrl,
    this.priority = MessagePriority.normal,
    this.replyToId,
    required this.createdAt,
  });
}

// Enums
enum FlowIntensity { light, normal, heavy }

enum MessageType { text, voice, image, audio, video, document, location }

enum MessagePriority { low, normal, high, urgent }

enum HealthRecordType {
  consultation,
  vaccination,
  labResult,
  prescription,
  vitalSigns,
  familyPlanning,
  pregnancy,
  prenatalCare,
  menstrualCycle,
  contraception,
}
