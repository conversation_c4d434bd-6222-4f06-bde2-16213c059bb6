// Complete health record model matching backend structure with all fields
class HealthRecord {
  // BaseEntity fields
  final String id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int version;

  // User relationship
  final String userId;
  final String userName;
  final String userEmail;

  // Heart rate fields (required with defaults)
  final int heartRateValue;
  final String heartRateUnit;

  // Blood pressure fields (required with defaults)
  final String bpValue;
  final String bpUnit;

  // Weight fields (required with defaults)
  final double kgValue;
  final String kgUnit;

  // Temperature fields (required with defaults)
  final double tempValue;
  final String tempUnit;

  // Height fields (required with defaults)
  final double heightValue;
  final String heightUnit;

  // Computed fields (required with defaults)
  final double bmi;
  final String healthStatus;

  // Metadata fields (required with defaults)
  final String notes;
  final bool isVerified;
  final String recordedBy;
  final String assignedHealthWorkerId;
  final String assignedHealthWorkerName;
  final DateTime lastUpdated;

  HealthRecord({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    this.version = 0,
    required this.userId,
    this.userName = '',
    this.userEmail = '',
    this.heartRateValue = 0,
    this.heartRateUnit = 'bpm',
    this.bpValue = '0/0',
    this.bpUnit = 'mmHg',
    this.kgValue = 0.0,
    this.kgUnit = 'kg',
    this.tempValue = 0.0,
    this.tempUnit = '°C',
    this.heightValue = 0.0,
    this.heightUnit = 'cm',
    this.bmi = 0.0,
    this.healthStatus = 'normal',
    this.notes = '',
    this.isVerified = false,
    this.recordedBy = '',
    this.assignedHealthWorkerId = '',
    this.assignedHealthWorkerName = '',
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  factory HealthRecord.fromJson(Map<String, dynamic> json) {
    return HealthRecord(
      // BaseEntity fields
      id: json['id']?.toString() ?? '0',
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt'] ?? '') ?? DateTime.now(),
      version:
          json['version'] != null
              ? int.tryParse(json['version'].toString()) ?? 0
              : 0,

      // User relationship
      userId:
          json['user']?['id']?.toString() ?? json['userId']?.toString() ?? '0',
      userName:
          json['user']?['name']?.toString() ??
          json['userName']?.toString() ??
          '',
      userEmail:
          json['user']?['email']?.toString() ??
          json['userEmail']?.toString() ??
          '',

      // Heart rate fields (with defaults)
      heartRateValue:
          json['heartRateValue'] != null
              ? int.tryParse(json['heartRateValue'].toString()) ?? 0
              : 0,
      heartRateUnit: json['heartRateUnit']?.toString() ?? 'bpm',

      // Blood pressure fields (with defaults)
      bpValue: json['bpValue']?.toString() ?? '0/0',
      bpUnit: json['bpUnit']?.toString() ?? 'mmHg',

      // Weight fields (with defaults)
      kgValue:
          json['kgValue'] != null
              ? double.tryParse(json['kgValue'].toString()) ?? 0.0
              : 0.0,
      kgUnit: json['kgUnit']?.toString() ?? 'kg',

      // Temperature fields (with defaults)
      tempValue:
          json['tempValue'] != null
              ? double.tryParse(json['tempValue'].toString()) ?? 0.0
              : 0.0,
      tempUnit: json['tempUnit']?.toString() ?? '°C',

      // Height fields (with defaults)
      heightValue:
          json['heightValue'] != null
              ? double.tryParse(json['heightValue'].toString()) ?? 0.0
              : 0.0,
      heightUnit: json['heightUnit']?.toString() ?? 'cm',

      // Computed fields (with defaults)
      bmi:
          json['bmi'] != null
              ? double.tryParse(json['bmi'].toString()) ?? 0.0
              : 0.0,
      healthStatus: json['healthStatus']?.toString() ?? 'normal',

      // Metadata (with defaults)
      notes: json['notes']?.toString() ?? '',
      isVerified: json['isVerified'] ?? false,
      recordedBy: json['recordedBy']?.toString() ?? '',
      assignedHealthWorkerId:
          json['assignedHealthWorker']?['id']?.toString() ??
          json['assignedHealthWorkerId']?.toString() ??
          '',
      assignedHealthWorkerName:
          json['assignedHealthWorker']?['name']?.toString() ??
          json['assignedHealthWorkerName']?.toString() ??
          '',
      lastUpdated:
          json['lastUpdated'] != null
              ? DateTime.tryParse(json['lastUpdated']) ?? DateTime.now()
              : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // BaseEntity fields
      'id': id,
      'version': version,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),

      // User relationship
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,

      // Heart rate fields
      'heartRateValue': heartRateValue,
      'heartRateUnit': heartRateUnit,

      // Blood pressure fields
      'bpValue': bpValue,
      'bpUnit': bpUnit,

      // Weight fields
      'kgValue': kgValue,
      'kgUnit': kgUnit,

      // Temperature fields
      'tempValue': tempValue,
      'tempUnit': tempUnit,

      // Height fields
      'heightValue': heightValue,
      'heightUnit': heightUnit,

      // Computed fields
      'bmi': bmi,
      'healthStatus': healthStatus,

      // Metadata
      'notes': notes,
      'isVerified': isVerified,
      'recordedBy': recordedBy,
      'assignedHealthWorkerId': assignedHealthWorkerId,
      'assignedHealthWorkerName': assignedHealthWorkerName,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  // Convenience methods for easier access
  int? get systolic {
    if (bpValue.isNotEmpty && bpValue.contains('/')) {
      try {
        return int.parse(bpValue.split('/')[0]);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  int? get diastolic {
    if (bpValue.isNotEmpty && bpValue.contains('/')) {
      try {
        return int.parse(bpValue.split('/')[1]);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  void setBloodPressure(int systolic, int diastolic) {
    // This would be used in a mutable version of the class
    // For now, it's just a helper method signature
  }

  // Check if any vital signs are present (non-zero values)
  bool get hasVitalSigns {
    return heartRateValue > 0 ||
        (bpValue.isNotEmpty && bpValue != '0/0') ||
        kgValue > 0 ||
        tempValue > 0;
  }

  // Check if any measurements are present (non-zero values)
  bool get hasMeasurements {
    return hasVitalSigns || heightValue > 0 || bmi > 0;
  }

  // Get a summary of available metrics (non-zero values)
  List<String> get availableMetrics {
    List<String> metrics = [];
    if (heartRateValue > 0) metrics.add('Heart Rate');
    if (bpValue.isNotEmpty && bpValue != '0/0') metrics.add('Blood Pressure');
    if (kgValue > 0) metrics.add('Weight');
    if (tempValue > 0) metrics.add('Temperature');
    if (heightValue > 0) metrics.add('Height');
    if (bmi > 0) metrics.add('BMI');
    return metrics;
  }

  // Check if record has complete vital signs
  bool get hasCompleteVitals {
    return heartRateValue > 0 &&
        bpValue.isNotEmpty &&
        bpValue != '0/0' &&
        kgValue > 0 &&
        tempValue > 0;
  }

  // Get health status color
  String get healthStatusColor {
    switch (healthStatus.toLowerCase()) {
      case 'critical':
        return 'red';
      case 'concerning':
        return 'orange';
      case 'normal':
        return 'green';
      default:
        return 'gray';
    }
  }

  // Create a copy with updated values
  HealthRecord copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? version,
    String? userId,
    String? userName,
    String? userEmail,
    int? heartRateValue,
    String? heartRateUnit,
    String? bpValue,
    String? bpUnit,
    double? kgValue,
    String? kgUnit,
    double? tempValue,
    String? tempUnit,
    double? heightValue,
    String? heightUnit,
    double? bmi,
    String? healthStatus,
    String? notes,
    bool? isVerified,
    String? recordedBy,
    String? assignedHealthWorkerId,
    String? assignedHealthWorkerName,
    DateTime? lastUpdated,
  }) {
    return HealthRecord(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      version: version ?? this.version,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      heartRateValue: heartRateValue ?? this.heartRateValue,
      heartRateUnit: heartRateUnit ?? this.heartRateUnit,
      bpValue: bpValue ?? this.bpValue,
      bpUnit: bpUnit ?? this.bpUnit,
      kgValue: kgValue ?? this.kgValue,
      kgUnit: kgUnit ?? this.kgUnit,
      tempValue: tempValue ?? this.tempValue,
      tempUnit: tempUnit ?? this.tempUnit,
      heightValue: heightValue ?? this.heightValue,
      heightUnit: heightUnit ?? this.heightUnit,
      bmi: bmi ?? this.bmi,
      healthStatus: healthStatus ?? this.healthStatus,
      notes: notes ?? this.notes,
      isVerified: isVerified ?? this.isVerified,
      recordedBy: recordedBy ?? this.recordedBy,
      assignedHealthWorkerId:
          assignedHealthWorkerId ?? this.assignedHealthWorkerId,
      assignedHealthWorkerName:
          assignedHealthWorkerName ?? this.assignedHealthWorkerName,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

class MenstrualCycle {
  final String id;
  final String userId;
  final DateTime startDate;
  final DateTime? endDate;
  final int cycleLength;
  final int flowDuration;
  final FlowIntensity flowIntensity;
  final List<String> symptoms;
  final String? notes;
  final bool isPredicted;
  final DateTime createdAt;
  final DateTime updatedAt;

  MenstrualCycle({
    required this.id,
    required this.userId,
    required this.startDate,
    this.endDate,
    required this.cycleLength,
    required this.flowDuration,
    required this.flowIntensity,
    this.symptoms = const [],
    this.notes,
    this.isPredicted = false,
    required this.createdAt,
    required this.updatedAt,
  });

  DateTime get nextPeriodDate => startDate.add(Duration(days: cycleLength));
  DateTime get ovulationDate => startDate.add(Duration(days: cycleLength ~/ 2));
  DateTime get fertileWindowStart =>
      ovulationDate.subtract(const Duration(days: 5));
  DateTime get fertileWindowEnd => ovulationDate.add(const Duration(days: 1));
}

class Medication {
  final String id;
  final String userId;
  final String name;
  final String dosage;
  final String frequency;
  final DateTime startDate;
  final DateTime? endDate;
  final String prescribedBy;
  final String purpose;
  final String? instructions;
  final List<String> sideEffects;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Medication({
    required this.id,
    required this.userId,
    required this.name,
    required this.dosage,
    required this.frequency,
    required this.startDate,
    this.endDate,
    required this.prescribedBy,
    required this.purpose,
    this.instructions,
    this.sideEffects = const [],
    this.isActive = true,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });
}

class HealthWorker {
  final String id;
  final String name;
  final String specialization;
  final String facilityId;
  final String phone;
  final String email;
  final List<String> qualifications;
  final List<String> languages;
  final bool isAvailable;
  final bool isActive;
  final double rating;
  final DateTime createdAt;
  final DateTime updatedAt;

  HealthWorker({
    required this.id,
    required this.name,
    required this.specialization,
    required this.facilityId,
    required this.phone,
    required this.email,
    this.qualifications = const [],
    this.languages = const [],
    this.isAvailable = true,
    this.isActive = true,
    this.rating = 0.0,
    required this.createdAt,
    required this.updatedAt,
  });
}

class EducationContent {
  final String id;
  final String title;
  final String content;
  final String category;
  final List<String> tags;
  final String language;
  final String? audioUrl;
  final String? videoUrl;
  final List<String> images;
  final int readingTime;
  final bool isPublished;
  final DateTime createdAt;
  final DateTime updatedAt;

  EducationContent({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    this.tags = const [],
    required this.language,
    this.audioUrl,
    this.videoUrl,
    this.images = const [],
    required this.readingTime,
    this.isPublished = true,
    required this.createdAt,
    required this.updatedAt,
  });
}

class UserProgress {
  final String id;
  final String userId;
  final String contentId;
  final double progressPercentage;
  final DateTime lastAccessed;
  final bool isCompleted;
  final int timeSpent;
  final Map<String, dynamic> quizResults;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProgress({
    required this.id,
    required this.userId,
    required this.contentId,
    required this.progressPercentage,
    required this.lastAccessed,
    this.isCompleted = false,
    this.timeSpent = 0,
    this.quizResults = const {},
    required this.createdAt,
    required this.updatedAt,
  });
}

class Message {
  final String id;
  final String senderId;
  final String receiverId;
  final String content;
  final MessageType type;
  final DateTime sentAt;
  final DateTime? readAt;
  final bool isDelivered;
  final String? attachmentUrl;
  final MessagePriority priority;
  final String? replyToId;
  final DateTime createdAt;

  Message({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    required this.type,
    required this.sentAt,
    this.readAt,
    this.isDelivered = false,
    this.attachmentUrl,
    this.priority = MessagePriority.normal,
    this.replyToId,
    required this.createdAt,
  });
}

// Enums
enum FlowIntensity { light, normal, heavy }

enum MessageType { text, voice, image, audio, video, document, location }

enum MessagePriority { low, normal, high, urgent }

enum HealthRecordType {
  consultation,
  vaccination,
  labResult,
  prescription,
  vitalSigns,
  familyPlanning,
  pregnancy,
  prenatalCare,
  menstrualCycle,
  contraception,
}
