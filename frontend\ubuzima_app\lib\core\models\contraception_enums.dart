enum ContraceptionType {
  pill,
  iud,
  implant,
  injection,
  patch,
  ring,
  condom,
  diaphragm,
  spermicide,
  naturalFamilyPlanning,
  sterilization,
  emergency,
}

enum ReminderType {
  dailyPill,
  weeklyPatch,
  monthlyRing,
  quarterlyInjection,
  appointment,
  refill,
  sideEffectCheck,
}

extension ContraceptionTypeExtension on ContraceptionType {
  String get name {
    switch (this) {
      case ContraceptionType.pill:
        return '<PERSON>biyobyabwenge';
      case ContraceptionType.iud:
        return 'IUD';
      case ContraceptionType.implant:
        return 'Implant';
      case ContraceptionType.injection:
        return 'Urushinge';
      case ContraceptionType.patch:
        return 'Patch';
      case ContraceptionType.ring:
        return 'Ring';
      case ContraceptionType.condom:
        return 'Kondomu';
      case ContraceptionType.diaphragm:
        return 'Diaphragm';
      case ContraceptionType.spermicide:
        return 'Spermicide';
      case ContraceptionType.naturalFamilyPlanning:
        return 'Uburyo bwa kamere';
      case ContraceptionType.sterilization:
        return '<PERSON><PERSON>';
      case ContraceptionType.emergency:
        return '<PERSON><PERSON> by<PERSON>';
    }
  }

  double get effectiveness {
    switch (this) {
      case ContraceptionType.pill:
        return 91.0;
      case ContraceptionType.iud:
        return 99.0;
      case ContraceptionType.implant:
        return 99.0;
      case ContraceptionType.injection:
        return 94.0;
      case ContraceptionType.patch:
        return 91.0;
      case ContraceptionType.ring:
        return 91.0;
      case ContraceptionType.condom:
        return 85.0;
      case ContraceptionType.diaphragm:
        return 88.0;
      case ContraceptionType.spermicide:
        return 72.0;
      case ContraceptionType.naturalFamilyPlanning:
        return 76.0;
      case ContraceptionType.sterilization:
        return 99.0;
      case ContraceptionType.emergency:
        return 89.0;
    }
  }

  String get instructions {
    switch (this) {
      case ContraceptionType.pill:
        return 'Nywa buri munsi ku gihe kimwe';
      case ContraceptionType.iud:
        return 'Gusuzuma buri myaka 5';
      case ContraceptionType.implant:
        return 'Gusimbura nyuma y\'imyaka 3';
      case ContraceptionType.injection:
        return 'Urushinge buri mezi 3';
      case ContraceptionType.patch:
        return 'Guhindura buri cyumweru';
      case ContraceptionType.ring:
        return 'Gusimbura buri kwezi';
      case ContraceptionType.condom:
        return 'Gukoresha buri gihe';
      case ContraceptionType.diaphragm:
        return 'Gushyira mbere y\'imibonano';
      case ContraceptionType.spermicide:
        return 'Gukoresha mbere y\'imibonano';
      case ContraceptionType.naturalFamilyPlanning:
        return 'Gukurikirana imihango';
      case ContraceptionType.sterilization:
        return 'Uburyo buhoraho';
      case ContraceptionType.emergency:
        return 'Gukoresha mu masaha 72';
    }
  }
}
