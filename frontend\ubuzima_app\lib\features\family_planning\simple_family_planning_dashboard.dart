import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/family_planning_service.dart';
import '../education/education_screen.dart';
import '../appointments/appointment_booking_screen.dart';
import '../contraception/simple_contraception_screen.dart';

class SimpleFamilyPlanningDashboard extends StatefulWidget {
  const SimpleFamilyPlanningDashboard({super.key});

  @override
  State<SimpleFamilyPlanningDashboard> createState() =>
      _SimpleFamilyPlanningDashboardState();
}

class _SimpleFamilyPlanningDashboardState
    extends State<SimpleFamilyPlanningDashboard> {
  final FamilyPlanningService _familyPlanningService = FamilyPlanningService();
  Map<String, dynamic> _dashboardData = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);
    try {
      final data = await _familyPlanningService.getFamilyPlanningOverview();
      setState(() {
        _dashboardData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Habaye ikosa mu gufata amakuru: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            _buildAppBar(isTablet),
            if (_isLoading)
              const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              )
            else ...[
              _buildWelcomeSection(isTablet),
              _buildStatsSection(isTablet),
              _buildQuickActions(isTablet),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar(bool isTablet) {
    return SliverAppBar(
      expandedHeight: isTablet ? 120 : 100,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.primaryColor,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Gahunda yo Gushaka Inda',
          style: AppTheme.headingMedium.copyWith(
            color: Colors.white,
            fontSize: isTablet ? 24 : 20,
          ),
        ),
        centerTitle: true,
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh_rounded, color: Colors.white),
          onPressed: _loadDashboardData,
        ),
      ],
    );
  }

  Widget _buildWelcomeSection(bool isTablet) {
    return SliverToBoxAdapter(
      child: Container(
        margin: EdgeInsets.all(AppTheme.spacing16),
        padding: EdgeInsets.all(AppTheme.spacing20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.1),
              Colors.white,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          border: Border.all(
            color: AppTheme.primaryColor.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.family_restroom_rounded,
                  color: AppTheme.primaryColor,
                  size: isTablet ? 32 : 28,
                ),
                SizedBox(width: AppTheme.spacing12),
                Expanded(
                  child: Text(
                    'Murakaza neza ku gahunda yanyu yo gushaka inda',
                    style: AppTheme.headingMedium.copyWith(
                      fontSize: isTablet ? 22 : 18,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: AppTheme.spacing12),
            Text(
              'Dufasha mu gufata ibyemezo byiza ku buzima bw\'imyororokere n\'ubwiyunge bw\'umuryango.',
              style: AppTheme.bodyLarge.copyWith(
                color: Colors.black87,
                fontSize: isTablet ? 16 : 14,
              ),
            ),
          ],
        ),
      ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0),
    );
  }

  Widget _buildStatsSection(bool isTablet) {
    final stats = _dashboardData['stats'] as Map<String, dynamic>? ?? {};

    return SliverToBoxAdapter(
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: AppTheme.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ibipimo by\'Ubwiyunge',
              style: AppTheme.headingMedium.copyWith(
                fontSize: isTablet ? 20 : 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppTheme.spacing16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: AppTheme.spacing12,
              mainAxisSpacing: AppTheme.spacing12,
              childAspectRatio: isTablet ? 1.5 : 1.3,
              children: [
                _buildStatCard(
                  'Amategeko',
                  '${stats['totalPlans'] ?? 0}',
                  Icons.assignment_rounded,
                  AppTheme.primaryColor,
                  isTablet,
                ),
                _buildStatCard(
                  'Kurinda Inda',
                  '${stats['activeMethods'] ?? 0}',
                  Icons.shield_rounded,
                  AppTheme.secondaryColor,
                  isTablet,
                ),
                _buildStatCard(
                  'Imihango',
                  '${stats['cyclesTracked'] ?? 0}',
                  Icons.calendar_month_rounded,
                  AppTheme.accentColor,
                  isTablet,
                ),
                _buildStatCard(
                  'Amasomo',
                  '${stats['completedLessons'] ?? 0}',
                  Icons.school_rounded,
                  AppTheme.successColor,
                  isTablet,
                ),
              ],
            ),
          ],
        ),
      ).animate().fadeIn(duration: 800.ms, delay: 200.ms),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    bool isTablet,
  ) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: isTablet ? 32 : 28),
          SizedBox(height: AppTheme.spacing8),
          Text(
            value,
            style: AppTheme.headingLarge.copyWith(
              color: color,
              fontSize: isTablet ? 24 : 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing4),
          Text(
            title,
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.black87,
              fontSize: isTablet ? 14 : 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(bool isTablet) {
    final quickActions = [
      {
        'title': 'Kurinda Inda',
        'subtitle': 'Gukurikirana uburyo bwo kurinda inda',
        'icon': Icons.shield_rounded,
        'color': AppTheme.secondaryColor,
        'onTap':
            () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const SimpleContraceptionScreen(),
              ),
            ),
      },
      {
        'title': 'Kwiga',
        'subtitle': 'Amasomo y\'ubwiyunge',
        'icon': Icons.school_rounded,
        'color': AppTheme.successColor,
        'onTap':
            () => Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => const EducationScreen()),
            ),
      },
      {
        'title': 'Gahunda',
        'subtitle': 'Gahunda y\'ubuvuzi',
        'icon': Icons.event_rounded,
        'color': AppTheme.warningColor,
        'onTap':
            () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const AppointmentBookingScreen(),
              ),
            ),
      },
    ];

    return SliverToBoxAdapter(
      child: Container(
        margin: EdgeInsets.all(AppTheme.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ibikorwa Byihuse',
              style: AppTheme.headingMedium.copyWith(
                fontSize: isTablet ? 20 : 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppTheme.spacing16),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: isTablet ? 3 : 2,
                crossAxisSpacing: AppTheme.spacing12,
                mainAxisSpacing: AppTheme.spacing12,
                childAspectRatio: isTablet ? 1.2 : 1.1,
              ),
              itemCount: quickActions.length,
              itemBuilder: (context, index) {
                final action = quickActions[index];
                return _buildActionCard(action, isTablet, index);
              },
            ),
          ],
        ),
      ).animate().fadeIn(duration: 1000.ms, delay: 400.ms),
    );
  }

  Widget _buildActionCard(
    Map<String, dynamic> action,
    bool isTablet,
    int index,
  ) {
    return GestureDetector(
      onTap: action['onTap'] as VoidCallback?,
      child: Container(
            padding: EdgeInsets.all(AppTheme.spacing16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: (action['color'] as Color).withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  action['icon'] as IconData,
                  color: action['color'] as Color,
                  size: isTablet ? 32 : 28,
                ),
                SizedBox(height: AppTheme.spacing8),
                Text(
                  action['title'] as String,
                  style: AppTheme.headingSmall.copyWith(
                    color: action['color'] as Color,
                    fontSize: isTablet ? 16 : 14,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: AppTheme.spacing4),
                Text(
                  action['subtitle'] as String,
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.black54,
                    fontSize: isTablet ? 12 : 10,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          )
          .animate(delay: (index * 100).ms)
          .fadeIn(duration: 600.ms)
          .scale(begin: const Offset(0.8, 0.8)),
    );
  }
}
