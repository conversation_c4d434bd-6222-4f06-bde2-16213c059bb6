import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import '../models/health_record_model.dart';
import 'auth_service.dart';

/// Unified Health Record Service for User-Centric Health System
/// 
/// This service provides a clean interface for all health record operations
/// using the new user-centric backend approach where each user has one
/// comprehensive health record instead of multiple separate records.
class UnifiedHealthRecordService {
  static final UnifiedHealthRecordService _instance = UnifiedHealthRecordService._internal();
  factory UnifiedHealthRecordService() => _instance;
  UnifiedHealthRecordService._internal();

  final String baseUrl = AppConstants.baseUrl;
  final AuthService _authService = AuthService();

  Future<String?> _getAuthToken() async {
    return _authService.currentToken;
  }

  Future<String?> _getCurrentUserId() async {
    return _authService.currentUser?.id;
  }

  Map<String, String> _getHeaders([String? token]) {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  /// Get current user's health record
  Future<HealthRecord?> getCurrentUserHealthRecord() async {
    try {
      final token = await _getAuthToken();
      
      final uri = Uri.parse('$baseUrl/user-centric-health/record');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return HealthRecord.fromJson(data);
      } else if (response.statusCode == 404) {
        // No health record exists for this user yet
        return null;
      }

      throw Exception('Failed to load health record - Status: ${response.statusCode}');
    } catch (e) {
      debugPrint('Error loading current user health record: $e');
      return null;
    }
  }

  /// Get health record for a specific user (for health workers/admins)
  Future<HealthRecord?> getUserHealthRecord(String userId) async {
    try {
      final token = await _getAuthToken();
      
      final uri = Uri.parse('$baseUrl/user-centric-health/record/$userId');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return HealthRecord.fromJson(data);
      } else if (response.statusCode == 404) {
        // No health record exists for this user yet
        return null;
      }

      throw Exception('Failed to load health record - Status: ${response.statusCode}');
    } catch (e) {
      debugPrint('Error loading health record for user $userId: $e');
      return null;
    }
  }

  /// Add or update a health metric for the current user
  Future<HealthRecord?> addHealthMetric({
    required String metricType,
    required Map<String, dynamic> metricData,
  }) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/user-centric-health/record');

      Map<String, dynamic> requestBody = {
        'recordType': _mapToRecordType(metricType),
        'notes': metricData['notes'] ?? '',
      };

      // Handle different metric types
      switch (metricType.toLowerCase()) {
        case 'weight':
          requestBody['value'] = metricData['value'].toString();
          requestBody['unit'] = metricData['unit'] ?? 'kg';
          break;
        case 'height':
          requestBody['value'] = metricData['value'].toString();
          requestBody['unit'] = metricData['unit'] ?? 'cm';
          break;
        case 'temperature':
          requestBody['value'] = metricData['value'].toString();
          requestBody['unit'] = metricData['unit'] ?? '°C';
          break;
        case 'heart_rate':
          requestBody['value'] = metricData['value'].toString();
          requestBody['unit'] = metricData['unit'] ?? 'bpm';
          break;
        case 'blood_pressure':
          requestBody['systolic'] = metricData['systolic'];
          requestBody['diastolic'] = metricData['diastolic'];
          requestBody['unit'] = metricData['unit'] ?? 'mmHg';
          break;
        default:
          requestBody['value'] = metricData['value']?.toString() ?? '';
          requestBody['unit'] = metricData['unit'] ?? '';
          break;
      }

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return HealthRecord.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to add health metric');
      }
    } catch (e) {
      debugPrint('Error adding health metric: $e');
      rethrow;
    }
  }

  /// Add weight measurement
  Future<HealthRecord?> addWeight(double weight, {String unit = 'kg', String? notes}) async {
    return addHealthMetric(
      metricType: 'weight',
      metricData: {
        'value': weight,
        'unit': unit,
        'notes': notes,
      },
    );
  }

  /// Add height measurement
  Future<HealthRecord?> addHeight(double height, {String unit = 'cm', String? notes}) async {
    return addHealthMetric(
      metricType: 'height',
      metricData: {
        'value': height,
        'unit': unit,
        'notes': notes,
      },
    );
  }

  /// Add temperature measurement
  Future<HealthRecord?> addTemperature(double temperature, {String unit = '°C', String? notes}) async {
    return addHealthMetric(
      metricType: 'temperature',
      metricData: {
        'value': temperature,
        'unit': unit,
        'notes': notes,
      },
    );
  }

  /// Add heart rate measurement
  Future<HealthRecord?> addHeartRate(int heartRate, {String unit = 'bpm', String? notes}) async {
    return addHealthMetric(
      metricType: 'heart_rate',
      metricData: {
        'value': heartRate,
        'unit': unit,
        'notes': notes,
      },
    );
  }

  /// Add blood pressure measurement
  Future<HealthRecord?> addBloodPressure(
    int systolic,
    int diastolic, {
    String unit = 'mmHg',
    String? notes,
  }) async {
    return addHealthMetric(
      metricType: 'blood_pressure',
      metricData: {
        'systolic': systolic,
        'diastolic': diastolic,
        'unit': unit,
        'notes': notes,
      },
    );
  }

  /// Helper method to map frontend types to backend RecordType
  String _mapToRecordType(String frontendType) {
    switch (frontendType.toLowerCase()) {
      case 'weight':
        return 'WEIGHT';
      case 'height':
        return 'HEIGHT';
      case 'blood_pressure':
        return 'BLOOD_PRESSURE';
      case 'temperature':
        return 'TEMPERATURE';
      case 'heart_rate':
        return 'HEART_RATE';
      case 'menstrual_cycle':
        return 'MENSTRUAL_CYCLE';
      case 'pregnancy_test':
        return 'PREGNANCY_TEST';
      case 'contraceptive_use':
        return 'CONTRACEPTIVE_USE';
      case 'symptoms':
        return 'SYMPTOMS';
      case 'medication':
        return 'MEDICATION';
      case 'vaccination':
        return 'VACCINATION';
      case 'consultation_notes':
        return 'CONSULTATION_NOTES';
      default:
        return 'OTHER';
    }
  }

  /// For backward compatibility - returns list with single record or empty list
  Future<List<HealthRecord>> getHealthRecords({String? userId}) async {
    final record = userId != null 
        ? await getUserHealthRecord(userId)
        : await getCurrentUserHealthRecord();
    return record != null ? [record] : [];
  }
}
