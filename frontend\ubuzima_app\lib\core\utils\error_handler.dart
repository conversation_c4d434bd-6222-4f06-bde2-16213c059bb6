import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

enum ErrorType {
  network,
  authentication,
  validation,
  notFound,
  serverError,
  unknown,
}

class AppError {
  final ErrorType type;
  final String title;
  final String message;
  final String? technicalDetails;
  final DateTime timestamp;

  AppError({
    required this.type,
    required this.title,
    required this.message,
    this.technicalDetails,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory AppError.network({String? details}) {
    return AppError(
      type: ErrorType.network,
      title: 'Ikibazo cy\'Umuyoboro',
      message: 'Ntitwashoboye guhuza na seriveri. Suzuma umuyoboro wawe.',
      technicalDetails: details,
    );
  }

  factory AppError.authentication({String? details}) {
    return AppError(
      type: ErrorType.authentication,
      title: 'I<PERSON>bazo cy\'Kwinjira',
      message: 'Ntabwo wemerewe gukora iki gikorwa. Injira nanone.',
      technicalDetails: details,
    );
  }

  factory AppError.validation({required String field, String? details}) {
    return AppError(
      type: ErrorType.validation,
      title: 'Amakuru Atari Yo',
      message: 'Amakuru y\'$field ntabwo ari yo. Ongera ugerageze.',
      technicalDetails: details,
    );
  }

  factory AppError.notFound({String? resource, String? details}) {
    return AppError(
      type: ErrorType.notFound,
      title: 'Ntibisanzwe',
      message: '${resource ?? 'Amakuru'} asabwa ntabwo abonetse.',
      technicalDetails: details,
    );
  }

  factory AppError.serverError({String? details}) {
    return AppError(
      type: ErrorType.serverError,
      title: 'Ikosa ry\'Seriveri',
      message: 'Habaye ikosa ku seriveri. Ongera ugerageze nyuma.',
      technicalDetails: details,
    );
  }

  factory AppError.unknown({String? details}) {
    return AppError(
      type: ErrorType.unknown,
      title: 'Ikosa Ritazwi',
      message: 'Habaye ikosa ritazwi. Ongera ugerageze.',
      technicalDetails: details,
    );
  }

  factory AppError.fromException(dynamic exception) {
    final errorString = exception.toString().toLowerCase();
    
    if (errorString.contains('network') || errorString.contains('connection')) {
      return AppError.network(details: exception.toString());
    } else if (errorString.contains('401') || errorString.contains('unauthorized')) {
      return AppError.authentication(details: exception.toString());
    } else if (errorString.contains('404') || errorString.contains('not found')) {
      return AppError.notFound(details: exception.toString());
    } else if (errorString.contains('500') || errorString.contains('server')) {
      return AppError.serverError(details: exception.toString());
    } else {
      return AppError.unknown(details: exception.toString());
    }
  }

  Color get color {
    switch (type) {
      case ErrorType.network:
        return Colors.orange.shade600;
      case ErrorType.authentication:
        return Colors.red.shade600;
      case ErrorType.validation:
        return Colors.amber.shade600;
      case ErrorType.notFound:
        return Colors.blue.shade600;
      case ErrorType.serverError:
        return Colors.red.shade700;
      case ErrorType.unknown:
        return Colors.grey.shade600;
    }
  }

  IconData get icon {
    switch (type) {
      case ErrorType.network:
        return Icons.wifi_off_rounded;
      case ErrorType.authentication:
        return Icons.lock_rounded;
      case ErrorType.validation:
        return Icons.warning_rounded;
      case ErrorType.notFound:
        return Icons.search_off_rounded;
      case ErrorType.serverError:
        return Icons.error_rounded;
      case ErrorType.unknown:
        return Icons.help_outline_rounded;
    }
  }
}

class ErrorHandler {
  static void showError(BuildContext context, AppError error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(error.icon, color: error.color),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                error.title,
                style: TextStyle(color: error.color),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(error.message),
            if (error.technicalDetails != null) ...[
              const SizedBox(height: 16),
              ExpansionTile(
                title: const Text('Amakuru y\'Ubuhanga'),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      error.technicalDetails!,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Siga'),
          ),
          if (error.type == ErrorType.network)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Retry logic would go here
              },
              child: const Text('Ongera Ugerageze'),
            ),
        ],
      ),
    );
  }

  static void showSnackBar(BuildContext context, AppError error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(error.icon, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(error.message)),
          ],
        ),
        backgroundColor: error.color,
        duration: const Duration(seconds: 4),
        action: error.type == ErrorType.network
            ? SnackBarAction(
                label: 'Ongera',
                textColor: Colors.white,
                onPressed: () {
                  // Retry logic would go here
                },
              )
            : null,
      ),
    );
  }

  static Widget buildErrorWidget(AppError error, {VoidCallback? onRetry}) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            error.icon,
            size: 64,
            color: error.color,
          ),
          const SizedBox(height: 16),
          Text(
            error.title,
            style: AppTheme.headingMedium.copyWith(
              color: error.color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            error.message,
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('Ongera Ugerageze'),
              style: ElevatedButton.styleFrom(
                backgroundColor: error.color,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  static Future<T?> handleAsyncOperation<T>(
    BuildContext context,
    Future<T> operation, {
    bool showLoading = true,
    bool showErrorDialog = true,
    bool showErrorSnackBar = false,
  }) async {
    if (showLoading) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    try {
      final result = await operation;
      
      if (showLoading && context.mounted) {
        Navigator.of(context).pop();
      }
      
      return result;
    } catch (e) {
      if (showLoading && context.mounted) {
        Navigator.of(context).pop();
      }

      final error = AppError.fromException(e);
      
      if (context.mounted) {
        if (showErrorDialog) {
          showError(context, error);
        } else if (showErrorSnackBar) {
          showSnackBar(context, error);
        }
      }
      
      return null;
    }
  }

  static void logError(AppError error) {
    debugPrint('=== ERROR LOG ===');
    debugPrint('Type: ${error.type}');
    debugPrint('Title: ${error.title}');
    debugPrint('Message: ${error.message}');
    debugPrint('Timestamp: ${error.timestamp}');
    if (error.technicalDetails != null) {
      debugPrint('Technical Details: ${error.technicalDetails}');
    }
    debugPrint('================');
  }
}
