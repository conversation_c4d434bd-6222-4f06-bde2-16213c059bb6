name: ubuzima_app
description: "Ubuzima - Rwanda Family Planning Mobile Application"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.8
  google_fonts: ^6.1.0

  # Animations
  flutter_animate: ^4.3.0

  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # HTTP & API
  http: ^1.1.2
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Database & Storage
  sqflite: ^2.3.0
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.2

  # Database Connection (HTTP-based)
  # postgres: ^2.6.2  # Not needed for HTTP API approach
  # drift: ^2.14.1    # Not needed for simplified approach

  # Authentication & Security
  crypto: ^3.0.3
  encrypt: ^5.0.1
  local_auth: ^2.1.7

  # Location & Maps
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  google_maps_flutter: ^2.5.3
  location: ^5.0.3

  # Media & Files
  image_picker: ^1.0.7
  # file_picker: ^6.1.1
  cached_network_image: ^3.3.1
  permission_handler: ^11.2.0

  # Audio & Voice - Essential for voice-first architecture (Android compatible versions)
  # speech_to_text: ^6.3.0  # Temporarily disabled for Android compatibility
  flutter_tts: ^3.8.5     # Compatible with current Flutter
  audioplayers: ^5.2.1
  # record: ^6.0.0  # Temporarily disabled due to compatibility issues

  # Notifications
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  # flutter_local_notifications: ^16.3.2  # Temporarily disabled for Android build

  # Connectivity & Sync
  connectivity_plus: ^5.0.2
  internet_connection_checker: ^1.0.0+1

  # Utils
  intl: ^0.19.0
  uuid: ^4.3.3
  url_launcher: ^6.2.4
  package_info_plus: ^5.0.1
  device_info_plus: ^9.1.2
  path: ^1.8.3

  # Internationalization
  flutter_localizations:
    sdk: flutter

  # Charts & Analytics
  fl_chart: ^0.66.2
  syncfusion_flutter_charts: ^24.1.41

  # Video & Telemedicine
  agora_rtc_engine: ^6.3.2
  camera: ^0.10.5+9
  video_player: ^2.8.2

  # Additional Utils
  share_plus: ^7.2.2
  printing: ^5.12.0
  pdf: ^3.10.7
  csv: ^5.0.2
  web_socket_channel: ^2.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  mockito: ^5.4.2
  build_runner: ^2.4.7

  # Code Generation (not needed for simplified approach)
  # build_runner: ^2.4.7
  # drift_dev: ^2.14.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/audio/
