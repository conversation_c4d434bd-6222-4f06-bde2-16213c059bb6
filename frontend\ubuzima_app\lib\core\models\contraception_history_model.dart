import 'package:json_annotation/json_annotation.dart';
import 'contraception_enums.dart';

part 'contraception_history_model.g.dart';

@JsonSerializable()
class ContraceptionHistory {
  final String id;
  final String userId;
  final ContraceptionType methodType;
  final DateTime startDate;
  final DateTime? endDate;
  final String? reasonForStopping;
  final String? sideEffects;
  final int? satisfactionRating;
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ContraceptionHistory({
    required this.id,
    required this.userId,
    required this.methodType,
    required this.startDate,
    this.endDate,
    this.reasonForStopping,
    this.sideEffects,
    this.satisfactionRating,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  factory ContraceptionHistory.fromJson(Map<String, dynamic> json) =>
      _$ContraceptionHistoryFromJson(json);

  Map<String, dynamic> toJson() => _$ContraceptionHistoryToJson(this);
}
