import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import '../models/health_record_model.dart';
import 'auth_service.dart';

class HealthTrackingService {
  static final HealthTrackingService _instance =
      HealthTrackingService._internal();
  factory HealthTrackingService() => _instance;
  HealthTrackingService._internal();

  final String baseUrl = AppConstants.baseUrl;
  final AuthService _authService = AuthService();

  Future<String?> _getAuthToken() async {
    return _authService.currentToken;
  }

  Future<String?> _getCurrentUserId() async {
    return _authService.currentUser?.id;
  }

  Map<String, String> _getHeaders([String? token]) {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  // Get user's health record (user-centric approach)
  Future<HealthRecord?> getUserHealthRecord({String? userId}) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        debugPrint('User not authenticated - cannot load health record');
        return null;
      }

      final uri = Uri.parse('$baseUrl/health-records/record/$currentUserId');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        try {
          final data = json.decode(response.body);
          if (data['success'] == true) {
            return HealthRecord.fromJson(data['data']);
          }
        } catch (jsonError) {
          debugPrint('JSON parsing error in getUserHealthRecord: $jsonError');
          debugPrint('Response body: ${response.body}');
          throw Exception('Invalid JSON response from server');
        }
      } else if (response.statusCode == 404) {
        // User doesn't have a health record yet
        return null;
      }

      throw Exception(
        'Failed to load health record - Status: ${response.statusCode}',
      );
    } catch (e) {
      debugPrint('Error loading health record: $e');
      return null;
    }
  }

  // Get all users' health records (for admin/health worker)
  Future<List<HealthRecord>> getAllHealthRecords() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/health-records/all');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        try {
          final data = json.decode(response.body);
          if (data['success'] == true) {
            final List<dynamic> records = data['data'];
            return records.map((json) => HealthRecord.fromJson(json)).toList();
          }
        } catch (jsonError) {
          debugPrint('JSON parsing error in getAllHealthRecords: $jsonError');
          debugPrint('Response body: ${response.body}');
          throw Exception('Invalid JSON response from server');
        }
      }

      throw Exception(
        'Failed to load health records - Status: ${response.statusCode}',
      );
    } catch (e) {
      debugPrint('Error loading health records: $e');
      return [];
    }
  }

  // Update heart rate
  Future<HealthRecord?> updateHeartRate({
    String? userId,
    required int heartRateValue,
    String unit = 'bpm',
  }) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        debugPrint('User not authenticated - cannot update heart rate');
        return null;
      }

      final uri = Uri.parse('$baseUrl/health-records/heart-rate');
      final requestBody = {
        'userId': int.parse(currentUserId),
        'heartRateValue': heartRateValue,
        'unit': unit,
      };

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return HealthRecord.fromJson(data['data']);
        }
      }

      throw Exception('Failed to update heart rate');
    } catch (e) {
      debugPrint('Error updating heart rate: $e');
      return null;
    }
  }

  // Update blood pressure
  Future<HealthRecord?> updateBloodPressure({
    String? userId,
    required String bpValue,
    String unit = 'mmHg',
  }) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        debugPrint('User not authenticated - cannot update blood pressure');
        return null;
      }

      final uri = Uri.parse('$baseUrl/health-records/blood-pressure');
      final requestBody = {
        'userId': int.parse(currentUserId),
        'bpValue': bpValue,
        'unit': unit,
      };

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return HealthRecord.fromJson(data['data']);
        }
      }

      throw Exception('Failed to update blood pressure');
    } catch (e) {
      debugPrint('Error updating blood pressure: $e');
      return null;
    }
  }

  // Update weight
  Future<HealthRecord?> updateWeight({
    String? userId,
    required double kgValue,
    String unit = 'kg',
  }) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        debugPrint('User not authenticated - cannot update weight');
        return null;
      }

      final uri = Uri.parse('$baseUrl/health-records/weight');
      final requestBody = {
        'userId': int.parse(currentUserId),
        'kgValue': kgValue,
        'unit': unit,
      };

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return HealthRecord.fromJson(data['data']);
        }
      }

      throw Exception('Failed to update weight');
    } catch (e) {
      debugPrint('Error updating weight: $e');
      return null;
    }
  }

  // Update temperature
  Future<HealthRecord?> updateTemperature({
    String? userId,
    required double tempValue,
    String unit = '°C',
  }) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        debugPrint('User not authenticated - cannot update temperature');
        return null;
      }

      final uri = Uri.parse('$baseUrl/health-records/temperature');
      final requestBody = {
        'userId': int.parse(currentUserId),
        'tempValue': tempValue,
        'unit': unit,
      };

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return HealthRecord.fromJson(data['data']);
        }
      }

      throw Exception('Failed to update temperature');
    } catch (e) {
      debugPrint('Error updating temperature: $e');
      return null;
    }
  }

  // Update height
  Future<HealthRecord?> updateHeight({
    String? userId,
    required double heightValue,
    String unit = 'cm',
  }) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        debugPrint('User not authenticated - cannot update height');
        return null;
      }

      final uri = Uri.parse('$baseUrl/health-records/height');
      final requestBody = {
        'userId': int.parse(currentUserId),
        'heightValue': heightValue,
        'unit': unit,
      };

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return HealthRecord.fromJson(data['data']);
        }
      }

      throw Exception('Failed to update height');
    } catch (e) {
      debugPrint('Error updating height: $e');
      return null;
    }
  }

  // Create complete health record with all fields using multiple API calls
  Future<HealthRecord?> createCompleteHealthRecord({
    String? userId,
    int? heartRateValue,
    String? heartRateUnit,
    String? bpValue,
    String? bpUnit,
    double? kgValue,
    String? kgUnit,
    double? tempValue,
    String? tempUnit,
    double? heightValue,
    String? heightUnit,
    String? notes,
    String? recordedBy,
  }) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        debugPrint('User not authenticated - cannot create health record');
        return null;
      }

      // Use the multiple metrics endpoint to update all values at once
      final uri = Uri.parse('$baseUrl/health-records/multiple');
      final requestBody = <String, dynamic>{'userId': int.parse(currentUserId)};

      // Add all provided values
      if (heartRateValue != null && heartRateValue > 0) {
        requestBody['heartRate'] = heartRateValue;
        requestBody['heartRateUnit'] = heartRateUnit ?? 'bpm';
      }

      if (bpValue != null && bpValue.isNotEmpty && bpValue != '0/0') {
        requestBody['bloodPressure'] = bpValue;
        requestBody['bpUnit'] = bpUnit ?? 'mmHg';
      }

      if (kgValue != null && kgValue > 0) {
        requestBody['weight'] = kgValue;
        requestBody['weightUnit'] = kgUnit ?? 'kg';
      }

      if (tempValue != null && tempValue > 0) {
        requestBody['temperature'] = tempValue;
        requestBody['tempUnit'] = tempUnit ?? '°C';
      }

      // For height, we'll need to use a separate call since it's not in the multiple endpoint
      HealthRecord? result;

      if (requestBody.length > 1) {
        // More than just userId
        final response = await http.post(
          uri,
          headers: _getHeaders(token),
          body: json.encode(requestBody),
        );

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          if (data['success'] == true) {
            result = HealthRecord.fromJson(data['data']);
          }
        }
      }

      // Update height separately if provided
      if (heightValue != null && heightValue > 0) {
        result = await updateHeight(
          userId: currentUserId,
          heightValue: heightValue,
          unit: heightUnit ?? 'cm',
        );
      }

      // If no result yet, get the current record
      result ??= await getUserHealthRecord(userId: currentUserId);

      return result;
    } catch (e) {
      debugPrint('Error creating complete health record: $e');
      return null;
    }
  }

  // Update multiple health metrics at once
  Future<HealthRecord?> updateMultipleMetrics({
    String? userId,
    int? heartRate,
    String? heartRateUnit,
    String? bloodPressure,
    String? bpUnit,
    double? weight,
    String? weightUnit,
    double? temperature,
    String? tempUnit,
  }) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        debugPrint('User not authenticated - cannot update health metrics');
        return null;
      }

      final uri = Uri.parse('$baseUrl/health-records/multiple');
      final requestBody = <String, dynamic>{'userId': int.parse(currentUserId)};

      if (heartRate != null) {
        requestBody['heartRate'] = heartRate;
        requestBody['heartRateUnit'] = heartRateUnit ?? 'bpm';
      }
      if (bloodPressure != null) {
        requestBody['bloodPressure'] = bloodPressure;
        requestBody['bpUnit'] = bpUnit ?? 'mmHg';
      }
      if (weight != null) {
        requestBody['weight'] = weight;
        requestBody['weightUnit'] = weightUnit ?? 'kg';
      }
      if (temperature != null) {
        requestBody['temperature'] = temperature;
        requestBody['tempUnit'] = tempUnit ?? '°C';
      }

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return HealthRecord.fromJson(data['data']);
        }
      }

      throw Exception('Failed to update health metrics');
    } catch (e) {
      debugPrint('Error updating health metrics: $e');
      return null;
    }
  }

  // Backward compatibility methods
  @Deprecated('Use getUserHealthRecord() instead')
  Future<List<HealthRecord>> getHealthRecords({
    String? userId,
    String? recordType,
    int page = 0,
    int limit = 10,
  }) async {
    final healthRecord = await getUserHealthRecord(userId: userId);
    return healthRecord != null ? [healthRecord] : [];
  }

  @Deprecated('Use updateMultipleMetrics() or specific update methods instead')
  Future<HealthRecord?> createHealthRecord(
    Map<String, dynamic> recordData,
  ) async {
    // Convert old format to new format
    final recordType = recordData['recordType']?.toString().toUpperCase();
    final value = recordData['value']?.toString();
    final userId = recordData['userId']?.toString();

    switch (recordType) {
      case 'HEART_RATE':
        final heartRate = int.tryParse(value ?? '');
        if (heartRate != null) {
          return await updateHeartRate(
            userId: userId,
            heartRateValue: heartRate,
            unit: recordData['unit']?.toString() ?? 'bpm',
          );
        }
        break;
      case 'BLOOD_PRESSURE':
        if (value != null) {
          return await updateBloodPressure(
            userId: userId,
            bpValue: value,
            unit: recordData['unit']?.toString() ?? 'mmHg',
          );
        }
        break;
      case 'WEIGHT':
        final weight = double.tryParse(value ?? '');
        if (weight != null) {
          return await updateWeight(
            userId: userId,
            kgValue: weight,
            unit: recordData['unit']?.toString() ?? 'kg',
          );
        }
        break;
      case 'TEMPERATURE':
        final temp = double.tryParse(value ?? '');
        if (temp != null) {
          return await updateTemperature(
            userId: userId,
            tempValue: temp,
            unit: recordData['unit']?.toString() ?? '°C',
          );
        }
        break;
    }
    return null;
  }

  Future<Map<String, dynamic>?> getHealthStatistics({String? userId}) async {
    try {
      final healthRecord = await getUserHealthRecord(userId: userId);
      if (healthRecord == null) return null;

      // Generate statistics from the health record
      final stats = <String, dynamic>{
        'totalRecords': healthRecord.hasVitalSigns ? 1 : 0,
        'availableMetrics': healthRecord.availableMetrics,
        'healthStatus': healthRecord.healthStatus,
        'lastUpdated': healthRecord.lastUpdated.toIso8601String(),
        'hasCompleteVitals': healthRecord.hasCompleteVitals,
        'hasMeasurements': healthRecord.hasMeasurements,
      };

      // Always include all metrics (with 0 values if not set)
      stats['heartRate'] = {
        'current': healthRecord.heartRateValue,
        'unit': healthRecord.heartRateUnit,
        'isSet': healthRecord.heartRateValue > 0,
      };

      stats['bloodPressure'] = {
        'current': healthRecord.bpValue,
        'unit': healthRecord.bpUnit,
        'systolic': healthRecord.systolic,
        'diastolic': healthRecord.diastolic,
        'isSet':
            healthRecord.bpValue.isNotEmpty && healthRecord.bpValue != '0/0',
      };

      stats['weight'] = {
        'current': healthRecord.kgValue,
        'unit': healthRecord.kgUnit,
        'isSet': healthRecord.kgValue > 0,
      };

      stats['temperature'] = {
        'current': healthRecord.tempValue,
        'unit': healthRecord.tempUnit,
        'isSet': healthRecord.tempValue > 0,
      };

      stats['height'] = {
        'current': healthRecord.heightValue,
        'unit': healthRecord.heightUnit,
        'isSet': healthRecord.heightValue > 0,
      };

      stats['bmi'] = {
        'current': healthRecord.bmi,
        'isSet': healthRecord.bmi > 0,
      };

      // Additional metadata
      stats['user'] = {
        'id': healthRecord.userId,
        'name': healthRecord.userName,
        'email': healthRecord.userEmail,
      };

      stats['metadata'] = {
        'notes': healthRecord.notes,
        'isVerified': healthRecord.isVerified,
        'recordedBy': healthRecord.recordedBy,
        'assignedHealthWorker': {
          'id': healthRecord.assignedHealthWorkerId,
          'name': healthRecord.assignedHealthWorkerName,
        },
        'createdAt': healthRecord.createdAt.toIso8601String(),
        'updatedAt': healthRecord.updatedAt.toIso8601String(),
        'version': healthRecord.version,
      };

      return stats;
    } catch (e) {
      debugPrint('Error loading health statistics: $e');
      return null;
    }
  }

  // Menstrual Cycle
  Future<List<Map<String, dynamic>>> getMenstrualCycles({
    String? userId,
    int page = 0,
    int limit = 10,
  }) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        throw Exception('User ID not found');
      }

      final queryParams = {
        'userId': currentUserId,
        'page': page.toString(),
        'limit': limit.toString(),
      };

      final uri = Uri.parse(
        '$baseUrl/menstrual-cycles',
      ).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['cycles'] ?? []);
        }
      }

      throw Exception('Failed to load menstrual cycles');
    } catch (e) {
      debugPrint('Error loading menstrual cycles: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>?> getCurrentCycle({String? userId}) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        debugPrint('User not authenticated - cannot load current cycle');
        return null;
      }

      final queryParams = {'userId': currentUserId};

      final uri = Uri.parse(
        '$baseUrl/menstrual-cycles/current',
      ).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['currentCycle'];
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error loading current cycle: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> getCyclePredictions({String? userId}) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        debugPrint('User not authenticated - cannot load cycle predictions');
        return null;
      }

      final queryParams = {'userId': currentUserId};

      final uri = Uri.parse(
        '$baseUrl/menstrual-cycles/predictions',
      ).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['predictions'];
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error loading cycle predictions: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> createMenstrualCycle(
    Map<String, dynamic> cycleData,
  ) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/menstrual-cycles');

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(cycleData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['menstrualCycle'];
        }
      }

      throw Exception('Failed to create menstrual cycle');
    } catch (e) {
      debugPrint('Error creating menstrual cycle: $e');
      return null;
    }
  }

  Future<HealthRecord?> addHealthRecord(
    String type,
    Map<String, dynamic> data,
  ) async {
    try {
      final token = await _getAuthToken();
      final userId = await _getCurrentUserId();

      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final uri = Uri.parse('$baseUrl/health-records');

      Map<String, dynamic> requestBody = {
        'userId': userId,
        'recordType': _mapToRecordType(type),
        'notes': data['notes'] ?? '',
      };

      // Handle different metric types for original approach
      switch (type.toLowerCase()) {
        case 'blood_pressure':
          // Combine systolic/diastolic into a single value
          requestBody['value'] = '${data['systolic']}/${data['diastolic']}';
          requestBody['unit'] = data['unit'] ?? 'mmHg';
          break;
        case 'weight':
          requestBody['value'] = data['value'].toString();
          requestBody['unit'] = data['unit'] ?? 'kg';
          break;
        case 'height':
          requestBody['value'] = data['value'].toString();
          requestBody['unit'] = data['unit'] ?? 'cm';
          break;
        case 'temperature':
          requestBody['value'] = data['value'].toString();
          requestBody['unit'] = data['unit'] ?? '°C';
          break;
        case 'heart_rate':
          requestBody['value'] = data['value'].toString();
          requestBody['unit'] = data['unit'] ?? 'bpm';
          break;
        default:
          requestBody['value'] = data['value']?.toString() ?? '';
          requestBody['unit'] = data['unit'] ?? '';
          break;
      }

      final body = json.encode(requestBody);

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['success'] == true) {
          return HealthRecord.fromJson(responseData['healthRecord']);
        }
      }

      final errorData = json.decode(response.body);
      throw Exception(errorData['message'] ?? 'Failed to add health record');
    } catch (e) {
      debugPrint('Error adding health record: $e');
      rethrow;
    }
  }

  // Helper method to map frontend types to backend RecordType enum
  String _mapToRecordType(String frontendType) {
    switch (frontendType.toLowerCase()) {
      case 'weight':
        return 'WEIGHT';
      case 'height':
        return 'HEIGHT';
      case 'blood_pressure':
        return 'BLOOD_PRESSURE';
      case 'temperature':
        return 'TEMPERATURE';
      case 'heart_rate':
        return 'HEART_RATE';
      case 'menstrual_cycle':
        return 'MENSTRUAL_CYCLE';
      case 'pregnancy_test':
        return 'PREGNANCY_TEST';
      case 'contraceptive_use':
        return 'CONTRACEPTIVE_USE';
      case 'symptoms':
        return 'SYMPTOMS';
      case 'medication':
        return 'MEDICATION';
      case 'vaccination':
        return 'VACCINATION';
      case 'consultation_notes':
        return 'CONSULTATION_NOTES';
      default:
        return 'OTHER';
    }
  }

  // Medications
  Future<List<Map<String, dynamic>>> getMedications() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/medications');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['medications']);
        }
      }

      throw Exception('Failed to load medications');
    } catch (e) {
      debugPrint('Error loading medications: $e');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getActiveMedications({
    String? userId,
  }) async {
    try {
      final token = await _getAuthToken();
      final currentUserId = userId ?? await _getCurrentUserId();

      if (currentUserId == null) {
        throw Exception('User ID not found');
      }

      final queryParams = {'userId': currentUserId};
      final uri = Uri.parse(
        '$baseUrl/medications/active',
      ).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(
            data['activeMedications'] ?? [],
          );
        }
      }

      throw Exception('Failed to load active medications');
    } catch (e) {
      debugPrint('Error loading active medications: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>?> createMedication(
    Map<String, dynamic> medicationData,
  ) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/medications');

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: json.encode(medicationData),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['medication'];
        }
      }

      throw Exception('Failed to create medication');
    } catch (e) {
      debugPrint('Error creating medication: $e');
      return null;
    }
  }
}
