import 'package:flutter/foundation.dart';
import 'http_client.dart';
import '../models/user_model.dart';

/// Models for Community Features
class SupportGroup {
  final String id;
  final String name;
  final String description;
  final String category;
  final User creator;
  final int memberCount;
  final int? maxMembers;
  final bool isPrivate;
  final bool isActive;
  final String? meetingSchedule;
  final String? meetingLocation;
  final String? contactInfo;
  final DateTime createdAt;
  final DateTime updatedAt;

  SupportGroup({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.creator,
    required this.memberCount,
    this.maxMembers,
    required this.isPrivate,
    required this.isActive,
    this.meetingSchedule,
    this.meetingLocation,
    this.contactInfo,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SupportGroup.fromJson(Map<String, dynamic> json) {
    return SupportGroup(
      id: json['id'].toString(),
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      creator: User.from<PERSON>son(json['creator'] ?? {}),
      memberCount: json['memberCount'] ?? 0,
      maxMembers: json['maxMembers'],
      isPrivate: json['isPrivate'] ?? false,
      isActive: json['isActive'] ?? true,
      meetingSchedule: json['meetingSchedule'],
      meetingLocation: json['meetingLocation'],
      contactInfo: json['contactInfo'],
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

class ForumTopic {
  final String id;
  final String title;
  final String content;
  final String category;
  final User author;
  final int viewCount;
  final int replyCount;
  final bool isPinned;
  final bool isLocked;
  final bool isActive;
  final DateTime lastActivityAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  ForumTopic({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.author,
    required this.viewCount,
    required this.replyCount,
    required this.isPinned,
    required this.isLocked,
    required this.isActive,
    required this.lastActivityAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ForumTopic.fromJson(Map<String, dynamic> json) {
    return ForumTopic(
      id: json['id'].toString(),
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      category: json['category'] ?? '',
      author: User.fromJson(json['author'] ?? {}),
      viewCount: json['viewCount'] ?? 0,
      replyCount: json['replyCount'] ?? 0,
      isPinned: json['isPinned'] ?? false,
      isLocked: json['isLocked'] ?? false,
      isActive: json['isActive'] ?? true,
      lastActivityAt: DateTime.parse(
        json['lastActivityAt'] ?? DateTime.now().toIso8601String(),
      ),
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

class CommunityEvent {
  final String id;
  final String title;
  final String description;
  final String type;
  final User organizer;
  final DateTime eventDate;
  final DateTime? endDate;
  final String location;
  final int? maxParticipants;
  final int currentParticipants;
  final bool registrationRequired;
  final DateTime? registrationDeadline;
  final bool isVirtual;
  final String? virtualLink;
  final String? contactInfo;
  final bool isActive;
  final bool isCancelled;
  final DateTime createdAt;
  final DateTime updatedAt;

  CommunityEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.organizer,
    required this.eventDate,
    this.endDate,
    required this.location,
    this.maxParticipants,
    required this.currentParticipants,
    required this.registrationRequired,
    this.registrationDeadline,
    required this.isVirtual,
    this.virtualLink,
    this.contactInfo,
    required this.isActive,
    required this.isCancelled,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CommunityEvent.fromJson(Map<String, dynamic> json) {
    return CommunityEvent(
      id: json['id'].toString(),
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      type: json['type'] ?? '',
      organizer: User.fromJson(json['organizer'] ?? {}),
      eventDate: DateTime.parse(
        json['eventDate'] ?? DateTime.now().toIso8601String(),
      ),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      location: json['location'] ?? '',
      maxParticipants: json['maxParticipants'],
      currentParticipants: json['currentParticipants'] ?? 0,
      registrationRequired: json['registrationRequired'] ?? true,
      registrationDeadline:
          json['registrationDeadline'] != null
              ? DateTime.parse(json['registrationDeadline'])
              : null,
      isVirtual: json['isVirtual'] ?? false,
      virtualLink: json['virtualLink'],
      contactInfo: json['contactInfo'],
      isActive: json['isActive'] ?? true,
      isCancelled: json['isCancelled'] ?? false,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

/// Community Service for API interactions
class CommunityService {
  final HttpClient _httpClient = HttpClient();

  /// Get community overview with statistics
  Future<Map<String, dynamic>> getCommunityOverview() async {
    try {
      final response = await _httpClient.get('/community/overview');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          return responseData['overview'] ?? {};
        }
      }

      // Fallback to mock data if API fails
      return _getMockCommunityOverview();
    } catch (e) {
      debugPrint('Error fetching community overview: $e');
      return _getMockCommunityOverview();
    }
  }

  /// Mock community overview data
  Map<String, dynamic> _getMockCommunityOverview() {
    return {
      'stats': {
        'totalGroups': 5,
        'totalTopics': 12,
        'totalEvents': 4,
        'upcomingEvents': 3,
      },
    };
  }

  /// Get support groups
  Future<List<SupportGroup>> getSupportGroups({
    String? category,
    String? search,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (category != null) queryParams['category'] = category;
      if (search != null) queryParams['search'] = search;

      final response = await _httpClient.get(
        '/community/support-groups',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['groups'] != null) {
          final groupsList = responseData['groups'] as List<dynamic>;

          return groupsList
              .map(
                (json) => SupportGroup.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        }
      }

      // Fallback to mock data if API fails
      return _getMockSupportGroups(category: category, search: search);
    } catch (e) {
      debugPrint('Error fetching support groups: $e');
      return _getMockSupportGroups(category: category, search: search);
    }
  }

  /// Mock support groups data
  List<SupportGroup> _getMockSupportGroups({String? category, String? search}) {
    final mockGroups = [
      SupportGroup(
        id: '1',
        name: 'Itsinda ry\'abagore biga kubana n\'ubwiyunge',
        description: 'Support Group for Women Family Planning',
        category: 'Family Planning',
        creator: User(
          id: '3',
          name: 'Marie Uwimana',
          email: '<EMAIL>',
          phone: '+250788111222',
          role: UserRole.client,
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          isActive: true,
        ),
        memberCount: 156,
        maxMembers: 200,
        isPrivate: false,
        isActive: true,
        meetingSchedule: 'Ku wa gatatu buri cyumweru saa kumi n\'ebyiri',
        meetingLocation: 'Kimisagara Health Center',
        contactInfo: '+250788111222',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      SupportGroup(
        id: '2',
        name: 'Ikiganiro cy\'ubuzima',
        description: 'Health Discussion Forum',
        category: 'Health',
        creator: User(
          id: '2',
          name: 'Jean Mukamana',
          email: '<EMAIL>',
          phone: '+250788333444',
          role: UserRole.healthWorker,
          createdAt: DateTime.now().subtract(const Duration(days: 35)),
          isActive: true,
        ),
        memberCount: 89,
        maxMembers: 150,
        isPrivate: false,
        isActive: true,
        meetingSchedule: 'Ku wa kane buri cyumweru saa kumi',
        meetingLocation: 'Online Platform',
        contactInfo: '<EMAIL>',
        createdAt: DateTime.now().subtract(const Duration(days: 35)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      SupportGroup(
        id: '3',
        name: 'Abana b\'urubyiruko',
        description: 'Youth Health Group',
        category: 'Youth Health',
        creator: User(
          id: '4',
          name: 'Alice Nyirahabimana',
          email: '<EMAIL>',
          phone: '+250788555666',
          role: UserRole.client,
          createdAt: DateTime.now().subtract(const Duration(days: 40)),
          isActive: true,
        ),
        memberCount: 67,
        maxMembers: 100,
        isPrivate: false,
        isActive: true,
        meetingSchedule: 'Ku wa mbere buri cyumweru saa kumi n\'ebyiri',
        meetingLocation: 'Nyarugenge Health Center',
        contactInfo: '+250788555666',
        createdAt: DateTime.now().subtract(const Duration(days: 40)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ];

    // Apply filtering
    var filteredGroups = mockGroups;

    if (category != null && category != 'all') {
      filteredGroups =
          filteredGroups
              .where(
                (group) => group.category.toLowerCase().contains(
                  category.toLowerCase(),
                ),
              )
              .toList();
    }

    if (search != null && search.isNotEmpty) {
      filteredGroups =
          filteredGroups
              .where(
                (group) =>
                    group.name.toLowerCase().contains(search.toLowerCase()) ||
                    group.description.toLowerCase().contains(
                      search.toLowerCase(),
                    ),
              )
              .toList();
    }

    return filteredGroups;
  }

  /// Create support group
  Future<bool> createSupportGroup({
    required String name,
    required String description,
    required String category,
    required String creatorId,
    bool isPrivate = false,
    int? maxMembers,
  }) async {
    try {
      final requestData = {
        'name': name,
        'description': description,
        'category': category,
        'creatorId': creatorId,
        'isPrivate': isPrivate,
        if (maxMembers != null) 'maxMembers': maxMembers,
      };

      final response = await _httpClient.post(
        '/community/support-groups',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        return responseData['success'] == true;
      }

      return false;
    } catch (e) {
      debugPrint('Error creating support group: $e');
      return false;
    }
  }

  /// Join support group
  Future<bool> joinSupportGroup(String groupId, String userId) async {
    try {
      final response = await _httpClient.post(
        '/community/support-groups/$groupId/join',
        queryParameters: {'userId': userId},
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        return responseData['success'] == true;
      }

      return false;
    } catch (e) {
      debugPrint('Error joining support group: $e');
      return false;
    }
  }

  /// Get forum topics
  Future<List<ForumTopic>> getForumTopics({
    String? category,
    String? search,
    String? filter,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (category != null) queryParams['category'] = category;
      if (search != null) queryParams['search'] = search;
      if (filter != null) queryParams['filter'] = filter;

      final response = await _httpClient.get(
        '/community/forum/topics',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['topics'] != null) {
          final topicsList = responseData['topics'] as List<dynamic>;

          return topicsList
              .map((json) => ForumTopic.fromJson(json as Map<String, dynamic>))
              .toList();
        }
      }

      // Fallback to mock data if API fails
      return _getMockForumTopics(
        category: category,
        search: search,
        filter: filter,
      );
    } catch (e) {
      debugPrint('Error fetching forum topics: $e');
      return _getMockForumTopics(
        category: category,
        search: search,
        filter: filter,
      );
    }
  }

  /// Mock forum topics data
  List<ForumTopic> _getMockForumTopics({
    String? category,
    String? search,
    String? filter,
  }) {
    final mockTopics = [
      ForumTopic(
        id: '1',
        title: 'Itsinda ry\'abagore biga kubana n\'ubwiyunge',
        content: 'Itsinda ry\'abagore biga kubana n\'ubwiyunge',
        category: 'Family Planning',
        author: User(
          id: '3',
          name: 'Marie Uwimana',
          email: '<EMAIL>',
          phone: '+250788111222',
          role: UserRole.client,
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          isActive: true,
        ),
        viewCount: 245,
        replyCount: 23,
        isPinned: true,
        isLocked: false,
        isActive: true,
        lastActivityAt: DateTime.now().subtract(const Duration(hours: 2)),
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      ForumTopic(
        id: '2',
        title: 'Ikiganiro cy\'ubuzima bw\'imyororokere',
        content: 'Ikiganiro gishya kuri ubuzima bw\'imyororokere',
        category: 'Health',
        author: User(
          id: '2',
          name: 'Jean Mukamana',
          email: '<EMAIL>',
          phone: '+250788333444',
          role: UserRole.healthWorker,
          createdAt: DateTime.now().subtract(const Duration(days: 8)),
          isActive: true,
        ),
        viewCount: 189,
        replyCount: 15,
        isPinned: false,
        isLocked: false,
        isActive: true,
        lastActivityAt: DateTime.now().subtract(const Duration(hours: 4)),
        createdAt: DateTime.now().subtract(const Duration(days: 8)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 4)),
      ),
    ];

    // Apply filtering
    var filteredTopics = mockTopics;

    if (category != null && category != 'all') {
      filteredTopics =
          filteredTopics
              .where(
                (topic) => topic.category.toLowerCase().contains(
                  category.toLowerCase(),
                ),
              )
              .toList();
    }

    if (search != null && search.isNotEmpty) {
      filteredTopics =
          filteredTopics
              .where(
                (topic) =>
                    topic.title.toLowerCase().contains(search.toLowerCase()) ||
                    topic.content.toLowerCase().contains(search.toLowerCase()),
              )
              .toList();
    }

    if (filter == 'popular') {
      filteredTopics.sort((a, b) => b.viewCount.compareTo(a.viewCount));
    } else if (filter == 'pinned') {
      filteredTopics = filteredTopics.where((topic) => topic.isPinned).toList();
    }

    return filteredTopics;
  }

  /// Get community events
  Future<List<CommunityEvent>> getCommunityEvents({
    String? type,
    String? filter,
    String? search,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (type != null) queryParams['type'] = type;
      if (filter != null) queryParams['filter'] = filter;
      if (search != null) queryParams['search'] = search;

      final response = await _httpClient.get(
        '/community/events',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['events'] != null) {
          final eventsList = responseData['events'] as List<dynamic>;

          return eventsList
              .map(
                (json) => CommunityEvent.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        }
      }

      // Fallback to mock data if API fails
      return _getMockCommunityEvents(
        type: type,
        filter: filter,
        search: search,
      );
    } catch (e) {
      debugPrint('Error fetching community events: $e');
      return _getMockCommunityEvents(
        type: type,
        filter: filter,
        search: search,
      );
    }
  }

  /// Mock community events data
  List<CommunityEvent> _getMockCommunityEvents({
    String? type,
    String? filter,
    String? search,
  }) {
    final now = DateTime.now();
    final mockEvents = [
      CommunityEvent(
        id: '1',
        title: 'Ubwiyunge bw\'urubyiruko',
        description: 'Youth Family Planning Workshop',
        type: 'WORKSHOP',
        organizer: User(
          id: '2',
          name: 'Jean Mukamana',
          email: '<EMAIL>',
          phone: '+250788333444',
          role: UserRole.healthWorker,
          createdAt: now.subtract(const Duration(days: 7)),
          isActive: true,
        ),
        eventDate: now.add(const Duration(days: 7)),
        endDate: now.add(const Duration(days: 7, hours: 3)),
        location: 'Kimisagara Health Center',
        maxParticipants: 50,
        currentParticipants: 8,
        registrationRequired: true,
        registrationDeadline: now.add(const Duration(days: 5)),
        isVirtual: false,
        isActive: true,
        isCancelled: false,
        contactInfo: '+250788111222',
        createdAt: now.subtract(const Duration(days: 7)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
      CommunityEvent(
        id: '2',
        title: 'Inama y\'ubuzima',
        description: 'Health Education Workshop',
        type: 'SEMINAR',
        organizer: User(
          id: '3',
          name: 'Marie Uwimana',
          email: '<EMAIL>',
          phone: '+250788111222',
          role: UserRole.client,
          createdAt: now.subtract(const Duration(days: 14)),
          isActive: true,
        ),
        eventDate: now.add(const Duration(days: 14)),
        endDate: now.add(const Duration(days: 14, hours: 4)),
        location: 'Nyarugenge Health Center',
        maxParticipants: 100,
        currentParticipants: 23,
        registrationRequired: true,
        registrationDeadline: now.add(const Duration(days: 10)),
        isVirtual: false,
        isActive: true,
        isCancelled: false,
        contactInfo: '+250788555666',
        createdAt: now.subtract(const Duration(days: 14)),
        updatedAt: now.subtract(const Duration(days: 2)),
      ),
    ];

    // Apply filtering
    var filteredEvents = mockEvents;

    if (type != null) {
      filteredEvents =
          filteredEvents
              .where((event) => event.type.toLowerCase() == type.toLowerCase())
              .toList();
    }

    if (search != null && search.isNotEmpty) {
      filteredEvents =
          filteredEvents
              .where(
                (event) =>
                    event.title.toLowerCase().contains(search.toLowerCase()) ||
                    event.description.toLowerCase().contains(
                      search.toLowerCase(),
                    ),
              )
              .toList();
    }

    if (filter == 'upcoming') {
      filteredEvents =
          filteredEvents
              .where((event) => event.eventDate.isAfter(now))
              .toList();
      filteredEvents.sort((a, b) => a.eventDate.compareTo(b.eventDate));
    } else if (filter == 'past') {
      filteredEvents =
          filteredEvents
              .where((event) => event.eventDate.isBefore(now))
              .toList();
      filteredEvents.sort((a, b) => b.eventDate.compareTo(a.eventDate));
    }

    return filteredEvents;
  }
}
