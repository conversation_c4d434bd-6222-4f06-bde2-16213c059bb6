import 'package:flutter/foundation.dart';
import 'http_client.dart';
import '../models/user_model.dart';

/// Models for Community Features
class SupportGroup {
  final String id;
  final String name;
  final String description;
  final String category;
  final User creator;
  final int memberCount;
  final int? maxMembers;
  final bool isPrivate;
  final bool isActive;
  final String? meetingSchedule;
  final String? meetingLocation;
  final String? contactInfo;
  final DateTime createdAt;
  final DateTime updatedAt;

  SupportGroup({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.creator,
    required this.memberCount,
    this.maxMembers,
    required this.isPrivate,
    required this.isActive,
    this.meetingSchedule,
    this.meetingLocation,
    this.contactInfo,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SupportGroup.fromJson(Map<String, dynamic> json) {
    return SupportGroup(
      id: json['id'].toString(),
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      creator: User.from<PERSON>son(json['creator'] ?? {}),
      memberCount: json['memberCount'] ?? 0,
      maxMembers: json['maxMembers'],
      isPrivate: json['isPrivate'] ?? false,
      isActive: json['isActive'] ?? true,
      meetingSchedule: json['meetingSchedule'],
      meetingLocation: json['meetingLocation'],
      contactInfo: json['contactInfo'],
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

class ForumTopic {
  final String id;
  final String title;
  final String content;
  final String category;
  final User author;
  final int viewCount;
  final int replyCount;
  final bool isPinned;
  final bool isLocked;
  final bool isActive;
  final DateTime lastActivityAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  ForumTopic({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.author,
    required this.viewCount,
    required this.replyCount,
    required this.isPinned,
    required this.isLocked,
    required this.isActive,
    required this.lastActivityAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ForumTopic.fromJson(Map<String, dynamic> json) {
    return ForumTopic(
      id: json['id'].toString(),
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      category: json['category'] ?? '',
      author: User.fromJson(json['author'] ?? {}),
      viewCount: json['viewCount'] ?? 0,
      replyCount: json['replyCount'] ?? 0,
      isPinned: json['isPinned'] ?? false,
      isLocked: json['isLocked'] ?? false,
      isActive: json['isActive'] ?? true,
      lastActivityAt: DateTime.parse(
        json['lastActivityAt'] ?? DateTime.now().toIso8601String(),
      ),
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

class CommunityEvent {
  final String id;
  final String title;
  final String description;
  final String type;
  final User organizer;
  final DateTime eventDate;
  final DateTime? endDate;
  final String location;
  final int? maxParticipants;
  final int currentParticipants;
  final bool registrationRequired;
  final DateTime? registrationDeadline;
  final bool isVirtual;
  final String? virtualLink;
  final String? contactInfo;
  final bool isActive;
  final bool isCancelled;
  final DateTime createdAt;
  final DateTime updatedAt;

  CommunityEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.organizer,
    required this.eventDate,
    this.endDate,
    required this.location,
    this.maxParticipants,
    required this.currentParticipants,
    required this.registrationRequired,
    this.registrationDeadline,
    required this.isVirtual,
    this.virtualLink,
    this.contactInfo,
    required this.isActive,
    required this.isCancelled,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CommunityEvent.fromJson(Map<String, dynamic> json) {
    return CommunityEvent(
      id: json['id'].toString(),
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      type: json['type'] ?? '',
      organizer: User.fromJson(json['organizer'] ?? {}),
      eventDate: DateTime.parse(
        json['eventDate'] ?? DateTime.now().toIso8601String(),
      ),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      location: json['location'] ?? '',
      maxParticipants: json['maxParticipants'],
      currentParticipants: json['currentParticipants'] ?? 0,
      registrationRequired: json['registrationRequired'] ?? true,
      registrationDeadline:
          json['registrationDeadline'] != null
              ? DateTime.parse(json['registrationDeadline'])
              : null,
      isVirtual: json['isVirtual'] ?? false,
      virtualLink: json['virtualLink'],
      contactInfo: json['contactInfo'],
      isActive: json['isActive'] ?? true,
      isCancelled: json['isCancelled'] ?? false,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

/// Community Service for API interactions
class CommunityService {
  final HttpClient _httpClient = HttpClient();

  /// Get community overview with statistics
  Future<Map<String, dynamic>> getCommunityOverview() async {
    try {
      // Temporary mock data until backend is restarted
      await Future.delayed(
        const Duration(milliseconds: 500),
      ); // Simulate API delay

      return {
        'stats': {'totalGroups': 3, 'totalTopics': 2, 'upcomingEvents': 2},
        'popularGroups': [],
        'popularTopics': [],
        'upcomingEvents': [],
      };
    } catch (e) {
      debugPrint('Error fetching community overview: $e');
      return {};
    }
  }

  /// Get support groups
  Future<List<SupportGroup>> getSupportGroups({
    String? category,
    String? search,
  }) async {
    try {
      // Temporary mock data until backend is restarted
      await Future.delayed(const Duration(milliseconds: 300));
      return [];
    } catch (e) {
      debugPrint('Error fetching support groups: $e');
      return [];
    }
  }

  /// Create support group
  Future<bool> createSupportGroup({
    required String name,
    required String description,
    required String category,
    required String creatorId,
    bool isPrivate = false,
    int? maxMembers,
  }) async {
    try {
      final requestData = {
        'name': name,
        'description': description,
        'category': category,
        'creatorId': creatorId,
        'isPrivate': isPrivate,
        if (maxMembers != null) 'maxMembers': maxMembers,
      };

      final response = await _httpClient.post(
        '/community/support-groups',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        return responseData['success'] == true;
      }

      return false;
    } catch (e) {
      debugPrint('Error creating support group: $e');
      return false;
    }
  }

  /// Join support group
  Future<bool> joinSupportGroup(String groupId, String userId) async {
    try {
      final response = await _httpClient.post(
        '/community/support-groups/$groupId/join',
        queryParameters: {'userId': userId},
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        return responseData['success'] == true;
      }

      return false;
    } catch (e) {
      debugPrint('Error joining support group: $e');
      return false;
    }
  }

  /// Get forum topics
  Future<List<ForumTopic>> getForumTopics({
    String? category,
    String? search,
    String? filter,
  }) async {
    try {
      // Temporary mock data until backend is restarted
      await Future.delayed(const Duration(milliseconds: 300));
      return [];
    } catch (e) {
      debugPrint('Error fetching forum topics: $e');
      return [];
    }
  }

  /// Get community events
  Future<List<CommunityEvent>> getCommunityEvents({
    String? type,
    String? filter,
    String? search,
  }) async {
    try {
      // Temporary mock data until backend is restarted
      await Future.delayed(const Duration(milliseconds: 300));
      return [];
    } catch (e) {
      debugPrint('Error fetching community events: $e');
      return [];
    }
  }
}
