import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../core/models/health_record_model.dart'
    hide MessageType, Message, MessagePriority;
import '../../core/models/message.dart';
import '../../core/models/user_model.dart';
import '../../core/services/message_service.dart';
import '../../core/services/voice_service.dart';
import '../../core/services/auth_service.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:async';

// Waveform Painter for voice messages
class WaveformPainter extends CustomPainter {
  final Color color;

  WaveformPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..strokeWidth = 2
          ..strokeCap = StrokeCap.round;

    final barWidth = size.width / 20;
    for (int i = 0; i < 20; i++) {
      final height = (i % 3 + 1) * size.height / 4;
      final x = i * barWidth + barWidth / 2;
      canvas.drawLine(
        Offset(x, size.height / 2 - height / 2),
        Offset(x, size.height / 2 + height / 2),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class ChatScreen extends StatefulWidget {
  final dynamic contact; // Can be HealthWorker or ChatContact

  const ChatScreen({super.key, required this.contact});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final VoiceService _voiceService = VoiceService();
  late final MessageService _messageService;

  List<Message> _messages = [];
  bool _isRecording = false;
  bool _isTyping = false;
  bool _isLoading = true;
  String? _editingMessageId;
  String? _selectedMessageId;
  bool _isSelectionMode = false;
  Timer? _recordingTimer;
  int _recordingDuration = 0;
  final FocusNode _messageFocusNode = FocusNode();

  late AnimationController _waveAnimationController;
  late AnimationController _typingAnimationController;

  // Helper methods to get contact properties regardless of type
  String get contactId {
    if (widget.contact is HealthWorker) {
      return (widget.contact as HealthWorker).id;
    } else {
      return widget.contact.id;
    }
  }

  String get contactName {
    if (widget.contact is HealthWorker) {
      return (widget.contact as HealthWorker).name;
    } else {
      return widget.contact.name;
    }
  }

  String get contactRole {
    if (widget.contact is HealthWorker) {
      return (widget.contact as HealthWorker).specialization;
    } else {
      return _getRoleDisplayName(widget.contact.role);
    }
  }

  String _getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'Umuyobozi';
      case UserRole.healthWorker:
        return 'Umukozi w\'ubuzima';
      case UserRole.client:
        return 'Umukiriya';
    }
  }

  @override
  void initState() {
    super.initState();
    _messageService = MessageService();
    _waveAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _typingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _loadMessages();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    _waveAnimationController.dispose();
    _typingAnimationController.dispose();
    _recordingTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadMessages() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = AuthService();
      final currentUser = authService.currentUser;
      if (currentUser != null) {
        final messages = await _messageService
            .getConversationMessagesBetweenUsers(
              userId1: currentUser.id,
              userId2: contactId,
            );

        setState(() {
          _messages = messages;
          _isLoading = false;
        });

        _scrollToBottom();
      }
    } catch (e) {
      debugPrint('Error loading messages: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(isTablet),
      body: Stack(
        children: [
          Column(
            children: [
              // Messages List
              Expanded(child: _buildMessagesList(isTablet)),

              // Message Input
              _buildMessageInput(isTablet),
            ],
          ),
          // Recording Overlay
          if (_isRecording) _buildRecordingOverlay(),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(bool isTablet) {
    if (_isSelectionMode && _selectedMessageId != null) {
      // Show selection mode AppBar with edit/delete actions
      final selectedMessage = _messages.firstWhere(
        (m) => m.id.toString() == _selectedMessageId,
        orElse: () => _messages.first,
      );
      final isMyMessage = selectedMessage.sender.id != contactId;

      return AppBar(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            setState(() {
              _isSelectionMode = false;
              _selectedMessageId = null;
            });
          },
        ),
        title: const Text('Hitamo ubutumwa'),
        actions: [
          if (isMyMessage) ...[
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _editMessage(selectedMessage),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _deleteMessage(selectedMessage),
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _deleteMessage(selectedMessage),
            ),
          ],
        ],
      );
    }

    // Normal AppBar
    return AppBar(
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      title: Row(
        children: [
          CircleAvatar(
            radius: isTablet ? 24 : 20,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            child: Icon(
              Icons.person,
              color: Colors.white,
              size: isTablet ? 24 : 20,
            ),
          ),
          SizedBox(width: AppTheme.spacing12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  contactName,
                  style: AppTheme.headingSmall.copyWith(
                    color: Colors.white,
                    fontSize: isTablet ? 18 : 16,
                  ),
                ),
                Text(
                  contactRole,
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: isTablet ? 14 : 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        // Voice Call Button
        IconButton(
          onPressed: _startVoiceCall,
          icon: const Icon(Icons.phone),
          tooltip: 'Guhamagara',
        ),

        // Video Call Button
        IconButton(
          onPressed: _startVideoCall,
          icon: const Icon(Icons.videocam),
          tooltip: 'Video call',
        ),

        // Emergency Button
        IconButton(
          onPressed: _showEmergencyOptions,
          icon: const Icon(Icons.emergency),
          tooltip: 'Ihutirwa',
        ),
      ],
    );
  }

  Widget _buildMessagesList(bool isTablet) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? AppTheme.spacing20 : AppTheme.spacing16,
      ),
      child: ListView.builder(
        controller: _scrollController,
        itemCount: _messages.length,
        itemBuilder: (context, index) {
          final message = _messages[index];
          return _buildMessageBubble(message, isTablet, index);
        },
      ),
    );
  }

  Widget _buildMessageBubble(Message message, bool isTablet, int index) {
    // Check if message is from current user by comparing with contactId
    final isFromMe = message.sender.id != contactId;
    final isSelected = _selectedMessageId == message.id.toString();

    return GestureDetector(
      onLongPress: () => _selectMessage(message),
      onTap: () {
        if (_isSelectionMode) {
          _selectMessage(message);
        }
      },
      child: Container(
        decoration:
            isSelected
                ? BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                )
                : null,
        padding: isSelected ? const EdgeInsets.all(4) : null,
        child: Container(
              margin: EdgeInsets.only(
                bottom: AppTheme.spacing8,
                left: isFromMe ? (isTablet ? 80 : 60) : 0,
                right: isFromMe ? 0 : (isTablet ? 80 : 60),
              ),
              child: Column(
                crossAxisAlignment:
                    isFromMe
                        ? CrossAxisAlignment.end
                        : CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppTheme.spacing16,
                      vertical: AppTheme.spacing12,
                    ),
                    decoration: BoxDecoration(
                      color: isFromMe ? AppTheme.primaryColor : Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: const Radius.circular(16),
                        topRight: const Radius.circular(16),
                        bottomLeft: Radius.circular(isFromMe ? 16 : 4),
                        bottomRight: Radius.circular(isFromMe ? 4 : 16),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (message.type == MessageType.audio)
                          _buildVoiceMessageContent(message, isFromMe, isTablet)
                        else
                          _buildTextMessageContent(message, isFromMe, isTablet),

                        SizedBox(height: AppTheme.spacing4),

                        Text(
                          _formatTime(message.createdAt),
                          style: AppTheme.bodySmall.copyWith(
                            color:
                                isFromMe
                                    ? Colors.white.withValues(alpha: 0.7)
                                    : AppTheme.textSecondary,
                            fontSize: isTablet ? 12 : 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
            .animate(delay: Duration(milliseconds: index * 100))
            .fadeIn(duration: 300.ms)
            .slideX(begin: isFromMe ? 0.3 : -0.3, duration: 300.ms),
      ),
    );
  }

  Widget _buildTextMessageContent(
    Message message,
    bool isFromMe,
    bool isTablet,
  ) {
    return Text(
      message.content,
      style: AppTheme.bodyMedium.copyWith(
        color: isFromMe ? Colors.white : AppTheme.textPrimary,
        fontSize: isTablet ? 16 : 14,
        height: 1.4,
      ),
    );
  }

  Widget _buildVoiceMessageContent(
    Message message,
    bool isFromMe,
    bool isTablet,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: () => _playVoiceMessage(message),
          icon: Icon(
            Icons.play_arrow,
            color: isFromMe ? Colors.white : AppTheme.primaryColor,
          ),
        ),

        Expanded(
          child: Container(
            height: 30,
            child: CustomPaint(
              painter: WaveformPainter(
                color: isFromMe ? Colors.white : AppTheme.primaryColor,
              ),
            ),
          ),
        ),

        Text(
          '0:15', // TODO: Get actual voice duration from message metadata
          style: AppTheme.bodySmall.copyWith(
            color:
                isFromMe
                    ? Colors.white.withValues(alpha: 0.8)
                    : AppTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildMessageInput(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Voice Recording Button - WhatsApp style
          GestureDetector(
            onLongPressStart: (_) => _startVoiceRecording(),
            onLongPressEnd: (_) => _stopVoiceRecording(),
            onLongPressCancel: () => _cancelVoiceRecording(),
            child: Container(
              width: isTablet ? 50 : 45,
              height: isTablet ? 50 : 45,
              decoration: BoxDecoration(
                color:
                    _isRecording ? AppTheme.errorColor : AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(25),
                boxShadow:
                    _isRecording
                        ? [
                          BoxShadow(
                            color: AppTheme.errorColor.withValues(alpha: 0.3),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ]
                        : null,
              ),
              child: Icon(
                _isRecording ? Icons.mic : Icons.mic_none,
                color: Colors.white,
                size: isTablet ? 24 : 20,
              ),
            ),
          ),

          SizedBox(width: AppTheme.spacing12),

          // Text Input
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: AppTheme.borderColor),
              ),
              child: TextField(
                controller: _messageController,
                decoration: InputDecoration(
                  hintText:
                      _editingMessageId != null
                          ? 'Hindura ubutumwa...'
                          : 'Andika ubutumwa...',
                  hintStyle: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: AppTheme.spacing16,
                    vertical: AppTheme.spacing12,
                  ),
                  prefixIcon:
                      _editingMessageId != null
                          ? Icon(
                            Icons.edit,
                            color: AppTheme.primaryColor,
                            size: 20,
                          )
                          : null,
                ),
                onChanged: (text) {
                  setState(() {
                    _isTyping = text.isNotEmpty;
                  });
                },
                onSubmitted: (_) => _sendTextMessage(),
              ),
            ),
          ),

          SizedBox(width: AppTheme.spacing12),

          // Send Button
          GestureDetector(
            onTap: _isTyping ? _sendTextMessage : _showMessageOptions,
            child: Container(
              width: isTablet ? 50 : 45,
              height: isTablet ? 50 : 45,
              decoration: BoxDecoration(
                color:
                    _isTyping ? AppTheme.primaryColor : AppTheme.secondaryColor,
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                _isTyping ? Icons.send : Icons.add,
                color: Colors.white,
                size: isTablet ? 24 : 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Voice Recording Methods
  Future<void> _startVoiceRecording() async {
    setState(() {
      _isRecording = true;
    });

    try {
      final directory = await getApplicationDocumentsDirectory();
      final filePath =
          '${directory.path}/voice_message_${DateTime.now().millisecondsSinceEpoch}.m4a';

      await _voiceService.startRecording(filePath);

      // Start recording timer
      _recordingDuration = 0;
      _recordingTimer = Timer.periodic(Duration(seconds: 1), (timer) {
        setState(() {
          _recordingDuration++;
        });
      });

      // Provide haptic feedback
      HapticFeedback.mediumImpact();

      // Show recording indicator with instructions
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.mic, color: Colors.white),
                SizedBox(width: 8),
                Text('Gufata amajwi... Reka kugira ngo uhagarike'),
              ],
            ),
            backgroundColor: AppTheme.primaryColor,
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      _showError('Habaye ikosa mu gufata ijwi: $e');
      setState(() {
        _isRecording = false;
      });
    }
  }

  Future<void> _stopVoiceRecording() async {
    if (!_isRecording) return;

    setState(() {
      _isRecording = false;
    });

    // Stop recording timer
    _recordingTimer?.cancel();
    _recordingTimer = null;

    try {
      final filePath = await _voiceService.stopRecording();

      if (filePath != null && File(filePath).existsSync()) {
        _showSuccess('Ijwi ryafashwe! Rohereza...');
        _sendVoiceMessage(filePath);
      } else {
        _showError('Nta jwi ryafashwe cyangwa dosiye ntiboneka');
      }
    } catch (e) {
      _showError('Habaye ikosa mu guhagarika gufata ijwi: $e');
    }
  }

  void _cancelVoiceRecording() {
    if (_isRecording) {
      setState(() {
        _isRecording = false;
      });

      // Stop recording timer
      _recordingTimer?.cancel();
      _recordingTimer = null;

      _voiceService.stopRecording();
      _showError('Gufata amajwi byahagaritswe');
    }
  }

  // Message Sending Methods
  Future<void> _sendTextMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    // Check if we're editing a message
    if (_editingMessageId != null) {
      await _updateMessage(text);
      return;
    }

    // Clear the input immediately for better UX
    _messageController.clear();
    setState(() {
      _isTyping = false;
    });

    try {
      final sentMessage = await _messageService.sendMessage(
        recipientId: contactId,
        content: text,
        type: MessageType.text,
      );

      if (sentMessage != null) {
        setState(() {
          _messages.add(sentMessage);
        });
        _scrollToBottom();
        _showSuccess('Ubutumwa bwoherejwe!');
      } else {
        _showError('Ubutumwa ntibwashobotse koherezwa. Ongera ugerageze.');
      }
    } catch (e) {
      debugPrint('Error sending message: $e');
      _showError('Ikosa ryabaye mu kohereza ubutumwa.');
    }
  }

  Future<void> _updateMessage(String newContent) async {
    if (_editingMessageId == null) return;

    try {
      final updatedMessage = await _messageService.updateMessage(
        messageId: _editingMessageId!,
        content: newContent,
      );

      if (updatedMessage != null) {
        setState(() {
          final index = _messages.indexWhere(
            (m) => m.id.toString() == _editingMessageId,
          );
          if (index != -1) {
            _messages[index] = updatedMessage;
          }
          _editingMessageId = null;
          _isTyping = false;
        });
        _messageController.clear();
        _showSuccess('Ubutumwa bwavuguruwe!');
      } else {
        _showError('Ubutumwa ntibwashobotse guvugururwa.');
      }
    } catch (e) {
      debugPrint('Error updating message: $e');
      _showError('Ikosa ryabaye mu guvugurura ubutumwa.');
    }
  }

  Future<void> _sendVoiceMessage(String filePath) async {
    try {
      final sentMessage = await _messageService.sendMessage(
        recipientId: contactId,
        content: 'Ubutumwa bw\'ijwi', // Voice message placeholder text
        type: MessageType.audio,
      );

      if (sentMessage != null) {
        setState(() {
          _messages.add(sentMessage);
        });
        _scrollToBottom();
      } else {
        _showError('Ubutumwa bw\'ijwi ntibwashobotse koherezwa.');
      }
    } catch (e) {
      debugPrint('Error sending voice message: $e');
      _showError('Ikosa ryabaye mu kohereza ubutumwa bw\'ijwi.');
    }
  }

  // Communication Methods
  void _startVoiceCall() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.phone, color: AppTheme.primaryColor),
                SizedBox(width: AppTheme.spacing8),
                Text('Guhamagara'),
              ],
            ),
            content: Text('Ushaka guhamagara $contactName?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Kuraguza'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _initiateVoiceCall();
                },
                child: Text('Hamagara'),
              ),
            ],
          ),
    );
  }

  void _startVideoCall() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.videocam, color: AppTheme.primaryColor),
                SizedBox(width: AppTheme.spacing8),
                Text('Video Call'),
              ],
            ),
            content: Text('Ushaka gukora video call na $contactName?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Kuraguza'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _initiateVideoCall();
                },
                child: Text('Tangira'),
              ),
            ],
          ),
    );
  }

  void _showEmergencyOptions() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: EdgeInsets.all(AppTheme.spacing20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Ubufasha bw\'ihutirwa', style: AppTheme.headingMedium),
                SizedBox(height: AppTheme.spacing20),

                ListTile(
                  leading: Icon(Icons.emergency, color: AppTheme.errorColor),
                  title: Text('Hamagara ihutirwa'),
                  subtitle: Text('Guhamagara kuri 912'),
                  onTap: () {
                    Navigator.pop(context);
                    _callEmergency();
                  },
                ),

                ListTile(
                  leading: Icon(
                    Icons.local_hospital,
                    color: AppTheme.primaryColor,
                  ),
                  title: Text('Saba ubufasha bw\'ihutirwa'),
                  subtitle: Text('Kohereza ubutumwa bw\'ihutirwa ku muganga'),
                  onTap: () {
                    Navigator.pop(context);
                    _sendEmergencyMessage();
                  },
                ),

                ListTile(
                  leading: Icon(
                    Icons.location_on,
                    color: AppTheme.successColor,
                  ),
                  title: Text('Shakisha ikigo cy\'ubuzima hafi'),
                  subtitle: Text('Reba ikigo cy\'ubuzima hafi yawe'),
                  onTap: () {
                    Navigator.pop(context);
                    _findNearestHealthFacility();
                  },
                ),
              ],
            ),
          ),
    );
  }

  // Utility Methods
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppTheme.errorColor),
    );
  }

  void _showMessageOptions() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: EdgeInsets.all(AppTheme.spacing20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: Icon(Icons.photo, color: AppTheme.primaryColor),
                  title: Text('Kohereza ifoto'),
                  onTap: () {
                    Navigator.pop(context);
                    _sendImage();
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.attach_file,
                    color: AppTheme.secondaryColor,
                  ),
                  title: Text('Kohereza dosiye'),
                  onTap: () {
                    Navigator.pop(context);
                    _sendFile();
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.location_on,
                    color: AppTheme.successColor,
                  ),
                  title: Text('Kohereza aho uri'),
                  onTap: () {
                    Navigator.pop(context);
                    _sendLocation();
                  },
                ),
              ],
            ),
          ),
    );
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Now';
    }
  }

  void _playVoiceMessage(Message message) async {
    try {
      // For now, we'll use the content as the file path
      // In a real implementation, you'd store the file path in the message
      final filePath = message.content;

      // Check if file exists
      if (File(filePath).existsSync()) {
        await _voiceService.playVoiceMessage(filePath);
        _showSuccess('Gukina ijwi...');
      } else {
        // For demo purposes, show a placeholder message
        _showError(
          'Dosiye y\'ijwi ntiboneka. Iri ni urugero rw\'ubutumwa bw\'ijwi.',
        );
      }
    } catch (e) {
      _showError('Ikosa mu gukina ijwi: $e');
    }
  }

  // Message Selection Methods
  void _selectMessage(Message message) {
    setState(() {
      if (_selectedMessageId == message.id.toString()) {
        // Deselect if already selected
        _selectedMessageId = null;
        _isSelectionMode = false;
      } else {
        // Select the message
        _selectedMessageId = message.id.toString();
        _isSelectionMode = true;
      }
    });
  }

  void _editMessage(Message message) {
    setState(() {
      _editingMessageId = message.id.toString();
      _messageController.text = message.content;
      _isSelectionMode = false;
      _selectedMessageId = null;
    });
    _messageFocusNode.requestFocus();
  }

  void _deleteMessage(Message message) async {
    try {
      final success = await _messageService.deleteMessage(
        message.id.toString(),
      );
      if (success) {
        setState(() {
          _messages.removeWhere((m) => m.id == message.id);
          _isSelectionMode = false;
          _selectedMessageId = null;
        });
        _showSuccess('Ubutumwa bwasibwe');
      } else {
        _showError('Ubutumwa ntibwashobotse gusibwa');
      }
    } catch (e) {
      _showError('Ikosa mu gusiba ubutumwa: $e');
    }
  }

  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  Widget _buildRecordingOverlay() {
    final minutes = _recordingDuration ~/ 60;
    final seconds = _recordingDuration % 60;
    final timeString =
        '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';

    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.errorColor.withValues(alpha: 0.9),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(16),
            bottomRight: Radius.circular(16),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.mic, color: Colors.white, size: 20),
            SizedBox(width: 8),
            Text(
              'Gufata amajwi... $timeString',
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(width: 16),
            Text(
              'Reka kugira ngo uhagarike',
              style: AppTheme.bodySmall.copyWith(
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Communication Implementation Methods
  void _initiateVoiceCall() {
    // In a real app, this would integrate with calling services
    _showError('Guhamagara bizashyirwa mu bikorwa vuba...');
  }

  void _initiateVideoCall() {
    // In a real app, this would integrate with video calling services
    _showError('Video call izashyirwa mu bikorwa vuba...');
  }

  void _callEmergency() {
    // In a real app, this would call emergency services
    _showError('Guhamagara ihutirwa bizashyirwa mu bikorwa vuba...');
  }

  Future<void> _sendEmergencyMessage() async {
    const emergencyText =
        '🚨 IHUTIRWA: Nkeneye ubufasha bw\'ihutirwa. Nyamuneka mfashe vuba.';

    try {
      final sentMessage = await _messageService.sendMessage(
        recipientId: contactId,
        content: emergencyText,
        type: MessageType.text,
        priority: MessagePriority.urgent,
      );

      if (sentMessage != null) {
        setState(() {
          _messages.add(sentMessage);
        });
        _scrollToBottom();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Ubutumwa bw\'ihutirwa bwoherejwe'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      } else {
        _showError('Ubutumwa bw\'ihutirwa ntibwashobotse koherezwa.');
      }
    } catch (e) {
      debugPrint('Error sending emergency message: $e');
      _showError('Ikosa ryabaye mu kohereza ubutumwa bw\'ihutirwa.');
    }
  }

  void _findNearestHealthFacility() {
    _showError('Gushakisha ikigo cy\'ubuzima bizashyirwa mu bikorwa vuba...');
  }

  void _sendImage() {
    _showError('Kohereza amafoto bizashyirwa mu bikorwa vuba...');
  }

  void _sendFile() {
    _showError('Kohereza amadosiye bizashyirwa mu bikorwa vuba...');
  }

  void _sendLocation() {
    _showError('Kohereza aho uri bizashyirwa mu bikorwa vuba...');
  }
}
