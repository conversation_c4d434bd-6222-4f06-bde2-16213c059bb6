class ContraceptionMethod {
  final String id;
  final String userId;
  final String methodType;
  final String methodName;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isActive;
  final double effectiveness;
  final List<String> sideEffects;
  final String instructions;
  final DateTime? nextAppointment;
  final String? notes;
  final String? prescribedBy;
  final int? satisfactionRating;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ContraceptionMethod({
    required this.id,
    required this.userId,
    required this.methodType,
    String? methodName,
    required this.startDate,
    this.endDate,
    required this.isActive,
    double? effectiveness,
    List<String>? sideEffects,
    String? instructions,
    this.nextAppointment,
    this.notes,
    this.prescribedBy,
    this.satisfactionRating,
    required this.createdAt,
    this.updatedAt,
  }) : methodName = methodName ?? methodType,
       effectiveness = effectiveness ?? _getDefaultEffectiveness(methodType),
       sideEffects = sideEffects ?? [],
       instructions = instructions ?? _getDefaultInstructions(methodType);

  static double _getDefaultEffectiveness(String methodType) {
    switch (methodType.toLowerCase()) {
      case 'pill':
        return 91.0;
      case 'iud':
        return 99.0;
      case 'implant':
        return 99.0;
      case 'injection':
        return 94.0;
      case 'patch':
        return 91.0;
      case 'ring':
        return 91.0;
      case 'condom':
        return 85.0;
      default:
        return 85.0;
    }
  }

  static String _getDefaultInstructions(String methodType) {
    switch (methodType.toLowerCase()) {
      case 'pill':
        return 'Nywa buri munsi ku gihe kimwe';
      case 'iud':
        return 'Gusuzuma buri myaka 5';
      case 'implant':
        return 'Gusimbura nyuma y\'imyaka 3';
      case 'injection':
        return 'Urushinge buri mezi 3';
      case 'patch':
        return 'Guhindura buri cyumweru';
      case 'ring':
        return 'Gusimbura buri kwezi';
      case 'condom':
        return 'Gukoresha buri gihe';
      default:
        return 'Gukurikiza amabwiriza y\'ubuvuzi';
    }
  }

  factory ContraceptionMethod.fromJson(Map<String, dynamic> json) {
    return ContraceptionMethod(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      methodType: json['methodType']?.toString() ?? '',
      methodName: json['methodName']?.toString(),
      startDate:
          json['startDate'] != null
              ? DateTime.parse(json['startDate'].toString())
              : DateTime.now(),
      endDate:
          json['endDate'] != null
              ? DateTime.parse(json['endDate'].toString())
              : null,
      isActive: json['isActive'] ?? true,
      effectiveness: (json['effectiveness'] as num?)?.toDouble(),
      sideEffects:
          json['sideEffects'] != null
              ? List<String>.from(json['sideEffects'])
              : null,
      instructions: json['instructions']?.toString(),
      nextAppointment:
          json['nextAppointment'] != null
              ? DateTime.parse(json['nextAppointment'].toString())
              : null,
      notes: json['notes']?.toString(),
      prescribedBy: json['prescribedBy']?.toString(),
      satisfactionRating: json['satisfactionRating'] as int?,
      createdAt:
          json['createdAt'] != null
              ? DateTime.parse(json['createdAt'].toString())
              : DateTime.now(),
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'].toString())
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'methodType': methodType,
      'methodName': methodName,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isActive': isActive,
      'effectiveness': effectiveness,
      'sideEffects': sideEffects,
      'instructions': instructions,
      'nextAppointment': nextAppointment?.toIso8601String(),
      'notes': notes,
      'prescribedBy': prescribedBy,
      'satisfactionRating': satisfactionRating,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
}
