class ContraceptionMethod {
  final String id;
  final String userId;
  final String methodType;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isActive;
  final String? notes;
  final String? prescribedBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ContraceptionMethod({
    required this.id,
    required this.userId,
    required this.methodType,
    required this.startDate,
    this.endDate,
    required this.isActive,
    this.notes,
    this.prescribedBy,
    required this.createdAt,
    this.updatedAt,
  });

  factory ContraceptionMethod.fromJson(Map<String, dynamic> json) {
    return ContraceptionMethod(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      methodType: json['methodType']?.toString() ?? '',
      startDate:
          json['startDate'] != null
              ? DateTime.parse(json['startDate'].toString())
              : DateTime.now(),
      endDate:
          json['endDate'] != null
              ? DateTime.parse(json['endDate'].toString())
              : null,
      isActive: json['isActive'] ?? true,
      notes: json['notes']?.toString(),
      prescribedBy: json['prescribedBy']?.toString(),
      createdAt:
          json['createdAt'] != null
              ? DateTime.parse(json['createdAt'].toString())
              : DateTime.now(),
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'].toString())
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'methodType': methodType,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isActive': isActive,
      'notes': notes,
      'prescribedBy': prescribedBy,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
}
