import 'package:json_annotation/json_annotation.dart';

part 'contraception_method_model.g.dart';

@JsonSerializable()
class ContraceptionMethod {
  final String id;
  final String userId;
  final String methodType;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isActive;
  final String? notes;
  final String? prescribedBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ContraceptionMethod({
    required this.id,
    required this.userId,
    required this.methodType,
    required this.startDate,
    this.endDate,
    required this.isActive,
    this.notes,
    this.prescribedBy,
    required this.createdAt,
    this.updatedAt,
  });

  factory ContraceptionMethod.fromJson(Map<String, dynamic> json) =>
      _$ContraceptionMethodFromJson(json);

  Map<String, dynamic> toJson() => _$ContraceptionMethodToJson(this);
}
