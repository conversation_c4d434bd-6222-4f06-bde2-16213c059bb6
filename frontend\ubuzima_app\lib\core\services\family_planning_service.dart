import 'package:flutter/foundation.dart';
import 'http_client.dart';
import '../models/pregnancy_plan_model.dart';
import '../models/contraception_method_model.dart';
import '../models/menstrual_cycle_model.dart';
import '../models/education_lesson_model.dart';

class FamilyPlanningService {
  final HttpClient _httpClient = HttpClient();

  /// Get family planning overview with statistics
  Future<Map<String, dynamic>> getFamilyPlanningOverview() async {
    try {
      // Get current user ID (you may need to implement this based on your auth system)
      final userId = 3; // For now, using the logged-in user ID

      final response = await _httpClient.get(
        '/family-planning/overview?userId=$userId',
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          return responseData['overview'] ?? {};
        }
      }

      // Return empty data if API fails
      return {};
    } catch (e) {
      debugPrint('Error fetching family planning overview: $e');
      return {};
    }
  }

  /// Get pregnancy plans for user
  Future<List<PregnancyPlan>> getPregnancyPlans({String? status}) async {
    try {
      final userId = 3; // Current user ID
      String endpoint = '/family-planning/pregnancy-plans?userId=$userId';

      if (status != null) {
        endpoint += '&status=$status';
      }

      final response = await _httpClient.get(endpoint);

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['plans'] != null) {
          final plansList = responseData['plans'] as List<dynamic>;

          return plansList
              .map(
                (json) => PregnancyPlan.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching pregnancy plans: $e');
      return [];
    }
  }

  /// Create new pregnancy plan
  Future<PregnancyPlan?> createPregnancyPlan({
    required String planName,
    required DateTime targetDate,
    String? description,
    String? partnerInvolvement,
  }) async {
    try {
      final userId = 3; // Current user ID

      final requestData = {
        'userId': userId,
        'planName': planName,
        'targetDate': targetDate.toIso8601String(),
        'description': description,
        'partnerInvolvement': partnerInvolvement,
        'currentStatus': 'PLANNING',
      };

      final response = await _httpClient.post(
        '/pregnancy-plans',
        data: requestData,
      );

      if (response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['plan'] != null) {
          return PregnancyPlan.fromJson(
            responseData['plan'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error creating pregnancy plan: $e');
      return null;
    }
  }

  /// Get contraception methods for user
  Future<List<ContraceptionMethod>> getContraceptionMethods({
    bool? activeOnly,
  }) async {
    try {
      final userId = 3; // Current user ID
      String endpoint = '/family-planning/contraception-methods?userId=$userId';

      if (activeOnly != null) {
        endpoint += '&activeOnly=$activeOnly';
      }

      final response = await _httpClient.get(endpoint);

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true &&
            responseData['methods'] != null) {
          final methodsList = responseData['methods'] as List<dynamic>;

          return methodsList
              .map(
                (json) =>
                    ContraceptionMethod.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching contraception methods: $e');
      return [];
    }
  }

  /// Start new contraception method
  Future<ContraceptionMethod?> startContraceptionMethod({
    required String methodType,
    required DateTime startDate,
    String? notes,
    String? prescribedBy,
  }) async {
    try {
      final userId = 3; // Current user ID

      final requestData = {
        'userId': userId,
        'methodType': methodType,
        'startDate': startDate.toIso8601String(),
        'notes': notes,
        'prescribedBy': prescribedBy,
        'isActive': true,
      };

      final response = await _httpClient.post(
        '/contraception',
        data: requestData,
      );

      if (response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['method'] != null) {
          return ContraceptionMethod.fromJson(
            responseData['method'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error starting contraception method: $e');
      return null;
    }
  }

  /// Get menstrual cycles for user
  Future<List<MenstrualCycle>> getMenstrualCycles({int? limit}) async {
    try {
      final userId = 3; // Current user ID
      String endpoint = '/family-planning/menstrual-cycles?userId=$userId';

      if (limit != null) {
        endpoint += '&limit=$limit';
      }

      final response = await _httpClient.get(endpoint);

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['cycles'] != null) {
          final cyclesList = responseData['cycles'] as List<dynamic>;

          return cyclesList
              .map(
                (json) => MenstrualCycle.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching menstrual cycles: $e');
      return [];
    }
  }

  /// Record new menstrual cycle
  Future<MenstrualCycle?> recordMenstrualCycle({
    required DateTime startDate,
    DateTime? endDate,
    int? flowIntensity,
    List<String>? symptoms,
    String? notes,
  }) async {
    try {
      final userId = 3; // Current user ID

      final requestData = {
        'userId': userId,
        'startDate': startDate.toIso8601String(),
        'endDate': endDate?.toIso8601String(),
        'flowIntensity': flowIntensity,
        'symptoms': symptoms,
        'notes': notes,
      };

      final response = await _httpClient.post(
        '/menstrual-cycles',
        data: requestData,
      );

      if (response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['cycle'] != null) {
          return MenstrualCycle.fromJson(
            responseData['cycle'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error recording menstrual cycle: $e');
      return null;
    }
  }

  /// Get family planning education lessons
  Future<List<EducationLesson>> getFamilyPlanningEducation({
    String? category,
    String? search,
    int? limit,
  }) async {
    try {
      String endpoint = '/family-planning/education';
      final queryParams = <String, String>{};

      if (category != null) queryParams['category'] = category;
      if (search != null) queryParams['search'] = search;
      if (limit != null) queryParams['limit'] = limit.toString();

      if (queryParams.isNotEmpty) {
        endpoint +=
            '?' +
            queryParams.entries.map((e) => '${e.key}=${e.value}').join('&');
      }

      final response = await _httpClient.get(endpoint);

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true &&
            responseData['lessons'] != null) {
          final lessonsList = responseData['lessons'] as List<dynamic>;

          return lessonsList
              .map(
                (json) =>
                    EducationLesson.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching family planning education: $e');
      return [];
    }
  }

  /// Get fertility predictions based on menstrual cycles
  Future<Map<String, dynamic>> getFertilityPredictions() async {
    try {
      final userId = 3; // Current user ID

      final response = await _httpClient.get(
        '/family-planning/fertility-predictions?userId=$userId',
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          return responseData['predictions'] ?? {};
        }
      }

      return {};
    } catch (e) {
      debugPrint('Error fetching fertility predictions: $e');
      return {};
    }
  }

  /// Get current cycle information
  Future<Map<String, dynamic>> getCurrentCycle() async {
    try {
      final userId = 3; // Current user ID

      final response = await _httpClient.get(
        '/family-planning/current-cycle?userId=$userId',
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          return responseData['currentCycle'] ?? {};
        }
      }

      return {};
    } catch (e) {
      debugPrint('Error fetching current cycle: $e');
      return {};
    }
  }
}
