import 'package:flutter/foundation.dart';
import 'http_client.dart';
import '../models/pregnancy_plan_model.dart';
import '../models/contraception_method_model.dart';
import '../models/menstrual_cycle_model.dart';
import '../models/education_lesson_model.dart';

class FamilyPlanningService {
  final HttpClient _httpClient = HttpClient();

  /// Get family planning overview with statistics
  Future<Map<String, dynamic>> getFamilyPlanningOverview() async {
    try {
      // Get current user ID (you may need to implement this based on your auth system)
      final userId = 3; // For now, using the logged-in user ID

      final response = await _httpClient.get(
        '/family-planning/overview?userId=$userId',
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          return responseData['overview'] ?? {};
        }
      }

      // Return empty data if API fails
      return {};
    } catch (e) {
      debugPrint('Error fetching family planning overview: $e');
      return {};
    }
  }

  /// Get pregnancy plans for user
  Future<List<PregnancyPlan>> getPregnancyPlans({String? status}) async {
    try {
      final userId = 3; // Current user ID
      String endpoint = '/family-planning/pregnancy-plans?userId=$userId';

      if (status != null) {
        endpoint += '&status=$status';
      }

      final response = await _httpClient.get(endpoint);

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['plans'] != null) {
          final plansList = responseData['plans'] as List<dynamic>;

          return plansList
              .map(
                (json) => PregnancyPlan.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching pregnancy plans: $e');
      return [];
    }
  }

  /// Create new pregnancy plan
  Future<PregnancyPlan?> createPregnancyPlan({
    required String planName,
    required DateTime targetDate,
    String? description,
    String? partnerInvolvement,
  }) async {
    try {
      final userId = 3; // Current user ID

      final requestData = {
        'userId': userId,
        'planName': planName,
        'targetDate': targetDate.toIso8601String(),
        'description': description,
        'partnerInvolvement': partnerInvolvement,
        'currentStatus': 'PLANNING',
      };

      final response = await _httpClient.post(
        '/pregnancy-plans',
        data: requestData,
      );

      if (response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['plan'] != null) {
          return PregnancyPlan.fromJson(
            responseData['plan'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error creating pregnancy plan: $e');
      return null;
    }
  }

  /// Get contraception methods for user
  Future<List<ContraceptionMethod>> getContraceptionMethods({
    bool? activeOnly,
  }) async {
    try {
      final userId = 3; // Current user ID
      String endpoint = '/family-planning/contraception-methods?userId=$userId';

      if (activeOnly != null) {
        endpoint += '&activeOnly=$activeOnly';
      }

      final response = await _httpClient.get(endpoint);

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true &&
            responseData['methods'] != null) {
          final methodsList = responseData['methods'] as List<dynamic>;

          return methodsList
              .map(
                (json) =>
                    ContraceptionMethod.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching contraception methods: $e');
      return [];
    }
  }

  /// Start new contraception method
  Future<ContraceptionMethod?> startContraceptionMethod({
    required String methodType,
    required DateTime startDate,
    String? notes,
    String? prescribedBy,
  }) async {
    try {
      final userId = 3; // Current user ID

      final requestData = {
        'userId': userId,
        'methodType': methodType,
        'startDate': startDate.toIso8601String(),
        'notes': notes,
        'prescribedBy': prescribedBy,
        'isActive': true,
      };

      final response = await _httpClient.post(
        '/contraception',
        data: requestData,
      );

      if (response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['method'] != null) {
          return ContraceptionMethod.fromJson(
            responseData['method'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error starting contraception method: $e');
      return null;
    }
  }

  /// Get menstrual cycles for user
  Future<List<MenstrualCycle>> getMenstrualCycles({int? limit}) async {
    try {
      final userId = 3; // Current user ID
      String endpoint = '/family-planning/menstrual-cycles?userId=$userId';

      if (limit != null) {
        endpoint += '&limit=$limit';
      }

      final response = await _httpClient.get(endpoint);

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['cycles'] != null) {
          final cyclesList = responseData['cycles'] as List<dynamic>;

          return cyclesList
              .map(
                (json) => MenstrualCycle.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching menstrual cycles: $e');
      return [];
    }
  }

  /// Record new menstrual cycle
  Future<MenstrualCycle?> recordMenstrualCycle({
    required DateTime startDate,
    DateTime? endDate,
    int? flowIntensity,
    List<String>? symptoms,
    String? notes,
  }) async {
    try {
      final userId = 3; // Current user ID

      final requestData = {
        'userId': userId,
        'startDate': startDate.toIso8601String(),
        'endDate': endDate?.toIso8601String(),
        'flowIntensity': flowIntensity,
        'symptoms': symptoms,
        'notes': notes,
      };

      final response = await _httpClient.post(
        '/menstrual-cycles',
        data: requestData,
      );

      if (response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['cycle'] != null) {
          return MenstrualCycle.fromJson(
            responseData['cycle'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error recording menstrual cycle: $e');
      return null;
    }
  }

  /// Get family planning education lessons
  Future<List<EducationLesson>> getFamilyPlanningEducation({
    String? category,
    String? search,
    int? limit,
  }) async {
    try {
      String endpoint = '/family-planning/education';
      final queryParams = <String, String>{};

      if (category != null) queryParams['category'] = category;
      if (search != null) queryParams['search'] = search;
      if (limit != null) queryParams['limit'] = limit.toString();

      if (queryParams.isNotEmpty) {
        endpoint +=
            '?' +
            queryParams.entries.map((e) => '${e.key}=${e.value}').join('&');
      }

      final response = await _httpClient.get(endpoint);

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true &&
            responseData['lessons'] != null) {
          final lessonsList = responseData['lessons'] as List<dynamic>;

          return lessonsList
              .map(
                (json) =>
                    EducationLesson.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching family planning education: $e');
      return [];
    }
  }

  /// Get fertility predictions based on menstrual cycles
  Future<Map<String, dynamic>> getFertilityPredictions() async {
    try {
      final userId = 3; // Current user ID

      final response = await _httpClient.get(
        '/family-planning/fertility-predictions?userId=$userId',
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          return responseData['predictions'] ?? {};
        }
      }

      return {};
    } catch (e) {
      debugPrint('Error fetching fertility predictions: $e');
      return {};
    }
  }

  /// Get current cycle information
  Future<Map<String, dynamic>> getCurrentCycle() async {
    try {
      final userId = 3; // Current user ID

      final response = await _httpClient.get(
        '/family-planning/current-cycle?userId=$userId',
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          return responseData['currentCycle'] ?? {};
        }
      }

      return {};
    } catch (e) {
      debugPrint('Error fetching current cycle: $e');
      return {};
    }
  }

  /// Get partner involvement data
  Future<Map<String, dynamic>> getPartnerInvolvement() async {
    try {
      final userId = 3; // Current user ID

      final response = await _httpClient.get(
        '/partner-involvement?userId=$userId',
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          return {
            'partner': responseData['partner'] ?? {},
            'sharedPlans': responseData['sharedPlans'] ?? [],
            'communications': responseData['communications'] ?? [],
          };
        }
      }

      return {'partner': {}, 'sharedPlans': [], 'communications': []};
    } catch (e) {
      debugPrint('Error fetching partner involvement: $e');
      return {'partner': {}, 'sharedPlans': [], 'communications': []};
    }
  }

  // COMPREHENSIVE CRUD OPERATIONS

  /// Create new contraception method
  Future<ContraceptionMethod?> createContraceptionMethod({
    required String methodType,
    required DateTime startDate,
    String? notes,
    String? prescribedBy,
  }) async {
    try {
      final userId = 3; // Current user ID

      final requestData = {
        'userId': userId,
        'methodType': methodType,
        'startDate': startDate.toIso8601String(),
        'isActive': true,
        'notes': notes,
        'prescribedBy': prescribedBy,
      };

      final response = await _httpClient.post(
        '/contraception-methods',
        data: requestData,
      );

      if (response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['method'] != null) {
          return ContraceptionMethod.fromJson(
            responseData['method'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error creating contraception method: $e');
      return null;
    }
  }

  /// Update contraception method
  Future<ContraceptionMethod?> updateContraceptionMethod({
    required String methodId,
    String? methodType,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    String? notes,
    String? prescribedBy,
  }) async {
    try {
      final requestData = <String, dynamic>{};

      if (methodType != null) requestData['methodType'] = methodType;
      if (startDate != null)
        requestData['startDate'] = startDate.toIso8601String();
      if (endDate != null) requestData['endDate'] = endDate.toIso8601String();
      if (isActive != null) requestData['isActive'] = isActive;
      if (notes != null) requestData['notes'] = notes;
      if (prescribedBy != null) requestData['prescribedBy'] = prescribedBy;

      final response = await _httpClient.put(
        '/contraception-methods/$methodId',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['method'] != null) {
          return ContraceptionMethod.fromJson(
            responseData['method'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error updating contraception method: $e');
      return null;
    }
  }

  /// Delete contraception method
  Future<bool> deleteContraceptionMethod(String methodId) async {
    try {
      final response = await _httpClient.delete(
        '/contraception-methods/$methodId',
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        return responseData['success'] == true;
      }

      return false;
    } catch (e) {
      debugPrint('Error deleting contraception method: $e');
      return false;
    }
  }

  /// Create new menstrual cycle record
  Future<MenstrualCycle?> createMenstrualCycle({
    required DateTime startDate,
    int? cycleLength,
    int? periodLength,
    String? symptoms,
    String? notes,
  }) async {
    try {
      final userId = 3; // Current user ID

      final requestData = {
        'userId': userId,
        'startDate': startDate.toIso8601String(),
        'cycleLength': cycleLength,
        'periodLength': periodLength,
        'symptoms': symptoms,
        'notes': notes,
      };

      final response = await _httpClient.post(
        '/menstrual-cycles',
        data: requestData,
      );

      if (response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['cycle'] != null) {
          return MenstrualCycle.fromJson(
            responseData['cycle'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error creating menstrual cycle: $e');
      return null;
    }
  }

  /// Update menstrual cycle record
  Future<MenstrualCycle?> updateMenstrualCycle({
    required String cycleId,
    DateTime? startDate,
    DateTime? endDate,
    int? cycleLength,
    int? periodLength,
    String? symptoms,
    String? notes,
  }) async {
    try {
      final requestData = <String, dynamic>{};

      if (startDate != null)
        requestData['startDate'] = startDate.toIso8601String();
      if (endDate != null) requestData['endDate'] = endDate.toIso8601String();
      if (cycleLength != null) requestData['cycleLength'] = cycleLength;
      if (periodLength != null) requestData['periodLength'] = periodLength;
      if (symptoms != null) requestData['symptoms'] = symptoms;
      if (notes != null) requestData['notes'] = notes;

      final response = await _httpClient.put(
        '/menstrual-cycles/$cycleId',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['cycle'] != null) {
          return MenstrualCycle.fromJson(
            responseData['cycle'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error updating menstrual cycle: $e');
      return null;
    }
  }

  /// Delete menstrual cycle record
  Future<bool> deleteMenstrualCycle(String cycleId) async {
    try {
      final response = await _httpClient.delete('/menstrual-cycles/$cycleId');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        return responseData['success'] == true;
      }

      return false;
    } catch (e) {
      debugPrint('Error deleting menstrual cycle: $e');
      return false;
    }
  }

  /// Create new pregnancy plan (CRUD version)
  Future<PregnancyPlan?> createPregnancyPlanCrud({
    required String planName,
    required DateTime targetDate,
    String? description,
    String? partnerInvolvement,
  }) async {
    try {
      final userId = 3; // Current user ID

      final requestData = {
        'userId': userId,
        'planName': planName,
        'targetDate': targetDate.toIso8601String(),
        'description': description,
        'currentStatus': 'PLANNING',
        'partnerInvolvement': partnerInvolvement,
      };

      final response = await _httpClient.post(
        '/pregnancy-plans',
        data: requestData,
      );

      if (response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['plan'] != null) {
          return PregnancyPlan.fromJson(
            responseData['plan'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error creating pregnancy plan: $e');
      return null;
    }
  }

  /// Update pregnancy plan
  Future<PregnancyPlan?> updatePregnancyPlan({
    required String planId,
    String? planName,
    DateTime? targetDate,
    String? description,
    String? currentStatus,
    String? partnerInvolvement,
  }) async {
    try {
      final requestData = <String, dynamic>{};

      if (planName != null) requestData['planName'] = planName;
      if (targetDate != null)
        requestData['targetDate'] = targetDate.toIso8601String();
      if (description != null) requestData['description'] = description;
      if (currentStatus != null) requestData['currentStatus'] = currentStatus;
      if (partnerInvolvement != null)
        requestData['partnerInvolvement'] = partnerInvolvement;

      final response = await _httpClient.put(
        '/pregnancy-plans/$planId',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['plan'] != null) {
          return PregnancyPlan.fromJson(
            responseData['plan'] as Map<String, dynamic>,
          );
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error updating pregnancy plan: $e');
      return null;
    }
  }

  /// Delete pregnancy plan
  Future<bool> deletePregnancyPlan(String planId) async {
    try {
      final response = await _httpClient.delete('/pregnancy-plans/$planId');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        return responseData['success'] == true;
      }

      return false;
    } catch (e) {
      debugPrint('Error deleting pregnancy plan: $e');
      return false;
    }
  }

  /// Generic error handling for all CRUD operations
  Map<String, String> handleApiError(dynamic error) {
    if (error.toString().contains('404')) {
      return {
        'title': 'Ntibisanzwe',
        'message': 'Amakuru asabwa ntabwo abonetse.',
      };
    } else if (error.toString().contains('401')) {
      return {
        'title': 'Ntabwo Wemerewe',
        'message': 'Ntabwo ufite uburenganzira bwo gukora iki gikorwa.',
      };
    } else if (error.toString().contains('500')) {
      return {
        'title': 'Ikosa ry\'Seriveri',
        'message': 'Habaye ikosa ku seriveri. Ongera ugerageze nyuma.',
      };
    } else {
      return {
        'title': 'Ikosa',
        'message': 'Habaye ikosa ritazwi. Ongera ugerageze.',
      };
    }
  }
}
