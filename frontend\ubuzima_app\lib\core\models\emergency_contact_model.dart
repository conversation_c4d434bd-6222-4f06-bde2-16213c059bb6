import 'package:json_annotation/json_annotation.dart';

part 'emergency_contact_model.g.dart';

@JsonSerializable()
class EmergencyContact {
  final String id;
  final String name;
  final String phoneNumber;
  final String relationship;
  final String? email;
  final bool isSystemContact;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  EmergencyContact({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.relationship,
    this.email,
    required this.isSystemContact,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
  });

  factory EmergencyContact.fromJson(Map<String, dynamic> json) =>
      _$EmergencyContactFromJson(json);

  Map<String, dynamic> toJson() => _$EmergencyContactToJson(this);
}
