import 'package:flutter/foundation.dart';
import '../models/health_facility.dart';
import '../models/user_model.dart';
import 'health_facility_service.dart';
import 'http_client.dart';
import 'api_response.dart';

/// Enhanced service for clinic-specific functionality
class ClinicManagementService {
  final HealthFacilityService _facilityService = HealthFacilityService();
  final HttpClient _httpClient = HttpClient();

  /// Get clinics by type with enhanced filtering
  Future<List<HealthFacility>> getClinicsByType({
    FacilityType? type,
    String? district,
    String? sector,
    bool? hasEmergencyServices,
    bool? hasFamilyPlanning,
    bool? hasMaternityWard,
    bool? hasLaboratory,
    bool? hasPharmacy,
    double? latitude,
    double? longitude,
    double? radiusKm,
    int limit = 50,
  }) async {
    try {
      final queryParams = <String, String>{
        'limit': limit.toString(),
      };

      if (type != null) queryParams['type'] = type.value;
      if (district != null) queryParams['district'] = district;
      if (sector != null) queryParams['sector'] = sector;
      if (hasEmergencyServices != null) queryParams['hasEmergencyServices'] = hasEmergencyServices.toString();
      if (hasFamilyPlanning != null) queryParams['hasFamilyPlanning'] = hasFamilyPlanning.toString();
      if (hasMaternityWard != null) queryParams['hasMaternityWard'] = hasMaternityWard.toString();
      if (hasLaboratory != null) queryParams['hasLaboratory'] = hasLaboratory.toString();
      if (hasPharmacy != null) queryParams['hasPharmacy'] = hasPharmacy.toString();
      if (latitude != null) queryParams['latitude'] = latitude.toString();
      if (longitude != null) queryParams['longitude'] = longitude.toString();
      if (radiusKm != null) queryParams['radiusKm'] = radiusKm.toString();

      final response = await _httpClient.get(
        '/facilities/search',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['facilities'] != null) {
          final facilitiesList = responseData['facilities'] as List<dynamic>;
          
          return facilitiesList
              .map((json) => HealthFacility.fromJson(json as Map<String, dynamic>))
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching clinics by type: $e');
      return [];
    }
  }

  /// Get clinic services and specializations
  Future<List<String>> getClinicServices(String facilityId) async {
    try {
      final response = await _httpClient.get('/facilities/$facilityId/services');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true && responseData['services'] != null) {
          return List<String>.from(responseData['services']);
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching clinic services: $e');
      return [];
    }
  }

  /// Get clinic operating hours
  Future<Map<String, String>> getClinicOperatingHours(String facilityId) async {
    try {
      final response = await _httpClient.get('/facilities/$facilityId/hours');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true && responseData['operatingHours'] != null) {
          return Map<String, String>.from(responseData['operatingHours']);
        }
      }

      return {};
    } catch (e) {
      debugPrint('Error fetching clinic operating hours: $e');
      return {};
    }
  }

  /// Check if clinic is currently open
  Future<bool> isClinicOpen(String facilityId) async {
    try {
      final response = await _httpClient.get('/facilities/$facilityId/is-open');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true) {
          return responseData['isOpen'] ?? false;
        }
      }

      return false;
    } catch (e) {
      debugPrint('Error checking clinic status: $e');
      return false;
    }
  }

  /// Get clinic reviews and ratings
  Future<Map<String, dynamic>> getClinicReviews(String facilityId) async {
    try {
      final response = await _httpClient.get('/facilities/$facilityId/reviews');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true) {
          return {
            'rating': responseData['averageRating'] ?? 0.0,
            'totalReviews': responseData['totalReviews'] ?? 0,
            'reviews': responseData['reviews'] ?? [],
          };
        }
      }

      return {'rating': 0.0, 'totalReviews': 0, 'reviews': []};
    } catch (e) {
      debugPrint('Error fetching clinic reviews: $e');
      return {'rating': 0.0, 'totalReviews': 0, 'reviews': []};
    }
  }

  /// Get health workers at a specific clinic
  Future<List<User>> getClinicHealthWorkers(String facilityId) async {
    try {
      final response = await _httpClient.get('/facilities/$facilityId/health-workers');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true && responseData['healthWorkers'] != null) {
          final workersList = responseData['healthWorkers'] as List<dynamic>;
          
          return workersList
              .map((json) => User.fromJson(json as Map<String, dynamic>))
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching clinic health workers: $e');
      return [];
    }
  }

  /// Get available appointment slots for a clinic
  Future<List<DateTime>> getClinicAvailableSlots({
    required String facilityId,
    required DateTime date,
    String? healthWorkerId,
  }) async {
    try {
      final queryParams = <String, String>{
        'facilityId': facilityId,
        'date': date.toIso8601String().split('T')[0],
      };

      if (healthWorkerId != null) {
        queryParams['healthWorkerId'] = healthWorkerId;
      }

      final response = await _httpClient.get(
        '/appointments/available-slots',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true && responseData['availableSlots'] != null) {
          final slotsList = responseData['availableSlots'] as List<dynamic>;
          
          return slotsList
              .map((slot) => DateTime.parse(slot['startTime']))
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching clinic available slots: $e');
      return [];
    }
  }

  /// Book appointment at clinic
  Future<bool> bookClinicAppointment({
    required String facilityId,
    required String healthWorkerId,
    required DateTime appointmentTime,
    required String clientId,
    String? notes,
    String? appointmentType,
  }) async {
    try {
      final requestData = {
        'facilityId': facilityId,
        'healthWorkerId': healthWorkerId,
        'clientId': clientId,
        'appointmentTime': appointmentTime.toIso8601String(),
        'notes': notes,
        'appointmentType': appointmentType ?? 'CONSULTATION',
      };

      final response = await _httpClient.post(
        '/appointments',
        data: requestData,
      );

      if (response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;
        return responseData['success'] == true;
      }

      return false;
    } catch (e) {
      debugPrint('Error booking clinic appointment: $e');
      return false;
    }
  }

  /// Get clinic contact information
  Future<Map<String, String>> getClinicContactInfo(String facilityId) async {
    try {
      final facility = await _facilityService.getHealthFacilityById(facilityId);
      
      if (facility != null) {
        return {
          'phone': facility.phone ?? '',
          'email': facility.email ?? '',
          'address': facility.address ?? '',
          'emergencyContact': '', // Add if available in model
        };
      }

      return {};
    } catch (e) {
      debugPrint('Error fetching clinic contact info: $e');
      return {};
    }
  }

  /// Calculate distance to clinic
  double calculateDistanceToClinic({
    required double userLatitude,
    required double userLongitude,
    required double clinicLatitude,
    required double clinicLongitude,
  }) {
    // Simple distance calculation (Haversine formula would be more accurate)
    const double earthRadius = 6371; // km
    
    final double dLat = _degreesToRadians(clinicLatitude - userLatitude);
    final double dLon = _degreesToRadians(clinicLongitude - userLongitude);
    
    final double a = 
        (dLat / 2).sin() * (dLat / 2).sin() +
        userLatitude.cos() * clinicLatitude.cos() *
        (dLon / 2).sin() * (dLon / 2).sin();
    
    final double c = 2 * a.sqrt().asin();
    
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (3.14159265359 / 180);
  }
}
