import 'dart:convert';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import 'auth_service.dart';

class EmergencyContact {
  final String id;
  final String name;
  final String phoneNumber;
  final String relationship;
  final bool isSystem;
  final DateTime createdAt;

  EmergencyContact({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.relationship,
    this.isSystem = false,
    required this.createdAt,
  });

  factory EmergencyContact.fromJson(Map<String, dynamic> json) {
    return EmergencyContact(
      id: json['id'].toString(),
      name: json['name'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      relationship: json['relationship'] ?? '',
      isSystem: json['isSystem'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'relationship': relationship,
      'isSystem': isSystem,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class EmergencyService {
  final String baseUrl = AppConstants.baseUrl;
  final AuthService _authService = AuthService();

  Future<String?> _getAuthToken() async {
    return _authService.currentToken;
  }

  Map<String, String> _getHeaders(String? token) {
    final headers = {'Content-Type': 'application/json'};
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  Future<List<EmergencyContact>> getEmergencyContacts() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/emergency-contacts');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> contacts = data['contacts'] ?? [];
          return contacts
              .map((json) => EmergencyContact.fromJson(json))
              .toList();
        }
      }

      // Return sample data if API fails
      return _getSampleEmergencyContacts();
    } catch (e) {
      print('Error loading emergency contacts: $e');
      return _getSampleEmergencyContacts();
    }
  }

  Future<List<EmergencyContact>> getSystemEmergencyContacts() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/emergency-contacts/system');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> contacts = data['contacts'] ?? [];
          return contacts
              .map((json) => EmergencyContact.fromJson(json))
              .toList();
        }
      }

      // Return sample system contacts if API fails
      return _getSampleSystemContacts();
    } catch (e) {
      print('Error loading system emergency contacts: $e');
      return _getSampleSystemContacts();
    }
  }

  Future<void> addEmergencyContact({
    required String name,
    required String phoneNumber,
    required String relationship,
  }) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/emergency-contacts');

      final body = json.encode({
        'name': name,
        'phoneNumber': phoneNumber,
        'relationship': relationship,
      });

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode != 201) {
        throw Exception('Failed to add emergency contact');
      }
    } catch (e) {
      print('Error adding emergency contact: $e');
      rethrow;
    }
  }

  Future<void> updateEmergencyContact({
    required String id,
    required String name,
    required String phoneNumber,
    required String relationship,
  }) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/emergency-contacts/$id');

      final body = json.encode({
        'name': name,
        'phoneNumber': phoneNumber,
        'relationship': relationship,
      });

      final response = await http.put(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to update emergency contact');
      }
    } catch (e) {
      print('Error updating emergency contact: $e');
      rethrow;
    }
  }

  Future<void> deleteEmergencyContact(String id) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/emergency-contacts/$id');

      final response = await http.delete(uri, headers: _getHeaders(token));

      if (response.statusCode != 200) {
        throw Exception('Failed to delete emergency contact');
      }
    } catch (e) {
      print('Error deleting emergency contact: $e');
      rethrow;
    }
  }

  Future<void> triggerEmergencyAlert({
    required String message,
    String? location,
  }) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/emergency/alert');

      final body = json.encode({
        'message': message,
        'location': location,
        'timestamp': DateTime.now().toIso8601String(),
      });

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to trigger emergency alert');
      }
    } catch (e) {
      print('Error triggering emergency alert: $e');
      rethrow;
    }
  }

  List<EmergencyContact> _getSampleEmergencyContacts() {
    debugPrint('⚠️ No sample emergency contacts - using empty list');
    return [];
  }

  List<EmergencyContact> _getSampleSystemContacts() {
    return [
      EmergencyContact(
        id: 'sys_1',
        name: 'Polisi y\'u Rwanda',
        phoneNumber: '112',
        relationship: 'Umutekano',
        isSystem: true,
        createdAt: DateTime.now(),
      ),
      EmergencyContact(
        id: 'sys_2',
        name: 'Ivuriro Rusizi',
        phoneNumber: '+250788999888',
        relationship: 'Ivuriro',
        isSystem: true,
        createdAt: DateTime.now(),
      ),
      EmergencyContact(
        id: 'sys_3',
        name: 'Ubufasha bw\'ihutirwa',
        phoneNumber: '114',
        relationship: 'Ubufasha',
        isSystem: true,
        createdAt: DateTime.now(),
      ),
      EmergencyContact(
        id: 'sys_4',
        name: 'Kigo Hospital',
        phoneNumber: '+250788777666',
        relationship: 'Ivuriro',
        isSystem: true,
        createdAt: DateTime.now(),
      ),
    ];
  }

  Future<List<String>> getEmergencyTips() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/emergency/tips');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> tips = data['tips'] ?? [];
          return tips.cast<String>();
        }
      }

      return _getSampleEmergencyTips();
    } catch (e) {
      print('Error loading emergency tips: $e');
      return _getSampleEmergencyTips();
    }
  }

  List<String> _getSampleEmergencyTips() {
    return [
      'Buri gihe uzane telefoni yawe ifite amashanyarazi',
      'Menya aho amavuriro ari hafi yawe',
      'Bika numero z\'ababazo b\'ihutirwa mu telefoni yawe',
      'Mu gihe cy\'ihutirwa, hamagara 112 cyangwa 114',
      'Menya aho ukoresha imiti yawe y\'ihutirwa',
      'Bika amafaranga y\'ihutirwa mu nzu yawe',
      'Menya inzira z\'ihutirwa zo kuva mu nzu yawe',
      'Bika ibikoresho by\'ubufasha bw\'ihutirwa',
    ];
  }
}
