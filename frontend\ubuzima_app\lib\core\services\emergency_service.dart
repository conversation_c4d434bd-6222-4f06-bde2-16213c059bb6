import 'package:flutter/foundation.dart';
import 'http_client.dart';
import '../models/emergency_contact_model.dart';

class EmergencyService {
  final HttpClient _httpClient = HttpClient();

  /// Get emergency contacts for user
  Future<List<EmergencyContact>> getEmergencyContacts() async {
    try {
      final userId = 3; // Current user ID
      
      final response = await _httpClient.get('/emergency-contacts?userId=$userId');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['contacts'] != null) {
          final contactsList = responseData['contacts'] as List<dynamic>;

          return contactsList
              .map((json) => EmergencyContact.fromJson(json as Map<String, dynamic>))
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching emergency contacts: $e');
      return [];
    }
  }

  /// Get system emergency contacts
  Future<List<EmergencyContact>> getSystemContacts() async {
    try {
      final response = await _httpClient.get('/emergency-contacts/system');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true && responseData['contacts'] != null) {
          final contactsList = responseData['contacts'] as List<dynamic>;

          return contactsList
              .map((json) => EmergencyContact.fromJson(json as Map<String, dynamic>))
              .toList();
        }
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching system contacts: $e');
      return [];
    }
  }

  /// Add emergency contact
  Future<EmergencyContact?> addEmergencyContact({
    required String name,
    required String phoneNumber,
    required String relationship,
    String? email,
  }) async {
    try {
      final userId = 3; // Current user ID
      
      final requestData = {
        'userId': userId,
        'name': name,
        'phoneNumber': phoneNumber,
        'relationship': relationship,
        'email': email,
        'isSystemContact': false,
        'isActive': true,
      };

      final response = await _httpClient.post('/emergency-contacts', data: requestData);

      if (response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true && responseData['contact'] != null) {
          return EmergencyContact.fromJson(responseData['contact'] as Map<String, dynamic>);
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error adding emergency contact: $e');
      return null;
    }
  }

  /// Delete emergency contact
  Future<bool> deleteEmergencyContact(String contactId) async {
    try {
      final response = await _httpClient.delete('/emergency-contacts/$contactId');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        return responseData['success'] == true;
      }

      return false;
    } catch (e) {
      debugPrint('Error deleting emergency contact: $e');
      return false;
    }
  }

  /// Update emergency contact
  Future<EmergencyContact?> updateEmergencyContact({
    required String contactId,
    required String name,
    required String phoneNumber,
    required String relationship,
    String? email,
  }) async {
    try {
      final requestData = {
        'name': name,
        'phoneNumber': phoneNumber,
        'relationship': relationship,
        'email': email,
      };

      final response = await _httpClient.put('/emergency-contacts/$contactId', data: requestData);

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true && responseData['contact'] != null) {
          return EmergencyContact.fromJson(responseData['contact'] as Map<String, dynamic>);
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error updating emergency contact: $e');
      return null;
    }
  }
}
