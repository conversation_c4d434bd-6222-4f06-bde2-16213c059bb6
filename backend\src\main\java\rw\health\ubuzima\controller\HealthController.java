package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.entity.SupportGroup;
import rw.health.ubuzima.entity.ForumTopic;
import rw.health.ubuzima.entity.CommunityEvent;
import rw.health.ubuzima.repository.SupportGroupRepository;
import rw.health.ubuzima.repository.ForumTopicRepository;
import rw.health.ubuzima.repository.CommunityEventRepository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.List;

@RestController
@RequestMapping("/health")
@RequiredArgsConstructor
public class HealthController {

    private final SupportGroupRepository supportGroupRepository;
    private final ForumTopicRepository forumTopicRepository;
    private final CommunityEventRepository communityEventRepository;

    @GetMapping
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("timestamp", LocalDateTime.now());
        response.put("service", "Ubuzima Backend API");
        response.put("version", "1.0.0");
        return ResponseEntity.ok(response);
    }

    @GetMapping("/community-test")
    public ResponseEntity<Map<String, Object>> communityTest() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Community endpoints are working!");
        response.put("timestamp", LocalDateTime.now());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/simple-test")
    public String simpleTest() {
        return "Simple test works!";
    }

    // Community endpoints
    @GetMapping("/community-overview")
    public ResponseEntity<Map<String, Object>> getCommunityOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();

            // Real community statistics from database
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalGroups", supportGroupRepository.countActiveGroups());
            stats.put("totalTopics", forumTopicRepository.countActiveTopics());
            stats.put("totalEvents", communityEventRepository.countActiveEvents());
            stats.put("upcomingEvents", communityEventRepository.countUpcomingEvents(LocalDateTime.now()));

            overview.put("stats", stats);

            // Get real data for highlights
            List<SupportGroup> popularGroups = supportGroupRepository.findPopularGroups().stream().limit(3).toList();
            List<ForumTopic> popularTopics = forumTopicRepository.findPopularTopics().stream().limit(3).toList();
            List<CommunityEvent> upcomingEvents = communityEventRepository.findUpcomingEvents(LocalDateTime.now()).stream().limit(3).toList();

            overview.put("popularGroups", popularGroups);
            overview.put("popularTopics", popularTopics);
            overview.put("upcomingEvents", upcomingEvents);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "overview", overview
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load community overview: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/community-support-groups")
    public ResponseEntity<Map<String, Object>> getSupportGroups(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String search) {
        try {
            System.out.println("🔍 Getting support groups from database...");

            List<SupportGroup> groups;

            if (search != null && !search.isEmpty()) {
                groups = supportGroupRepository.searchActiveGroups(search);
            } else if (category != null && !category.equals("all")) {
                groups = supportGroupRepository.findByCategoryAndIsActiveTrueOrderByMemberCountDesc(category);
            } else {
                groups = supportGroupRepository.findByIsActiveTrueOrderByCreatedAtDesc();
            }

            System.out.println("📊 Found " + groups.size() + " groups");
            return ResponseEntity.ok(Map.of(
                "success", true,
                "groups", groups
            ));

        } catch (Exception e) {
            System.out.println("❌ Error in getSupportGroups: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load support groups: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/community-forum-topics")
    public ResponseEntity<Map<String, Object>> getForumTopics(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String filter) {
        try {
            System.out.println("🔍 Getting forum topics from database...");

            List<ForumTopic> topics;

            if (search != null && !search.isEmpty()) {
                topics = forumTopicRepository.searchActiveTopics(search);
            } else if (category != null && !category.equals("all")) {
                topics = forumTopicRepository.findByCategoryAndIsActiveTrueOrderByLastActivityAtDesc(category);
            } else if ("popular".equals(filter)) {
                topics = forumTopicRepository.findPopularTopics();
            } else if ("pinned".equals(filter)) {
                topics = forumTopicRepository.findPinnedTopics();
            } else {
                topics = forumTopicRepository.findByIsActiveTrueOrderByLastActivityAtDesc();
            }

            System.out.println("📊 Found " + topics.size() + " topics");
            return ResponseEntity.ok(Map.of(
                "success", true,
                "topics", topics
            ));

        } catch (Exception e) {
            System.out.println("❌ Error in getForumTopics: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load forum topics: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/community-events")
    public ResponseEntity<Map<String, Object>> getCommunityEvents(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String filter,
            @RequestParam(required = false) String search) {
        try {
            System.out.println("🔍 Getting community events from database...");

            List<CommunityEvent> events;

            if (search != null && !search.isEmpty()) {
                events = communityEventRepository.searchEvents(search);
            } else if (type != null && !type.equals("all")) {
                events = communityEventRepository.findByTypeAndActive(type);
            } else if ("upcoming".equals(filter)) {
                events = communityEventRepository.findUpcomingEvents(LocalDateTime.now());
            } else if ("past".equals(filter)) {
                events = communityEventRepository.findPastEvents(LocalDateTime.now());
            } else if ("registration".equals(filter)) {
                events = communityEventRepository.findEventsOpenForRegistration(LocalDateTime.now());
            } else {
                events = communityEventRepository.findByIsActiveTrueAndIsCancelledFalseOrderByEventDateAsc();
            }

            System.out.println("📊 Found " + events.size() + " events");
            return ResponseEntity.ok(Map.of(
                "success", true,
                "events", events
            ));

        } catch (Exception e) {
            System.out.println("❌ Error in getCommunityEvents: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load community events: " + e.getMessage()
            ));
        }
    }

    // Temporary community endpoints until CommunityController is fixed
    @GetMapping("/community-overview")
    public ResponseEntity<Map<String, Object>> getCommunityOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();

            // Simple statistics
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalGroups", 3L);
            stats.put("totalTopics", 2L);
            stats.put("upcomingEvents", 2L);

            overview.put("stats", stats);
            overview.put("popularGroups", new ArrayList<>());
            overview.put("popularTopics", new ArrayList<>());
            overview.put("upcomingEvents", new ArrayList<>());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "overview", overview
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load community overview: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/community-support-groups")
    public ResponseEntity<Map<String, Object>> getSupportGroups() {
        try {
            return ResponseEntity.ok(Map.of(
                "success", true,
                "groups", new ArrayList<>()
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load support groups: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/community-forum-topics")
    public ResponseEntity<Map<String, Object>> getForumTopics() {
        try {
            return ResponseEntity.ok(Map.of(
                "success", true,
                "topics", new ArrayList<>()
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load forum topics: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/community-events")
    public ResponseEntity<Map<String, Object>> getCommunityEvents() {
        try {
            return ResponseEntity.ok(Map.of(
                "success", true,
                "events", new ArrayList<>()
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load community events: " + e.getMessage()
            ));
        }
    }
}
