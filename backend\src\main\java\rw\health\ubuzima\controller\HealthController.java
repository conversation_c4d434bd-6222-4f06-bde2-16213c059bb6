package rw.health.ubuzima.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/health")
public class HealthController {

    @GetMapping
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("timestamp", LocalDateTime.now());
        response.put("service", "Ubuzima Backend API");
        response.put("version", "1.0.0");
        return ResponseEntity.ok(response);
    }

    @GetMapping("/community-test")
    public ResponseEntity<Map<String, Object>> communityTest() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Community endpoints are working!");
        response.put("timestamp", LocalDateTime.now());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/simple-test")
    public String simpleTest() {
        return "Simple test works!";
    }

    // Temporary community endpoints until CommunityController is fixed
    @GetMapping("/community-overview")
    public ResponseEntity<Map<String, Object>> getCommunityOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();

            // Simple statistics
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalGroups", 3L);
            stats.put("totalTopics", 2L);
            stats.put("upcomingEvents", 2L);

            overview.put("stats", stats);
            overview.put("popularGroups", new ArrayList<>());
            overview.put("popularTopics", new ArrayList<>());
            overview.put("upcomingEvents", new ArrayList<>());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "overview", overview
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load community overview: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/community-support-groups")
    public ResponseEntity<Map<String, Object>> getSupportGroups() {
        try {
            return ResponseEntity.ok(Map.of(
                "success", true,
                "groups", new ArrayList<>()
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load support groups: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/community-forum-topics")
    public ResponseEntity<Map<String, Object>> getForumTopics() {
        try {
            return ResponseEntity.ok(Map.of(
                "success", true,
                "topics", new ArrayList<>()
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load forum topics: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/community-events")
    public ResponseEntity<Map<String, Object>> getCommunityEvents() {
        try {
            return ResponseEntity.ok(Map.of(
                "success", true,
                "events", new ArrayList<>()
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load community events: " + e.getMessage()
            ));
        }
    }
}
