import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/family_planning_service.dart';
import '../../core/models/contraception_method_model.dart';

class SimpleContraceptionScreen extends StatefulWidget {
  const SimpleContraceptionScreen({super.key});

  @override
  State<SimpleContraceptionScreen> createState() => _SimpleContraceptionScreenState();
}

class _SimpleContraceptionScreenState extends State<SimpleContraceptionScreen> {
  final FamilyPlanningService _familyPlanningService = FamilyPlanningService();
  List<ContraceptionMethod> _methods = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadContraceptionData();
  }

  Future<void> _loadContraceptionData() async {
    setState(() => _isLoading = true);
    
    try {
      final methods = await _familyPlanningService.getContraceptionMethods(activeOnly: true);
      setState(() {
        _methods = methods;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Habaye ikosa mu gufata amakuru: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Kurinda Inda'),
        backgroundColor: AppTheme.secondaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh_rounded),
            onPressed: _loadContraceptionData,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadContraceptionData,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildContent(isTablet),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddMethodDialog(),
        backgroundColor: AppTheme.secondaryColor,
        child: const Icon(Icons.add_rounded, color: Colors.white),
      ),
    );
  }

  Widget _buildContent(bool isTablet) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(isTablet),
          SizedBox(height: AppTheme.spacing24),
          _buildCurrentMethods(isTablet),
          SizedBox(height: AppTheme.spacing24),
          _buildMethodsInfo(isTablet),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.secondaryColor.withValues(alpha: 0.1), Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: AppTheme.secondaryColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.shield_rounded,
                color: AppTheme.secondaryColor,
                size: isTablet ? 32 : 28,
              ),
              SizedBox(width: AppTheme.spacing12),
              Expanded(
                child: Text(
                  'Gukurikirana Uburyo bwo Kurinda Inda',
                  style: AppTheme.headingMedium.copyWith(
                    fontSize: isTablet ? 20 : 18,
                    color: AppTheme.secondaryColor,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppTheme.spacing12),
          Text(
            'Hitamo uburyo bukwiye bwo kurinda inda kandi ukabukurikirana neza.',
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.black87,
              fontSize: isTablet ? 14 : 13,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms);
  }

  Widget _buildCurrentMethods(bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Uburyo Bukoresha',
          style: AppTheme.headingMedium.copyWith(
            fontSize: isTablet ? 20 : 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: AppTheme.spacing16),
        if (_methods.isEmpty)
          Container(
            padding: EdgeInsets.all(AppTheme.spacing20),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.shield_outlined,
                    size: isTablet ? 48 : 40,
                    color: Colors.grey.shade400,
                  ),
                  SizedBox(height: AppTheme.spacing12),
                  Text(
                    'Nta buryo bwo kurinda inda bukoresha',
                    style: AppTheme.bodyLarge.copyWith(
                      color: Colors.black54,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: AppTheme.spacing8),
                  Text(
                    'Kanda kuri + kugira ngo wongeremo uburyo bushya',
                    style: AppTheme.bodyMedium.copyWith(color: Colors.black54),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _methods.length,
            itemBuilder: (context, index) {
              final method = _methods[index];
              return _buildMethodCard(method, isTablet);
            },
          ),
      ],
    ).animate().fadeIn(duration: 800.ms, delay: 200.ms);
  }

  Widget _buildMethodCard(ContraceptionMethod method, bool isTablet) {
    return Container(
      margin: EdgeInsets.only(bottom: AppTheme.spacing12),
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: AppTheme.secondaryColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(AppTheme.spacing8),
                decoration: BoxDecoration(
                  color: AppTheme.secondaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                ),
                child: Icon(
                  Icons.shield_rounded,
                  color: AppTheme.secondaryColor,
                  size: isTablet ? 24 : 20,
                ),
              ),
              SizedBox(width: AppTheme.spacing12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      method.methodType,
                      style: AppTheme.bodyLarge.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: isTablet ? 16 : 14,
                      ),
                    ),
                    Text(
                      'Yatangiye: ${_formatDate(method.startDate)}',
                      style: AppTheme.bodySmall.copyWith(
                        color: Colors.black54,
                        fontSize: isTablet ? 12 : 11,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing8,
                  vertical: AppTheme.spacing4,
                ),
                decoration: BoxDecoration(
                  color: method.isActive ? AppTheme.successColor : Colors.grey,
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                ),
                child: Text(
                  method.isActive ? 'Birakora' : 'Byahagaritswe',
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white,
                    fontSize: isTablet ? 11 : 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          if (method.notes != null) ...[
            SizedBox(height: AppTheme.spacing12),
            Text(
              method.notes!,
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.black87,
                fontSize: isTablet ? 13 : 12,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMethodsInfo(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Inama z\'Ubwiyunge',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 18 : 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          _buildTipItem(
            'Hitamo uburyo bukwiye ubuzima bwawe',
            Icons.health_and_safety_rounded,
            isTablet,
          ),
          _buildTipItem(
            'Gukurikirana neza amabwiriza y\'ubuvuzi',
            Icons.medication_rounded,
            isTablet,
          ),
          _buildTipItem(
            'Gusuzuma buri gihe ku muganga',
            Icons.medical_services_rounded,
            isTablet,
          ),
        ],
      ),
    ).animate().fadeIn(duration: 1000.ms, delay: 400.ms);
  }

  Widget _buildTipItem(String tip, IconData icon, bool isTablet) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppTheme.spacing8),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppTheme.secondaryColor,
            size: isTablet ? 20 : 18,
          ),
          SizedBox(width: AppTheme.spacing12),
          Expanded(
            child: Text(
              tip,
              style: AppTheme.bodyMedium.copyWith(
                fontSize: isTablet ? 14 : 13,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddMethodDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ongeraho Uburyo'),
        content: const Text('Aha hazongera form yo kwandika uburyo bushya bwo kurinda inda.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Siga'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Add method logic here
            },
            child: const Text('Bika'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
