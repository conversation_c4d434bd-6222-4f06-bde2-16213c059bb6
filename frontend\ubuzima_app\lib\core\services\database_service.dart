import 'dart:async';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/user_model.dart';
import '../models/health_record_model.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'ubuzima.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        phone TEXT,
        role TEXT NOT NULL,
        facility_id TEXT,
        district TEXT,
        sector TEXT,
        cell TEXT,
        village TEXT,
        created_at TEXT NOT NULL,
        last_login_at TEXT,
        is_active INTEGER DEFAULT 1,
        profile_image_url TEXT,
        sync_status INTEGER DEFAULT 0
      )
    ''');

    // Health records table
    await db.execute('''
      CREATE TABLE health_records (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        health_worker_id TEXT NOT NULL,
        record_date TEXT NOT NULL,
        type TEXT NOT NULL,
        data TEXT NOT NULL,
        attachments TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_private INTEGER DEFAULT 0,
        status TEXT DEFAULT 'active',
        sync_status INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Menstrual cycles table
    await db.execute('''
      CREATE TABLE menstrual_cycles (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT,
        cycle_length INTEGER NOT NULL,
        period_length INTEGER NOT NULL,
        flow_intensity TEXT NOT NULL,
        symptoms TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Medications table
    await db.execute('''
      CREATE TABLE medications (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        dosage TEXT NOT NULL,
        frequency TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT,
        reminder_times TEXT,
        instructions TEXT,
        prescribed_by TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Appointments table
    await db.execute('''
      CREATE TABLE appointments (
        id TEXT PRIMARY KEY,
        client_id TEXT NOT NULL,
        health_worker_id TEXT NOT NULL,
        facility_id TEXT NOT NULL,
        scheduled_date TEXT NOT NULL,
        duration_minutes INTEGER NOT NULL,
        type TEXT NOT NULL,
        status TEXT DEFAULT 'scheduled',
        reason TEXT,
        notes TEXT,
        diagnosis TEXT,
        prescriptions TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        cancel_reason TEXT,
        cancelled_at TEXT,
        sync_status INTEGER DEFAULT 0,
        FOREIGN KEY (client_id) REFERENCES users (id),
        FOREIGN KEY (health_worker_id) REFERENCES users (id)
      )
    ''');

    // Messages table
    await db.execute('''
      CREATE TABLE messages (
        id TEXT PRIMARY KEY,
        sender_id TEXT NOT NULL,
        receiver_id TEXT NOT NULL,
        content TEXT NOT NULL,
        type TEXT DEFAULT 'text',
        attachments TEXT,
        is_read INTEGER DEFAULT 0,
        sent_at TEXT NOT NULL,
        read_at TEXT,
        reply_to_id TEXT,
        is_encrypted INTEGER DEFAULT 0,
        priority TEXT DEFAULT 'normal',
        sync_status INTEGER DEFAULT 0,
        FOREIGN KEY (sender_id) REFERENCES users (id),
        FOREIGN KEY (receiver_id) REFERENCES users (id)
      )
    ''');

    // Health facilities table
    await db.execute('''
      CREATE TABLE health_facilities (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        address TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        phone TEXT NOT NULL,
        email TEXT,
        services TEXT,
        operating_hours TEXT,
        is_active INTEGER DEFAULT 1,
        rating REAL DEFAULT 0.0,
        review_count INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status INTEGER DEFAULT 0
      )
    ''');

    // Education progress table
    await db.execute('''
      CREATE TABLE education_progress (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        lesson_id TEXT NOT NULL,
        progress_percentage INTEGER DEFAULT 0,
        time_spent_seconds INTEGER DEFAULT 0,
        is_completed INTEGER DEFAULT 0,
        completed_at TEXT,
        attempts INTEGER DEFAULT 0,
        quiz_results TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Sync queue table for offline operations
    await db.execute('''
      CREATE TABLE sync_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        operation TEXT NOT NULL,
        data TEXT NOT NULL,
        created_at TEXT NOT NULL,
        retry_count INTEGER DEFAULT 0,
        last_retry_at TEXT
      )
    ''');

    // Notifications table
    await db.execute('''
      CREATE TABLE notifications (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        title TEXT NOT NULL,
        body TEXT NOT NULL,
        type TEXT NOT NULL,
        data TEXT,
        is_read INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        read_at TEXT,
        sync_status INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Create indexes for better performance
    await db.execute(
      'CREATE INDEX idx_health_records_user_id ON health_records (user_id)',
    );
    await db.execute(
      'CREATE INDEX idx_health_records_date ON health_records (record_date)',
    );
    await db.execute(
      'CREATE INDEX idx_appointments_client_id ON appointments (client_id)',
    );
    await db.execute(
      'CREATE INDEX idx_appointments_health_worker_id ON appointments (health_worker_id)',
    );
    await db.execute(
      'CREATE INDEX idx_appointments_date ON appointments (scheduled_date)',
    );
    await db.execute(
      'CREATE INDEX idx_messages_sender_id ON messages (sender_id)',
    );
    await db.execute(
      'CREATE INDEX idx_messages_receiver_id ON messages (receiver_id)',
    );
    await db.execute('CREATE INDEX idx_messages_sent_at ON messages (sent_at)');
    await db.execute(
      'CREATE INDEX idx_sync_queue_table_record ON sync_queue (table_name, record_id)',
    );
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database schema upgrades
    if (oldVersion < 2) {
      // Add new columns or tables for version 2
    }
  }

  // Generic CRUD operations
  Future<int> insert(String table, Map<String, dynamic> data) async {
    final db = await database;
    data['sync_status'] = 0; // Mark as needing sync
    return await db.insert(table, data);
  }

  Future<List<Map<String, dynamic>>> query(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    return await db.query(
      table,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  Future<int> update(
    String table,
    Map<String, dynamic> data,
    String where,
    List<dynamic> whereArgs,
  ) async {
    final db = await database;
    data['updated_at'] = DateTime.now().toIso8601String();
    data['sync_status'] = 0; // Mark as needing sync
    return await db.update(table, data, where: where, whereArgs: whereArgs);
  }

  Future<int> delete(
    String table,
    String where,
    List<dynamic> whereArgs,
  ) async {
    final db = await database;
    return await db.delete(table, where: where, whereArgs: whereArgs);
  }

  // User operations
  Future<void> saveUser(User user) async {
    final data = {
      'id': user.id,
      'name': user.name,
      'email': user.email,
      'phone': user.phone,
      'role': user.role.name,
      'facility_id': user.facilityId,
      'district': user.district,
      'sector': user.sector,
      'cell': user.cell,
      'village': user.village,
      'created_at': user.createdAt.toIso8601String(),
      'last_login_at': user.lastLoginAt?.toIso8601String(),
      'is_active': user.isActive ? 1 : 0,
      'profile_image_url': user.profileImageUrl,
    };

    await insert('users', data);
  }

  Future<User?> getUser(String id) async {
    final results = await query('users', where: 'id = ?', whereArgs: [id]);
    if (results.isEmpty) return null;

    final data = results.first;
    return User(
      id: data['id'],
      name: data['name'],
      email: data['email'],
      phone: data['phone'],
      role: UserRole.values.firstWhere((e) => e.name == data['role']),
      facilityId: data['facility_id'],
      district: data['district'],
      sector: data['sector'],
      cell: data['cell'],
      village: data['village'],
      createdAt: DateTime.parse(data['created_at']),
      lastLoginAt:
          data['last_login_at'] != null
              ? DateTime.parse(data['last_login_at'])
              : null,
      isActive: data['is_active'] == 1,
      profileImageUrl: data['profile_image_url'],
    );
  }

  // Health record operations - User-Centric Approach
  Future<void> saveHealthRecord(HealthRecord record) async {
    final data = {
      'id': record.id,
      'user_id': record.userId,
      'assigned_health_worker_id': record.assignedHealthWorkerId,
      'assigned_health_worker_name': record.assignedHealthWorkerName,
      'assigned_at': record.assignedAt?.toIso8601String(),

      // Weight metrics
      'weight': record.weight,
      'weight_unit': record.weightUnit,
      'weight_recorded_at': record.weightRecordedAt?.toIso8601String(),

      // Height metrics
      'height': record.height,
      'height_unit': record.heightUnit,
      'height_recorded_at': record.heightRecordedAt?.toIso8601String(),

      // Temperature metrics
      'temperature': record.temperature,
      'temperature_unit': record.temperatureUnit,
      'temperature_recorded_at':
          record.temperatureRecordedAt?.toIso8601String(),

      // Heart Rate metrics
      'heart_rate': record.heartRate,
      'heart_rate_unit': record.heartRateUnit,
      'heart_rate_recorded_at': record.heartRateRecordedAt?.toIso8601String(),

      // Blood Pressure metrics
      'blood_pressure_systolic': record.bloodPressureSystolic,
      'blood_pressure_diastolic': record.bloodPressureDiastolic,
      'blood_pressure_unit': record.bloodPressureUnit,
      'blood_pressure_recorded_at':
          record.bloodPressureRecordedAt?.toIso8601String(),

      // Health indicators
      'bmi': record.bmi,
      'bmi_category': record.bmiCategory,
      'health_status': record.healthStatus,

      // General fields
      'notes': record.notes,
      'last_updated': record.lastUpdated?.toIso8601String(),
      'created_at': record.createdAt.toIso8601String(),
      'updated_at': record.updatedAt.toIso8601String(),
    };

    await insert('health_records', data);
  }

  // Get user's health record (user-centric approach - returns single record or null)
  Future<HealthRecord?> getHealthRecord(String userId) async {
    final results = await query(
      'health_records',
      where: 'user_id = ?',
      whereArgs: [userId],
      limit: 1,
    );

    if (results.isEmpty) return null;

    final data = results.first;
    return HealthRecord(
      id: data['id'],
      userId: data['user_id'],
      assignedHealthWorkerId: data['assigned_health_worker_id'],
      assignedHealthWorkerName: data['assigned_health_worker_name'],
      assignedAt:
          data['assigned_at'] != null
              ? DateTime.parse(data['assigned_at'])
              : null,

      // Weight metrics
      weight:
          data['weight'] != null
              ? double.tryParse(data['weight'].toString())
              : null,
      weightUnit: data['weight_unit'],
      weightRecordedAt:
          data['weight_recorded_at'] != null
              ? DateTime.parse(data['weight_recorded_at'])
              : null,

      // Height metrics
      height:
          data['height'] != null
              ? double.tryParse(data['height'].toString())
              : null,
      heightUnit: data['height_unit'],
      heightRecordedAt:
          data['height_recorded_at'] != null
              ? DateTime.parse(data['height_recorded_at'])
              : null,

      // Temperature metrics
      temperature:
          data['temperature'] != null
              ? double.tryParse(data['temperature'].toString())
              : null,
      temperatureUnit: data['temperature_unit'],
      temperatureRecordedAt:
          data['temperature_recorded_at'] != null
              ? DateTime.parse(data['temperature_recorded_at'])
              : null,

      // Heart Rate metrics
      heartRate:
          data['heart_rate'] != null
              ? int.tryParse(data['heart_rate'].toString())
              : null,
      heartRateUnit: data['heart_rate_unit'],
      heartRateRecordedAt:
          data['heart_rate_recorded_at'] != null
              ? DateTime.parse(data['heart_rate_recorded_at'])
              : null,

      // Blood Pressure metrics
      bloodPressureSystolic:
          data['blood_pressure_systolic'] != null
              ? int.tryParse(data['blood_pressure_systolic'].toString())
              : null,
      bloodPressureDiastolic:
          data['blood_pressure_diastolic'] != null
              ? int.tryParse(data['blood_pressure_diastolic'].toString())
              : null,
      bloodPressureUnit: data['blood_pressure_unit'],
      bloodPressureRecordedAt:
          data['blood_pressure_recorded_at'] != null
              ? DateTime.parse(data['blood_pressure_recorded_at'])
              : null,

      // Health indicators
      bmi: data['bmi'] != null ? double.tryParse(data['bmi'].toString()) : null,
      bmiCategory: data['bmi_category'],
      healthStatus: data['health_status'] ?? 'normal',

      // General fields
      notes: data['notes'],
      lastUpdated:
          data['last_updated'] != null
              ? DateTime.parse(data['last_updated'])
              : null,
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
    );
  }

  // For backward compatibility - returns list with single record or empty list
  Future<List<HealthRecord>> getHealthRecords(String userId) async {
    final record = await getHealthRecord(userId);
    return record != null ? [record] : [];
  }

  // Sync operations
  Future<void> addToSyncQueue(
    String tableName,
    String recordId,
    String operation,
    Map<String, dynamic> data,
  ) async {
    final syncData = {
      'table_name': tableName,
      'record_id': recordId,
      'operation': operation,
      'data': jsonEncode(data),
      'created_at': DateTime.now().toIso8601String(),
      'retry_count': 0,
    };

    await insert('sync_queue', syncData);
  }

  Future<List<Map<String, dynamic>>> getPendingSyncItems() async {
    return await query('sync_queue', orderBy: 'created_at ASC');
  }

  Future<void> removeSyncItem(int id) async {
    await delete('sync_queue', 'id = ?', [id]);
  }

  Future<void> incrementSyncRetry(int id) async {
    final db = await database;
    await db.rawUpdate(
      'UPDATE sync_queue SET retry_count = retry_count + 1, last_retry_at = ? WHERE id = ?',
      [DateTime.now().toIso8601String(), id],
    );
  }

  // Clear all data (for logout)
  Future<void> clearAllData() async {
    final db = await database;
    final tables = [
      'users',
      'health_records',
      'menstrual_cycles',
      'medications',
      'appointments',
      'messages',
      'health_facilities',
      'education_progress',
      'sync_queue',
      'notifications',
    ];

    for (final table in tables) {
      await db.delete(table);
    }
  }

  // Health Status Evaluation Methods
  Map<String, dynamic> evaluateHeartRateStatus(int? heartRate) {
    if (heartRate == null) {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': 'grey',
        'colorCode': 0xFF9E9E9E,
      };
    }

    if (heartRate >= 60 && heartRate <= 100) {
      return {
        'status': 'normal',
        'message': 'Normal',
        'color': 'green',
        'colorCode': 0xFF4CAF50,
      };
    } else if ((heartRate >= 50 && heartRate < 60) ||
        (heartRate > 100 && heartRate <= 120)) {
      return {
        'status': 'caution',
        'message': 'Iyiteho',
        'color': 'orange',
        'colorCode': 0xFFFF9800,
      };
    } else {
      return {
        'status': 'critical',
        'message': 'Urarwaye',
        'color': 'red',
        'colorCode': 0xFFF44336,
      };
    }
  }

  Map<String, dynamic> evaluateBloodPressureStatus(
    int? systolic,
    int? diastolic,
  ) {
    if (systolic == null || diastolic == null) {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': 'grey',
        'colorCode': 0xFF9E9E9E,
      };
    }

    // Normal: Systolic < 120 AND Diastolic < 80
    if (systolic < 120 && diastolic < 80) {
      return {
        'status': 'normal',
        'message': 'Normal',
        'color': 'green',
        'colorCode': 0xFF4CAF50,
      };
    }
    // Elevated/Pre-hypertension: Systolic 120-139 OR Diastolic 80-89
    else if ((systolic >= 120 && systolic <= 139) ||
        (diastolic >= 80 && diastolic <= 89)) {
      return {
        'status': 'caution',
        'message': 'Iyiteho',
        'color': 'orange',
        'colorCode': 0xFFFF9800,
      };
    }
    // High/Critical: Systolic >= 140 OR Diastolic >= 90
    else {
      return {
        'status': 'critical',
        'message': 'Urarwaye',
        'color': 'red',
        'colorCode': 0xFFF44336,
      };
    }
  }

  Map<String, dynamic> evaluateTemperatureStatus(double? temperature) {
    if (temperature == null) {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': 'grey',
        'colorCode': 0xFF9E9E9E,
      };
    }

    // Normal: 36.1°C - 37.2°C
    if (temperature >= 36.1 && temperature <= 37.2) {
      return {
        'status': 'normal',
        'message': 'Normal',
        'color': 'green',
        'colorCode': 0xFF4CAF50,
      };
    }
    // Caution: Low fever 37.3°C - 38.0°C or slightly low 35.5°C - 36.0°C
    else if ((temperature >= 37.3 && temperature <= 38.0) ||
        (temperature >= 35.5 && temperature < 36.1)) {
      return {
        'status': 'caution',
        'message': 'Iyiteho',
        'color': 'orange',
        'colorCode': 0xFFFF9800,
      };
    }
    // Critical: High fever > 38.0°C or hypothermia < 35.5°C
    else {
      return {
        'status': 'critical',
        'message': 'Urarwaye',
        'color': 'red',
        'colorCode': 0xFFF44336,
      };
    }
  }

  Map<String, dynamic> evaluateWeightStatus(double? weight, double? height) {
    if (weight == null || height == null) {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': 'grey',
        'colorCode': 0xFF9E9E9E,
      };
    }

    // Calculate BMI
    double heightInMeters = height / 100;
    double bmi = weight / (heightInMeters * heightInMeters);

    // Normal BMI: 18.5 - 24.9
    if (bmi >= 18.5 && bmi <= 24.9) {
      return {
        'status': 'normal',
        'message': 'Normal',
        'color': 'green',
        'colorCode': 0xFF4CAF50,
      };
    }
    // Caution: Underweight (< 18.5) or Overweight (25.0 - 29.9)
    else if (bmi < 18.5 || (bmi >= 25.0 && bmi <= 29.9)) {
      return {
        'status': 'caution',
        'message': 'Iyiteho',
        'color': 'orange',
        'colorCode': 0xFFFF9800,
      };
    }
    // Critical: Obese (>= 30.0)
    else {
      return {
        'status': 'critical',
        'message': 'Urarwaye',
        'color': 'red',
        'colorCode': 0xFFF44336,
      };
    }
  }

  // Overall health status based on all metrics
  Map<String, dynamic> evaluateOverallHealthStatus(HealthRecord? record) {
    if (record == null) {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': 'grey',
        'colorCode': 0xFF9E9E9E,
      };
    }

    List<String> statuses = [];

    // Check heart rate
    if (record.heartRateValue > 0) {
      statuses.add(evaluateHeartRateStatus(record.heartRateValue)['status']);
    }

    // Check blood pressure - parse from string format "120/80"
    if (record.bpValue.isNotEmpty && record.bpValue != '0/0') {
      List<String> bpParts = record.bpValue.split('/');
      if (bpParts.length == 2) {
        int? systolic = int.tryParse(bpParts[0]);
        int? diastolic = int.tryParse(bpParts[1]);
        if (systolic != null && diastolic != null) {
          statuses.add(
            evaluateBloodPressureStatus(systolic, diastolic)['status'],
          );
        }
      }
    }

    // Check temperature
    if (record.tempValue > 0) {
      statuses.add(evaluateTemperatureStatus(record.tempValue)['status']);
    }

    // Check weight/BMI
    if (record.kgValue > 0 && record.heightValue > 0) {
      statuses.add(
        evaluateWeightStatus(record.kgValue, record.heightValue)['status'],
      );
    }

    // Determine overall status
    if (statuses.contains('critical')) {
      return {
        'status': 'critical',
        'message': 'Urarwaye',
        'color': 'red',
        'colorCode': 0xFFF44336,
      };
    } else if (statuses.contains('caution')) {
      return {
        'status': 'caution',
        'message': 'Iyiteho',
        'color': 'orange',
        'colorCode': 0xFFFF9800,
      };
    } else if (statuses.contains('normal')) {
      return {
        'status': 'normal',
        'message': 'Normal',
        'color': 'green',
        'colorCode': 0xFF4CAF50,
      };
    } else {
      return {
        'status': 'unknown',
        'message': 'Ntabwo bihari',
        'color': 'grey',
        'colorCode': 0xFF9E9E9E,
      };
    }
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }
}
