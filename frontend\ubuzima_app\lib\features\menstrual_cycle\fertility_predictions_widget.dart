import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';

class FertilityPredictionsWidget extends StatelessWidget {
  final Map<String, dynamic> predictions;
  final bool isTablet;

  const FertilityPredictionsWidget({
    super.key,
    required this.predictions,
    required this.isTablet,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ibiteganijwe ku Buremere',
            style: AppTheme.headingMedium.copyWith(
              fontSize: isTablet ? 20 : 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppTheme.spacing16),
          if (predictions.isEmpty)
            _buildEmptyState()
          else ...[
            _buildFertilityWindow(),
            SizedBox(height: AppTheme.spacing16),
            _buildNextPeriodPrediction(),
            SizedBox(height: AppTheme.spacing16),
            _buildOvulationPrediction(),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 600.ms);
  }

  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          Icon(
            Icons.insights_rounded,
            size: isTablet ? 48 : 40,
            color: Colors.grey.shade400,
          ),
          SizedBox(height: AppTheme.spacing12),
          Text(
            'Nta biteganijwe biboneka',
            style: AppTheme.bodyLarge.copyWith(
              color: Colors.black54,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: AppTheme.spacing8),
          Text(
            'Andika imihango myinshi kugira ngo ubone ibiteganijwe ku buremere.',
            style: AppTheme.bodyMedium.copyWith(color: Colors.black54),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFertilityWindow() {
    final fertileStart = predictions['fertileWindowStart'] as String?;
    final fertileEnd = predictions['fertileWindowEnd'] as String?;
    final fertilityChance = predictions['fertilityChance'] as double? ?? 0.0;

    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.successColor.withOpacity(0.1),
            AppTheme.successColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(color: AppTheme.successColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.favorite_rounded,
                color: AppTheme.successColor,
                size: isTablet ? 24 : 20,
              ),
              SizedBox(width: AppTheme.spacing8),
              Text(
                'Igihe cy\'Uburemere',
                style: AppTheme.bodyLarge.copyWith(
                  color: AppTheme.successColor,
                  fontWeight: FontWeight.bold,
                  fontSize: isTablet ? 16 : 14,
                ),
              ),
            ],
          ),
          SizedBox(height: AppTheme.spacing12),
          if (fertileStart != null && fertileEnd != null)
            Text(
              'Kuva $fertileStart kugeza $fertileEnd',
              style: AppTheme.bodyMedium.copyWith(
                fontSize: isTablet ? 14 : 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          SizedBox(height: AppTheme.spacing8),
          Row(
            children: [
              Text(
                'Amahirwe yo gusanga: ',
                style: AppTheme.bodySmall.copyWith(
                  fontSize: isTablet ? 12 : 11,
                ),
              ),
              Text(
                '${(fertilityChance * 100).round()}%',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.successColor,
                  fontWeight: FontWeight.bold,
                  fontSize: isTablet ? 12 : 11,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNextPeriodPrediction() {
    final nextPeriod = predictions['nextPeriodDate'] as String?;
    final daysUntilNext = predictions['daysUntilNextPeriod'] as int? ?? 0;

    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.accentColor.withOpacity(0.1),
            AppTheme.accentColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(color: AppTheme.accentColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calendar_month_rounded,
                color: AppTheme.accentColor,
                size: isTablet ? 24 : 20,
              ),
              SizedBox(width: AppTheme.spacing8),
              Text(
                'Imihango Ikurikira',
                style: AppTheme.bodyLarge.copyWith(
                  color: AppTheme.accentColor,
                  fontWeight: FontWeight.bold,
                  fontSize: isTablet ? 16 : 14,
                ),
              ),
            ],
          ),
          SizedBox(height: AppTheme.spacing12),
          if (nextPeriod != null)
            Text(
              'Itariki: $nextPeriod',
              style: AppTheme.bodyMedium.copyWith(
                fontSize: isTablet ? 14 : 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          SizedBox(height: AppTheme.spacing8),
          Text(
            'Iminsi isigaye: $daysUntilNext',
            style: AppTheme.bodySmall.copyWith(
              fontSize: isTablet ? 12 : 11,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOvulationPrediction() {
    final ovulationDate = predictions['ovulationDate'] as String?;
    final daysUntilOvulation = predictions['daysUntilOvulation'] as int? ?? 0;

    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.warningColor.withOpacity(0.1),
            AppTheme.warningColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(color: AppTheme.warningColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.egg_rounded,
                color: AppTheme.warningColor,
                size: isTablet ? 24 : 20,
              ),
              SizedBox(width: AppTheme.spacing8),
              Text(
                'Gusohoka kw\'Amagi',
                style: AppTheme.bodyLarge.copyWith(
                  color: AppTheme.warningColor,
                  fontWeight: FontWeight.bold,
                  fontSize: isTablet ? 16 : 14,
                ),
              ),
            ],
          ),
          SizedBox(height: AppTheme.spacing12),
          if (ovulationDate != null)
            Text(
              'Itariki: $ovulationDate',
              style: AppTheme.bodyMedium.copyWith(
                fontSize: isTablet ? 14 : 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          SizedBox(height: AppTheme.spacing8),
          Text(
            daysUntilOvulation > 0
                ? 'Iminsi isigaye: $daysUntilOvulation'
                : 'Gusohoka kw\'amagi kwabaye',
            style: AppTheme.bodySmall.copyWith(
              fontSize: isTablet ? 12 : 11,
            ),
          ),
        ],
      ),
    );
  }
}
