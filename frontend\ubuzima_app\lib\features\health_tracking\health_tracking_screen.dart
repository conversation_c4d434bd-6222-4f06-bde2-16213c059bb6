import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../core/models/health_record_model.dart';
import '../../core/services/health_tracking_service.dart';
import '../../core/theme/app_theme.dart';
import '../../widgets/voice_button.dart';

class HealthTrackingScreen extends StatefulWidget {
  const HealthTrackingScreen({super.key});

  @override
  State<HealthTrackingScreen> createState() => _HealthTrackingScreenState();
}

class _HealthTrackingScreenState extends State<HealthTrackingScreen> {
  final HealthTrackingService _healthService = HealthTrackingService();
  List<HealthRecord> _healthRecords = [];
  bool _isLoading = true;
  String _selectedMetric = 'ALL';

  @override
  void initState() {
    super.initState();
    _loadHealthRecords();
  }

  Future<void> _loadHealthRecords() async {
    setState(() => _isLoading = true);
    try {
      final healthRecord = await _healthService.getUserHealthRecord();
      print('Loaded health record: ${healthRecord?.id}'); // Debug
      if (healthRecord != null) {
        print(
          'Available metrics: ${healthRecord.availableMetrics.join(', ')}',
        ); // Debug
        print('Health status: ${healthRecord.healthStatus}'); // Debug
        if (healthRecord.heartRateValue != null) {
          print(
            'Heart Rate: ${healthRecord.heartRateValue} ${healthRecord.heartRateUnit}',
          );
        }
        if (healthRecord.bpValue != null) {
          print(
            'Blood Pressure: ${healthRecord.bpValue} ${healthRecord.bpUnit}',
          );
        }
        if (healthRecord.kgValue != null) {
          print('Weight: ${healthRecord.kgValue} ${healthRecord.kgUnit}');
        }
        if (healthRecord.tempValue != null) {
          print(
            'Temperature: ${healthRecord.tempValue} ${healthRecord.tempUnit}',
          );
        }
      }
      setState(() {
        _healthRecords = healthRecord != null ? [healthRecord] : [];
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading health records: $e'); // Debug
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Habaye ikosa mu gufata amakuru: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  List<HealthRecord> get _filteredRecords {
    if (_selectedMetric == 'ALL') return _healthRecords;

    // With user-centric approach, we filter based on available metrics
    return _healthRecords.where((record) {
      switch (_selectedMetric) {
        case 'WEIGHT':
          return record.kgValue != null;
        case 'BLOOD_PRESSURE':
          return record.bpValue != null;
        case 'TEMPERATURE':
          return record.tempValue != null;
        case 'HEART_RATE':
          return record.heartRateValue != null;
        default:
          return true;
      }
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Ubuzima bwawe'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                onRefresh: _loadHealthRecords,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildAddRecordButton(),
                      const SizedBox(height: 20),
                      _buildMetricCards(),
                      const SizedBox(height: 20),
                      _buildChart(),
                      const SizedBox(height: 20),
                      _buildRecentRecords(),
                    ],
                  ),
                ),
              ),
      floatingActionButton: VoiceButton(
        heroTag: 'health_tracking_voice',
        prompt:
            'Vuga: "Kongeraho" kugira ngo wongeraho amakuru, "Reba" kugira ngo urebe amateka',
        onResult: _handleVoiceCommand,
        tooltip: 'Koresha ijwi gucunga ubuzima',
      ),
    );
  }

  Widget _buildAddRecordButton() {
    return GestureDetector(
      onTap: _showAddRecordDialog,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            const Icon(Icons.add_circle_outline, color: Colors.white, size: 24),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Ongeraho amakuru y\'ubuzima',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const Icon(Icons.mic, color: Colors.white, size: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCards() {
    final healthRecord =
        _healthRecords.isNotEmpty ? _healthRecords.first : null;

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'Umuvuduko w\'umutima',
                healthRecord?.heartRateValue?.toString() ?? '--',
                healthRecord?.heartRateUnit ?? 'bpm',
                Icons.favorite,
                AppTheme.errorColor,
                _getHealthStatus(
                  healthRecord?.heartRateValue?.toString(),
                  'HEART_RATE',
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMetricCard(
                'Ibiro',
                healthRecord?.kgValue?.toString() ?? '--',
                healthRecord?.kgUnit ?? 'kg',
                Icons.monitor_weight,
                AppTheme.warningColor,
                _getHealthStatus(healthRecord?.kgValue?.toString(), 'WEIGHT'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'Umuvuduko w\'amaraso',
                healthRecord?.bpValue ?? '--',
                healthRecord?.bpUnit ?? 'mmHg',
                Icons.water_drop,
                AppTheme.primaryColor,
                _getHealthStatus(healthRecord?.bpValue, 'BLOOD_PRESSURE'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMetricCard(
                'Ubushyuhe bw\'umubiri',
                healthRecord?.tempValue?.toString() ?? '--',
                healthRecord?.tempUnit ?? '°C',
                Icons.thermostat,
                AppTheme.warningColor,
                _getHealthStatus(
                  healthRecord?.tempValue?.toString(),
                  'TEMPERATURE',
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    String unit,
    IconData icon,
    Color color,
    String status,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textLight),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '$value $unit',
            style: AppTheme.headingMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: AppTheme.successColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              status,
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.successColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChart() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.trending_up, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              Text('Imihindagurikire y\'ubuzima', style: AppTheme.headingSmall),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(height: 200, child: _buildLineChart()),
        ],
      ),
    );
  }

  Widget _buildLineChart() {
    final chartData = _getChartData();

    if (chartData.isEmpty) {
      return Center(
        child: Text('Nta makuru ahari kugaragaza', style: AppTheme.bodyMedium),
      );
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: 1,
          verticalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(color: Colors.grey.withOpacity(0.3), strokeWidth: 1);
          },
          getDrawingVerticalLine: (value) {
            return FlLine(color: Colors.grey.withOpacity(0.3), strokeWidth: 1);
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (double value, TitleMeta meta) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  child: Text(
                    value.toInt().toString(),
                    style: const TextStyle(
                      color: Colors.grey,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 1,
              getTitlesWidget: (double value, TitleMeta meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                );
              },
              reservedSize: 42,
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: const Color(0xff37434d)),
        ),
        minX: 0,
        maxX: chartData.isNotEmpty ? (chartData.length - 1).toDouble() : 6,
        minY:
            chartData.isNotEmpty
                ? chartData.map((e) => e.y).reduce((a, b) => a < b ? a : b) - 1
                : 64,
        maxY:
            chartData.isNotEmpty
                ? chartData.map((e) => e.y).reduce((a, b) => a > b ? a : b) + 1
                : 67,
        lineBarsData: [
          LineChartBarData(
            spots: chartData,
            isCurved: true,
            gradient: LinearGradient(
              colors: [
                AppTheme.primaryColor,
                AppTheme.primaryColor.withOpacity(0.3),
              ],
            ),
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: AppTheme.primaryColor,
                  strokeWidth: 2,
                  strokeColor: Colors.white,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryColor.withOpacity(0.3),
                  AppTheme.primaryColor.withOpacity(0.1),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentRecords() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Amateka y\'ubuzima', style: AppTheme.headingSmall),
          const SizedBox(height: 16),
          if (_healthRecords.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Text(
                  'Nta makuru y\'ubuzima ahari',
                  style: AppTheme.bodyMedium,
                ),
              ),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _healthRecords.take(5).length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final record = _healthRecords[index];
                return _buildRecordTile(record);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildRecordTile(HealthRecord record) {
    // Since we now have a user-centric record, we'll display all available metrics
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.health_and_safety, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Amakuru y\'ubuzima',
                  style: AppTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Text(
                  _formatDate(record.lastUpdated ?? record.updatedAt),
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textLight),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildMetricsList(record),
            if (record.notes != null && record.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Inyandiko: ${record.notes}',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textLight,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMetricsList(HealthRecord record) {
    final metrics = <Widget>[];

    if (record.heartRateValue != null) {
      metrics.add(
        _buildMetricRow(
          'Umutima',
          '${record.heartRateValue} ${record.heartRateUnit}',
          Icons.favorite,
          AppTheme.errorColor,
        ),
      );
    }

    if (record.bpValue != null) {
      metrics.add(
        _buildMetricRow(
          'Umuvuduko w\'amaraso',
          '${record.bpValue} ${record.bpUnit}',
          Icons.water_drop,
          AppTheme.primaryColor,
        ),
      );
    }

    if (record.kgValue != null) {
      metrics.add(
        _buildMetricRow(
          'Ibiro',
          '${record.kgValue} ${record.kgUnit}',
          Icons.monitor_weight,
          AppTheme.warningColor,
        ),
      );
    }

    if (record.tempValue != null) {
      metrics.add(
        _buildMetricRow(
          'Ubushyuhe',
          '${record.tempValue} ${record.tempUnit}',
          Icons.thermostat,
          AppTheme.infoColor,
        ),
      );
    }

    if (record.bmi != null) {
      metrics.add(
        _buildMetricRow(
          'BMI',
          '${record.bmi}',
          Icons.calculate,
          AppTheme.successColor,
        ),
      );
    }

    return Column(children: metrics);
  }

  Widget _buildMetricRow(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Text(label, style: AppTheme.bodySmall),
          const Spacer(),
          Text(
            value,
            style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  // Deprecated - kept for backward compatibility
  @Deprecated('Use getUserHealthRecord instead')
  Map<String, HealthRecord> _getLatestRecords() {
    // With user-centric approach, we just return the single health record
    if (_healthRecords.isNotEmpty) {
      final record = _healthRecords.first;
      return {
        'HEART_RATE': record,
        'BLOOD_PRESSURE': record,
        'WEIGHT': record,
        'TEMPERATURE': record,
      };
    }
    return {};
  }

  List<FlSpot> _getChartData() {
    // With user-centric approach, we create a simple chart showing available metrics
    if (_healthRecords.isEmpty) {
      return [];
    }

    final record = _healthRecords.first;
    List<FlSpot> spots = [];

    // Create chart data points for available metrics
    double index = 0;

    if (record.heartRateValue != null) {
      spots.add(FlSpot(index++, record.heartRateValue!.toDouble()));
    }

    if (record.tempValue != null) {
      spots.add(FlSpot(index++, record.tempValue!.toDouble()));
    }

    if (record.kgValue != null) {
      spots.add(FlSpot(index++, record.kgValue!.toDouble()));
    }

    // If we have blood pressure, use systolic value
    if (record.systolic != null) {
      spots.add(FlSpot(index++, record.systolic!.toDouble()));
    }

    return spots;
  }

  String _getHealthStatus(String? value, String recordType) {
    if (value == null || value == '--') return 'Ntabwo bihari';

    final numValue = double.tryParse(value);
    if (numValue == null) return 'Ntabwo bihari';

    switch (recordType) {
      case 'HEART_RATE':
        if (numValue >= 60 && numValue <= 100) return 'Mwiza';
        if (numValue >= 50 && numValue <= 120) return 'Bisanzwe';
        return 'Bikeneye kwitabwaho';

      case 'BLOOD_PRESSURE':
        // Assuming systolic pressure (first number)
        if (numValue >= 90 && numValue <= 120) return 'Mwiza';
        if (numValue >= 80 && numValue <= 140) return 'Bisanzwe';
        return 'Bikeneye kwitabwaho';

      case 'TEMPERATURE':
        if (numValue >= 36.1 && numValue <= 37.2) return 'Bisanzwe';
        if (numValue >= 35.5 && numValue <= 38.0) return 'Mwiza';
        return 'Bikeneye kwitabwaho';

      case 'WEIGHT':
        // Basic weight assessment (would need height for BMI)
        if (numValue >= 50 && numValue <= 90) return 'Bisanzwe';
        return 'Mwiza';

      default:
        return 'Bisanzwe';
    }
  }

  // Removed unused methods - no longer needed with user-centric approach

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Uyu munsi';
    } else if (difference.inDays == 1) {
      return 'Ejo';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} amunsi ashize';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _handleVoiceCommand(String command) {
    final lowerCommand = command.toLowerCase();

    if (lowerCommand.contains('kongeraho') || lowerCommand.contains('add')) {
      _showAddRecordDialog();
    } else if (lowerCommand.contains('reba') || lowerCommand.contains('view')) {
      _showRecordsHistory();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Ntabwo nkumva icyo uvuze. Gerageza "Kongeraho" cyangwa "Reba"',
          ),
        ),
      );
    }
  }

  void _showAddRecordDialog() {
    showDialog(
      context: context,
      builder:
          (context) => _AddHealthRecordDialog(
            onRecordAdded: () {
              _loadHealthRecords();
              Navigator.of(context).pop();
            },
          ),
    );
  }

  void _showRecordsHistory() {
    // Navigate to full records history
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Gufungura amateka yose y\'ubuzima...')),
    );
  }
}

class _AddHealthRecordDialog extends StatefulWidget {
  final VoidCallback onRecordAdded;

  const _AddHealthRecordDialog({required this.onRecordAdded});

  @override
  State<_AddHealthRecordDialog> createState() => _AddHealthRecordDialogState();
}

class _AddHealthRecordDialogState extends State<_AddHealthRecordDialog> {
  final _formKey = GlobalKey<FormState>();
  final _valueController = TextEditingController();
  final _notesController = TextEditingController();
  final HealthTrackingService _healthService = HealthTrackingService();

  String _selectedType = 'TEMPERATURE';
  bool _isLoading = false;

  final Map<String, Map<String, String>> _recordTypes = {
    'TEMPERATURE': {'title': 'Ubushyuhe bw\'umubiri', 'unit': '°C'},
    'WEIGHT': {'title': 'Ibiro', 'unit': 'kg'},
    'HEIGHT': {'title': 'Uburebure', 'unit': 'cm'},
    'BLOOD_PRESSURE': {'title': 'Umuvuduko w\'amaraso', 'unit': 'mmHg'},
    'HEART_RATE': {'title': 'Umuvuduko w\'umutima', 'unit': 'bpm'},
  };

  @override
  void dispose() {
    _valueController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _saveRecord() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      await _healthService.addHealthRecord(_selectedType, {
        'value': _valueController.text,
        'unit': _recordTypes[_selectedType]!['unit']!,
        'notes': _notesController.text,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Amakuru y\'ubuzima yongerewe neza!'),
            backgroundColor: AppTheme.successColor,
          ),
        );
        widget.onRecordAdded();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Habaye ikosa: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Ongeraho amakuru y\'ubuzima'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Ubwoko bw\'amakuru',
                border: OutlineInputBorder(),
              ),
              items:
                  _recordTypes.entries.map((entry) {
                    return DropdownMenuItem(
                      value: entry.key,
                      child: Text(entry.value['title']!),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() => _selectedType = value!);
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _valueController,
              decoration: InputDecoration(
                labelText: 'Agaciro (${_recordTypes[_selectedType]!['unit']})',
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Injiza agaciro';
                }
                if (double.tryParse(value) == null) {
                  return 'Injiza umubare wemewe';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Inyandiko (ntibishoboka)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Kuraguza'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveRecord,
          child:
              _isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Text('Bika'),
        ),
      ],
    );
  }
}
