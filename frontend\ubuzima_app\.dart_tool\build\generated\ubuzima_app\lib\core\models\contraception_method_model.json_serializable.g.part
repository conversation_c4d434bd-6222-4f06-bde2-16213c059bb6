// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ContraceptionMethod _$ContraceptionMethodFromJson(Map<String, dynamic> json) =>
    ContraceptionMethod(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: $enumDecode(_$ContraceptionTypeEnumMap, json['type']),
      name: json['name'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate:
          json['endDate'] == null
              ? null
              : DateTime.parse(json['endDate'] as String),
      isActive: json['isActive'] as bool,
      effectiveness: (json['effectiveness'] as num).toDouble(),
      sideEffects:
          (json['sideEffects'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      instructions: json['instructions'] as String,
      nextAppointment:
          json['nextAppointment'] == null
              ? null
              : DateTime.parse(json['nextAppointment'] as String),
      notes: json['notes'] as String?,
      prescribedBy: json['prescribedBy'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt:
          json['updatedAt'] == null
              ? null
              : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$ContraceptionMethodToJson(
  ContraceptionMethod instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'type': _$ContraceptionTypeEnumMap[instance.type]!,
  'name': instance.name,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate?.toIso8601String(),
  'isActive': instance.isActive,
  'effectiveness': instance.effectiveness,
  'sideEffects': instance.sideEffects,
  'instructions': instance.instructions,
  'nextAppointment': instance.nextAppointment?.toIso8601String(),
  'notes': instance.notes,
  'prescribedBy': instance.prescribedBy,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

const _$ContraceptionTypeEnumMap = {
  ContraceptionType.pill: 'pill',
  ContraceptionType.iud: 'iud',
  ContraceptionType.implant: 'implant',
  ContraceptionType.injection: 'injection',
  ContraceptionType.patch: 'patch',
  ContraceptionType.ring: 'ring',
  ContraceptionType.condom: 'condom',
  ContraceptionType.diaphragm: 'diaphragm',
  ContraceptionType.spermicide: 'spermicide',
  ContraceptionType.naturalFamilyPlanning: 'naturalFamilyPlanning',
  ContraceptionType.sterilization: 'sterilization',
  ContraceptionType.emergency: 'emergency',
};
