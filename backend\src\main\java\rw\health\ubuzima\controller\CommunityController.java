package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.entity.*;
import rw.health.ubuzima.repository.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/community")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class CommunityController {

    // Temporarily commented out to test controller loading
    // private final SupportGroupRepository supportGroupRepository;
    // private final SupportGroupMemberRepository supportGroupMemberRepository;
    // private final ForumTopicRepository forumTopicRepository;
    // private final CommunityEventRepository communityEventRepository;
    private final UserRepository userRepository;

    // ============ TEST ENDPOINT ============

    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> testCommunity() {
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Community controller is working!",
            "timestamp", LocalDateTime.now()
        ));
    }

    // ============ COMMUNITY OVERVIEW ============

    @GetMapping("/overview")
    public ResponseEntity<Map<String, Object>> getCommunityOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();
            
            // Community statistics
            Long totalGroups = supportGroupRepository.countActiveGroups();
            Long totalTopics = forumTopicRepository.countActiveTopics();
            Long totalEvents = communityEventRepository.countActiveEvents();
            Long upcomingEvents = communityEventRepository.countUpcomingEvents(LocalDateTime.now());
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalGroups", totalGroups);
            stats.put("totalTopics", totalTopics);
            stats.put("totalEvents", totalEvents);
            stats.put("upcomingEvents", upcomingEvents);
            
            overview.put("stats", stats);
            
            // Recent highlights
            List<SupportGroup> popularGroups = supportGroupRepository.findPublicGroupsByPopularity()
                    .stream().limit(3).toList();
            List<ForumTopic> popularTopics = forumTopicRepository.findPopularTopics()
                    .stream().limit(3).toList();
            List<CommunityEvent> upcomingEventsList = communityEventRepository.findUpcomingEvents(LocalDateTime.now())
                    .stream().limit(3).toList();
            
            overview.put("popularGroups", popularGroups);
            overview.put("popularTopics", popularTopics);
            overview.put("upcomingEvents", upcomingEventsList);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "overview", overview
            ));
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load community overview: " + e.getMessage()
            ));
        }
    }

    // ============ SUPPORT GROUPS ============
    
    @GetMapping("/support-groups")
    public ResponseEntity<Map<String, Object>> getSupportGroups(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String search) {
        try {
            List<SupportGroup> groups;
            
            if (search != null && !search.isEmpty()) {
                groups = supportGroupRepository.searchActiveGroups(search);
            } else if (category != null && !category.equals("all")) {
                groups = supportGroupRepository.findByCategoryAndIsActiveTrueOrderByMemberCountDesc(category);
            } else {
                groups = supportGroupRepository.findByIsActiveTrueOrderByCreatedAtDesc();
            }
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "groups", groups
            ));
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load support groups: " + e.getMessage()
            ));
        }
    }
    
    @PostMapping("/support-groups")
    public ResponseEntity<Map<String, Object>> createSupportGroup(@RequestBody Map<String, Object> request) {
        try {
            Long creatorId = Long.valueOf(request.get("creatorId").toString());
            User creator = userRepository.findById(creatorId).orElse(null);
            
            if (creator == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Creator not found"
                ));
            }
            
            SupportGroup group = new SupportGroup();
            group.setName(request.get("name").toString());
            group.setDescription(request.get("description").toString());
            group.setCategory(request.get("category").toString());
            group.setCreator(creator);
            group.setIsPrivate(Boolean.valueOf(request.get("isPrivate").toString()));
            
            if (request.containsKey("maxMembers")) {
                group.setMaxMembers(Integer.valueOf(request.get("maxMembers").toString()));
            }
            
            SupportGroup savedGroup = supportGroupRepository.save(group);
            
            // Add creator as admin member
            SupportGroupMember creatorMember = new SupportGroupMember();
            creatorMember.setGroup(savedGroup);
            creatorMember.setUser(creator);
            creatorMember.setRole("ADMIN");
            supportGroupMemberRepository.save(creatorMember);
            
            // Update member count
            savedGroup.setMemberCount(1);
            supportGroupRepository.save(savedGroup);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Support group created successfully",
                "group", savedGroup
            ));
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to create support group: " + e.getMessage()
            ));
        }
    }
    
    @PostMapping("/support-groups/{groupId}/join")
    public ResponseEntity<Map<String, Object>> joinSupportGroup(
            @PathVariable Long groupId,
            @RequestParam Long userId) {
        try {
            SupportGroup group = supportGroupRepository.findById(groupId).orElse(null);
            User user = userRepository.findById(userId).orElse(null);
            
            if (group == null || user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Group or user not found"
                ));
            }
            
            // Check if already a member
            if (supportGroupMemberRepository.existsByGroupAndUserAndIsActiveTrue(group, user)) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User is already a member of this group"
                ));
            }
            
            // Check if group is full
            if (group.getMaxMembers() != null && group.getMemberCount() >= group.getMaxMembers()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Group is full"
                ));
            }
            
            SupportGroupMember member = new SupportGroupMember();
            member.setGroup(group);
            member.setUser(user);
            member.setRole("MEMBER");
            supportGroupMemberRepository.save(member);
            
            // Update member count
            group.setMemberCount(group.getMemberCount() + 1);
            supportGroupRepository.save(group);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Successfully joined the group"
            ));
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to join group: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/support-groups/user/{userId}")
    public ResponseEntity<Map<String, Object>> getUserSupportGroups(@PathVariable Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User not found"
                ));
            }

            List<SupportGroupMember> memberships = supportGroupMemberRepository.findActiveGroupsByUser(user);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "memberships", memberships
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load user groups: " + e.getMessage()
            ));
        }
    }

    // ============ FORUM TOPICS ============

    @GetMapping("/forum/topics")
    public ResponseEntity<Map<String, Object>> getForumTopics(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String filter) {
        try {
            List<ForumTopic> topics;

            if (search != null && !search.isEmpty()) {
                topics = forumTopicRepository.searchTopics(search);
            } else if ("popular".equals(filter)) {
                topics = forumTopicRepository.findPopularTopics();
            } else if ("pinned".equals(filter)) {
                topics = forumTopicRepository.findPinnedTopics();
            } else if (category != null && !category.equals("all")) {
                topics = forumTopicRepository.findByCategoryAndIsActiveTrueOrderByLastActivityAtDesc(category);
            } else {
                topics = forumTopicRepository.findByIsActiveTrueOrderByLastActivityAtDesc();
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "topics", topics
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load forum topics: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/forum/topics")
    public ResponseEntity<Map<String, Object>> createForumTopic(@RequestBody Map<String, Object> request) {
        try {
            Long authorId = Long.valueOf(request.get("authorId").toString());
            User author = userRepository.findById(authorId).orElse(null);

            if (author == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Author not found"
                ));
            }

            ForumTopic topic = new ForumTopic();
            topic.setTitle(request.get("title").toString());
            topic.setContent(request.get("content").toString());
            topic.setCategory(request.get("category").toString());
            topic.setAuthor(author);
            topic.setLastActivityAt(LocalDateTime.now());

            ForumTopic savedTopic = forumTopicRepository.save(topic);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Forum topic created successfully",
                "topic", savedTopic
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to create forum topic: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/forum/topics/user/{userId}")
    public ResponseEntity<Map<String, Object>> getUserForumTopics(@PathVariable Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User not found"
                ));
            }

            List<ForumTopic> topics = forumTopicRepository.findByAuthorAndIsActiveTrueOrderByCreatedAtDesc(user);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "topics", topics
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load user topics: " + e.getMessage()
            ));
        }
    }

    // ============ COMMUNITY EVENTS ============

    @GetMapping("/events")
    public ResponseEntity<Map<String, Object>> getCommunityEvents(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String filter,
            @RequestParam(required = false) String search) {
        try {
            List<CommunityEvent> events;
            LocalDateTime now = LocalDateTime.now();

            if (search != null && !search.isEmpty()) {
                events = communityEventRepository.searchEvents(search);
            } else if ("upcoming".equals(filter)) {
                events = communityEventRepository.findUpcomingEvents(now);
            } else if ("past".equals(filter)) {
                events = communityEventRepository.findPastEvents(now);
            } else if ("registration-open".equals(filter)) {
                events = communityEventRepository.findEventsOpenForRegistration(now);
            } else if (type != null) {
                events = communityEventRepository.findByTypeAndActive(type);
            } else {
                events = communityEventRepository.findByIsActiveTrueAndIsCancelledFalseOrderByEventDateAsc();
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "events", events
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load community events: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/events")
    public ResponseEntity<Map<String, Object>> createCommunityEvent(@RequestBody Map<String, Object> request) {
        try {
            Long organizerId = Long.valueOf(request.get("organizerId").toString());
            User organizer = userRepository.findById(organizerId).orElse(null);

            if (organizer == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Organizer not found"
                ));
            }

            CommunityEvent event = new CommunityEvent();
            event.setTitle(request.get("title").toString());
            event.setDescription(request.get("description").toString());
            event.setType(request.get("type").toString());
            event.setOrganizer(organizer);
            event.setEventDate(LocalDateTime.parse(request.get("eventDate").toString()));
            event.setLocation(request.get("location").toString());

            if (request.containsKey("maxParticipants")) {
                event.setMaxParticipants(Integer.valueOf(request.get("maxParticipants").toString()));
            }

            CommunityEvent savedEvent = communityEventRepository.save(event);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Community event created successfully",
                "event", savedEvent
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to create community event: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/events/user/{userId}")
    public ResponseEntity<Map<String, Object>> getUserCommunityEvents(@PathVariable Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User not found"
                ));
            }

            List<CommunityEvent> events = communityEventRepository.findByOrganizerAndIsActiveTrueOrderByEventDateDesc(user);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "events", events
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load user events: " + e.getMessage()
            ));
        }
    }
}
