package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.entity.SupportGroup;
import rw.health.ubuzima.entity.ForumTopic;
import rw.health.ubuzima.entity.CommunityEvent;
import rw.health.ubuzima.repository.SupportGroupRepository;
import rw.health.ubuzima.repository.ForumTopicRepository;
import rw.health.ubuzima.repository.CommunityEventRepository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;

@RestController
@RequestMapping("/community")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class CommunityController {

    private final SupportGroupRepository supportGroupRepository;
    private final ForumTopicRepository forumTopicRepository;
    private final CommunityEventRepository communityEventRepository;

    // ============ TEST ENDPOINT ============
    
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> testCommunity() {
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Community controller is working!",
            "timestamp", LocalDateTime.now()
        ));
    }
    
    // ============ COMMUNITY OVERVIEW ============
    
    @GetMapping("/overview")
    public ResponseEntity<Map<String, Object>> getCommunityOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();

            // Real community statistics from database
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalGroups", supportGroupRepository.countActiveGroups());
            stats.put("totalTopics", forumTopicRepository.countActiveTopics());
            stats.put("totalEvents", communityEventRepository.countActiveEvents());
            stats.put("upcomingEvents", communityEventRepository.countUpcomingEvents(LocalDateTime.now()));

            overview.put("stats", stats);

            // Get real data for highlights
            List<SupportGroup> popularGroups = supportGroupRepository.findPopularGroups().stream().limit(3).toList();
            List<ForumTopic> popularTopics = forumTopicRepository.findPopularTopics().stream().limit(3).toList();
            List<CommunityEvent> upcomingEvents = communityEventRepository.findUpcomingEvents(LocalDateTime.now()).stream().limit(3).toList();

            overview.put("popularGroups", popularGroups);
            overview.put("popularTopics", popularTopics);
            overview.put("upcomingEvents", upcomingEvents);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "overview", overview
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load community overview: " + e.getMessage()
            ));
        }
    }

    // ============ SUPPORT GROUPS ============
    
    @GetMapping("/support-groups")
    public ResponseEntity<Map<String, Object>> getSupportGroups(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String search) {
        try {
            System.out.println("🔍 Getting support groups from database...");

            List<SupportGroup> groups;

            if (search != null && !search.isEmpty()) {
                groups = supportGroupRepository.searchActiveGroups(search);
            } else if (category != null && !category.equals("all")) {
                groups = supportGroupRepository.findByCategoryAndIsActiveTrueOrderByMemberCountDesc(category);
            } else {
                groups = supportGroupRepository.findByIsActiveTrueOrderByCreatedAtDesc();
            }

            System.out.println("📊 Found " + groups.size() + " groups");
            return ResponseEntity.ok(Map.of(
                "success", true,
                "groups", groups
            ));

        } catch (Exception e) {
            System.out.println("❌ Error in getSupportGroups: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load support groups: " + e.getMessage()
            ));
        }
    }
    
    // ============ FORUM TOPICS ============
    
    @GetMapping("/forum/topics")
    public ResponseEntity<Map<String, Object>> getForumTopics(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String filter) {
        try {
            System.out.println("🔍 Getting forum topics from database...");

            List<ForumTopic> topics;

            if (search != null && !search.isEmpty()) {
                topics = forumTopicRepository.searchActiveTopics(search);
            } else if (category != null && !category.equals("all")) {
                topics = forumTopicRepository.findByCategoryAndIsActiveTrueOrderByLastActivityAtDesc(category);
            } else if ("popular".equals(filter)) {
                topics = forumTopicRepository.findPopularTopics();
            } else if ("pinned".equals(filter)) {
                topics = forumTopicRepository.findPinnedTopics();
            } else {
                topics = forumTopicRepository.findByIsActiveTrueOrderByLastActivityAtDesc();
            }

            System.out.println("📊 Found " + topics.size() + " topics");
            return ResponseEntity.ok(Map.of(
                "success", true,
                "topics", topics
            ));

        } catch (Exception e) {
            System.out.println("❌ Error in getForumTopics: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load forum topics: " + e.getMessage()
            ));
        }
    }
    
    // ============ COMMUNITY EVENTS ============
    
    @GetMapping("/events")
    public ResponseEntity<Map<String, Object>> getCommunityEvents(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String filter,
            @RequestParam(required = false) String search) {
        try {
            System.out.println("🔍 Getting community events from database...");

            List<CommunityEvent> events;

            if (search != null && !search.isEmpty()) {
                events = communityEventRepository.searchEvents(search);
            } else if (type != null && !type.equals("all")) {
                events = communityEventRepository.findByTypeAndActive(type);
            } else if ("upcoming".equals(filter)) {
                events = communityEventRepository.findUpcomingEvents(LocalDateTime.now());
            } else if ("past".equals(filter)) {
                events = communityEventRepository.findPastEvents(LocalDateTime.now());
            } else if ("registration".equals(filter)) {
                events = communityEventRepository.findEventsOpenForRegistration(LocalDateTime.now());
            } else {
                events = communityEventRepository.findByIsActiveTrueAndIsCancelledFalseOrderByEventDateAsc();
            }

            System.out.println("📊 Found " + events.size() + " events");
            return ResponseEntity.ok(Map.of(
                "success", true,
                "events", events
            ));

        } catch (Exception e) {
            System.out.println("❌ Error in getCommunityEvents: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to load community events: " + e.getMessage()
            ));
        }
    }
    
    // ============ MOCK DATA METHODS ============
    
    private List<Map<String, Object>> getMockSupportGroups() {
        List<Map<String, Object>> groups = new ArrayList<>();
        
        Map<String, Object> group1 = new HashMap<>();
        group1.put("id", "1");
        group1.put("name", "Itsinda ry'abagore biga kubana n'ubwiyunge");
        group1.put("description", "Support Group for Women Family Planning");
        group1.put("category", "Family Planning");
        group1.put("creatorName", "Dr. Marie Uwimana");
        group1.put("memberCount", 156);
        group1.put("maxMembers", 200);
        group1.put("isPrivate", false);
        group1.put("isActive", true);
        group1.put("meetingSchedule", "Ku wa gatatu buri cyumweru saa kumi n'ebyiri");
        group1.put("meetingLocation", "Kimisagara Health Center");
        group1.put("contactInfo", "+250788111222");
        group1.put("tags", new ArrayList<>());
        group1.put("createdAt", LocalDateTime.now().minusDays(30));
        group1.put("updatedAt", LocalDateTime.now().minusDays(1));
        groups.add(group1);
        
        Map<String, Object> group2 = new HashMap<>();
        group2.put("id", "2");
        group2.put("name", "Ikiganiro cy'ubuzima");
        group2.put("description", "Health Discussion Forum");
        group2.put("category", "Health");
        group2.put("creatorName", "System Administrator");
        group2.put("memberCount", 89);
        group2.put("maxMembers", 150);
        group2.put("isPrivate", false);
        group2.put("isActive", true);
        group2.put("meetingSchedule", "Ku wa kane buri cyumweru saa kumi");
        group2.put("meetingLocation", "Online Platform");
        group2.put("contactInfo", "<EMAIL>");
        group2.put("tags", new ArrayList<>());
        group2.put("createdAt", LocalDateTime.now().minusDays(35));
        group2.put("updatedAt", LocalDateTime.now().minusDays(2));
        groups.add(group2);
        
        Map<String, Object> group3 = new HashMap<>();
        group3.put("id", "3");
        group3.put("name", "Abana b'urubyiruko");
        group3.put("description", "Youth Health Group");
        group3.put("category", "Youth Health");
        group3.put("creatorName", "Grace Mukamana");
        group3.put("memberCount", 67);
        group3.put("maxMembers", 100);
        group3.put("isPrivate", false);
        group3.put("isActive", true);
        group3.put("meetingSchedule", "Ku wa mbere buri cyumweru saa kumi n'ebyiri");
        group3.put("meetingLocation", "Nyarugenge Health Center");
        group3.put("contactInfo", "+250788555666");
        group3.put("tags", new ArrayList<>());
        group3.put("createdAt", LocalDateTime.now().minusDays(40));
        group3.put("updatedAt", LocalDateTime.now().minusDays(3));
        groups.add(group3);
        
        return groups;
    }

    private List<Map<String, Object>> getMockForumTopics() {
        List<Map<String, Object>> topics = new ArrayList<>();

        Map<String, Object> topic1 = new HashMap<>();
        topic1.put("id", "1");
        topic1.put("title", "Itsinda ry'abagore biga kubana n'ubwiyunge");
        topic1.put("content", "Itsinda ry'abagore biga kubana n'ubwiyunge");
        topic1.put("category", "Family Planning");
        topic1.put("authorName", "Dr. Marie Uwimana");
        topic1.put("viewCount", 245);
        topic1.put("replyCount", 23);
        topic1.put("isPinned", true);
        topic1.put("isLocked", false);
        topic1.put("isActive", true);
        topic1.put("tags", new ArrayList<>());
        topic1.put("lastActivityAt", LocalDateTime.now().minusHours(2));
        topic1.put("lastReplyByName", "Grace Mukamana");
        topic1.put("createdAt", LocalDateTime.now().minusDays(10));
        topic1.put("updatedAt", LocalDateTime.now().minusHours(2));
        topics.add(topic1);

        Map<String, Object> topic2 = new HashMap<>();
        topic2.put("id", "2");
        topic2.put("title", "Ikiganiro cy'ubuzima bw'imyororokere");
        topic2.put("content", "Ikiganiro gishya kuri ubuzima bw'imyororokere");
        topic2.put("category", "Health");
        topic2.put("authorName", "System Administrator");
        topic2.put("viewCount", 189);
        topic2.put("replyCount", 15);
        topic2.put("isPinned", false);
        topic2.put("isLocked", false);
        topic2.put("isActive", true);
        topic2.put("tags", new ArrayList<>());
        topic2.put("lastActivityAt", LocalDateTime.now().minusHours(4));
        topic2.put("lastReplyByName", "Dr. Marie Uwimana");
        topic2.put("createdAt", LocalDateTime.now().minusDays(8));
        topic2.put("updatedAt", LocalDateTime.now().minusHours(4));
        topics.add(topic2);

        return topics;
    }

    private List<Map<String, Object>> getMockCommunityEvents() {
        List<Map<String, Object>> events = new ArrayList<>();

        Map<String, Object> event1 = new HashMap<>();
        event1.put("id", "1");
        event1.put("title", "Ubwiyunge bw'urubyiruko");
        event1.put("description", "Youth Family Planning Workshop");
        event1.put("type", "WORKSHOP");
        event1.put("organizerName", "Dr. Marie Uwimana");
        event1.put("eventDate", LocalDateTime.now().plusDays(7));
        event1.put("endDate", LocalDateTime.now().plusDays(7).plusHours(3));
        event1.put("location", "Kimisagara Health Center");
        event1.put("maxParticipants", 50);
        event1.put("currentParticipants", 8);
        event1.put("registrationRequired", true);
        event1.put("registrationDeadline", LocalDateTime.now().plusDays(5));
        event1.put("isVirtual", false);
        event1.put("virtualLink", null);
        event1.put("isActive", true);
        event1.put("isCancelled", false);
        event1.put("contactInfo", "+250788111222");
        event1.put("createdAt", LocalDateTime.now().minusDays(7));
        event1.put("updatedAt", LocalDateTime.now().minusDays(1));
        events.add(event1);

        Map<String, Object> event2 = new HashMap<>();
        event2.put("id", "2");
        event2.put("title", "Inama y'ubuzima");
        event2.put("description", "Health Education Workshop");
        event2.put("type", "SEMINAR");
        event2.put("organizerName", "System Administrator");
        event2.put("eventDate", LocalDateTime.now().plusDays(14));
        event2.put("endDate", LocalDateTime.now().plusDays(14).plusHours(4));
        event2.put("location", "Nyarugenge Health Center");
        event2.put("maxParticipants", 100);
        event2.put("currentParticipants", 23);
        event2.put("registrationRequired", true);
        event2.put("registrationDeadline", LocalDateTime.now().plusDays(10));
        event2.put("isVirtual", false);
        event2.put("virtualLink", null);
        event2.put("isActive", true);
        event2.put("isCancelled", false);
        event2.put("contactInfo", "+250788555666");
        event2.put("createdAt", LocalDateTime.now().minusDays(14));
        event2.put("updatedAt", LocalDateTime.now().minusDays(2));
        events.add(event2);

        return events;
    }
}
