import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import 'auth_service.dart';

enum NotificationSeverity { low, medium, high, critical }

enum NotificationType {
  healthDataRecorded,
  criticalReading,
  missedMedication,
  appointmentReminder
}

class HealthWorkerNotification {
  final String id;
  final String healthWorkerId;
  final String clientId;
  final String? healthRecordId;
  final NotificationType type;
  final String title;
  final String message;
  final NotificationSeverity severity;
  final bool isRead;
  final bool isAcknowledged;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime? readAt;
  final DateTime? acknowledgedAt;
  final String? clientName;
  final String? clientPhone;

  HealthWorkerNotification({
    required this.id,
    required this.healthWorkerId,
    required this.clientId,
    this.healthRecordId,
    required this.type,
    required this.title,
    required this.message,
    required this.severity,
    required this.isRead,
    required this.isAcknowledged,
    this.metadata,
    required this.createdAt,
    this.readAt,
    this.acknowledgedAt,
    this.clientName,
    this.clientPhone,
  });

  factory HealthWorkerNotification.fromJson(Map<String, dynamic> json) {
    return HealthWorkerNotification(
      id: json['id'].toString(),
      healthWorkerId: json['healthWorkerId'].toString(),
      clientId: json['clientId'].toString(),
      healthRecordId: json['healthRecordId']?.toString(),
      type: _parseNotificationType(json['notificationType']),
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      severity: _parseSeverity(json['severity']),
      isRead: json['isRead'] ?? false,
      isAcknowledged: json['isAcknowledged'] ?? false,
      metadata: json['metadata'],
      createdAt: DateTime.parse(json['createdAt']),
      readAt: json['readAt'] != null ? DateTime.parse(json['readAt']) : null,
      acknowledgedAt: json['acknowledgedAt'] != null ? DateTime.parse(json['acknowledgedAt']) : null,
      clientName: json['clientName'],
      clientPhone: json['clientPhone'],
    );
  }

  static NotificationType _parseNotificationType(String? type) {
    switch (type?.toUpperCase()) {
      case 'HEALTH_DATA_RECORDED':
        return NotificationType.healthDataRecorded;
      case 'CRITICAL_READING':
        return NotificationType.criticalReading;
      case 'MISSED_MEDICATION':
        return NotificationType.missedMedication;
      case 'APPOINTMENT_REMINDER':
        return NotificationType.appointmentReminder;
      default:
        return NotificationType.healthDataRecorded;
    }
  }

  static NotificationSeverity _parseSeverity(String? severity) {
    switch (severity?.toUpperCase()) {
      case 'LOW':
        return NotificationSeverity.low;
      case 'MEDIUM':
        return NotificationSeverity.medium;
      case 'HIGH':
        return NotificationSeverity.high;
      case 'CRITICAL':
        return NotificationSeverity.critical;
      default:
        return NotificationSeverity.medium;
    }
  }
}

class ClientSummary {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final DateTime assignedAt;
  final int unreadNotifications;
  final DateTime? lastHealthRecord;
  final String? lastHealthMetric;
  final String? riskLevel;

  ClientSummary({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    required this.assignedAt,
    required this.unreadNotifications,
    this.lastHealthRecord,
    this.lastHealthMetric,
    this.riskLevel,
  });

  factory ClientSummary.fromJson(Map<String, dynamic> json) {
    return ClientSummary(
      id: json['id'].toString(),
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      assignedAt: DateTime.parse(json['assignedAt']),
      unreadNotifications: json['unreadNotifications'] ?? 0,
      lastHealthRecord: json['lastHealthRecord'] != null 
          ? DateTime.parse(json['lastHealthRecord']) 
          : null,
      lastHealthMetric: json['lastHealthMetric'],
      riskLevel: json['riskLevel'],
    );
  }
}

class HealthWorkerNotificationService {
  final String baseUrl = AppConstants.baseUrl;
  final AuthService _authService = AuthService();

  Future<String?> _getAuthToken() async {
    return _authService.currentToken;
  }

  Map<String, String> _getHeaders(String? token) {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  Future<List<HealthWorkerNotification>> getNotifications({
    bool unreadOnly = false,
    int page = 0,
    int size = 20,
  }) async {
    try {
      final token = await _getAuthToken();
      final queryParams = {
        'unreadOnly': unreadOnly.toString(),
        'page': page.toString(),
        'size': size.toString(),
      };

      final uri = Uri.parse('$baseUrl/health-worker-notifications')
          .replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> notifications = data['notifications'] ?? [];
          return notifications.map((json) => HealthWorkerNotification.fromJson(json)).toList();
        }
      }

      return _getSampleNotifications();
    } catch (e) {
      print('Error loading health worker notifications: $e');
      return _getSampleNotifications();
    }
  }

  Future<int> getUnreadCount() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/health-worker-notifications')
          .replace(queryParameters: {'unreadOnly': 'true', 'size': '1'});
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['unreadCount'] ?? 0;
        }
      }

      return 3; // Sample unread count
    } catch (e) {
      print('Error loading unread count: $e');
      return 3;
    }
  }

  Future<void> markAsRead(String notificationId) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/health-worker-notifications/$notificationId/mark-read');
      
      final response = await http.post(
        uri,
        headers: _getHeaders(token),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to mark notification as read');
      }
    } catch (e) {
      print('Error marking notification as read: $e');
      rethrow;
    }
  }

  Future<void> acknowledgeNotification(String notificationId, {String? responseNote}) async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/health-worker-notifications/$notificationId/acknowledge');
      
      final body = responseNote != null 
          ? json.encode({'note': responseNote})
          : null;

      final response = await http.post(
        uri,
        headers: _getHeaders(token),
        body: body,
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to acknowledge notification');
      }
    } catch (e) {
      print('Error acknowledging notification: $e');
      rethrow;
    }
  }

  Future<List<ClientSummary>> getAssignedClients() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/health-worker-notifications/clients');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> clients = data['clients'] ?? [];
          return clients.map((json) => ClientSummary.fromJson(json)).toList();
        }
      }

      return _getSampleClients();
    } catch (e) {
      print('Error loading assigned clients: $e');
      return _getSampleClients();
    }
  }

  Future<Map<String, dynamic>> getDashboardSummary() async {
    try {
      final token = await _getAuthToken();
      final uri = Uri.parse('$baseUrl/health-worker-notifications/dashboard-summary');
      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['summary'] ?? {};
        }
      }

      return _getSampleDashboardSummary();
    } catch (e) {
      print('Error loading dashboard summary: $e');
      return _getSampleDashboardSummary();
    }
  }

  // Sample data removed - only use real API data
  List<HealthWorkerNotification> _getSampleNotifications() {
    debugPrint('⚠️ No sample notifications - using empty list');
    return [];
  }
  }

  List<ClientSummary> _getSampleClients() {
    debugPrint('⚠️ No sample clients - using empty list');
    return [];
  }

  Map<String, dynamic> _getSampleDashboardSummary() {
    debugPrint('⚠️ No sample dashboard summary - using empty data');
    return {
      'totalClients': 0,
      'unreadNotifications': 0,
      'criticalAlerts': 0,
      'todayRecords': 0,
      'weeklyRecords': 0,
      'clientsWithRecentData': 0,
      'averageResponseTime': '0 minutes',
    };
  }
}
