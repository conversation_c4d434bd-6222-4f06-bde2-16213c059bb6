import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/health_tracking_service.dart';
import '../../core/models/health_record_model.dart';

class CompleteHealthRecordForm extends StatefulWidget {
  final HealthRecord? existingRecord;
  final String? userId;

  const CompleteHealthRecordForm({Key? key, this.existingRecord, this.userId})
    : super(key: key);

  @override
  State<CompleteHealthRecordForm> createState() =>
      _CompleteHealthRecordFormState();
}

class _CompleteHealthRecordFormState extends State<CompleteHealthRecordForm> {
  final _formKey = GlobalKey<FormState>();
  final _healthService = HealthTrackingService();

  // Controllers for all health record fields
  final _heartRateController = TextEditingController();
  final _systolicController = TextEditingController();
  final _diastolicController = TextEditingController();
  final _weightController = TextEditingController();
  final _temperatureController = TextEditingController();
  final _heightController = TextEditingController();
  final _notesController = TextEditingController();

  // Units
  String _heartRateUnit = 'bpm';
  String _bpUnit = 'mmHg';
  String _weightUnit = 'kg';
  String _temperatureUnit = '°C';
  String _heightUnit = 'cm';

  bool _isLoading = false;
  bool _isVerified = false;
  String _recordedBy = 'Self-reported';

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.existingRecord != null) {
      final record = widget.existingRecord!;

      // Initialize with existing values (only if > 0)
      if (record.heartRateValue > 0) {
        _heartRateController.text = record.heartRateValue.toString();
      }
      _heartRateUnit = record.heartRateUnit;

      // Blood pressure
      if (record.bpValue.isNotEmpty && record.bpValue != '0/0') {
        final systolic = record.systolic;
        final diastolic = record.diastolic;
        if (systolic != null) _systolicController.text = systolic.toString();
        if (diastolic != null) _diastolicController.text = diastolic.toString();
      }
      _bpUnit = record.bpUnit;

      // Weight
      if (record.kgValue > 0) {
        _weightController.text = record.kgValue.toString();
      }
      _weightUnit = record.kgUnit;

      // Temperature
      if (record.tempValue > 0) {
        _temperatureController.text = record.tempValue.toString();
      }
      _temperatureUnit = record.tempUnit;

      // Height
      if (record.heightValue > 0) {
        _heightController.text = record.heightValue.toString();
      }
      _heightUnit = record.heightUnit;

      // Notes
      if (record.notes.isNotEmpty) {
        _notesController.text = record.notes;
      }

      _isVerified = record.isVerified;
      _recordedBy =
          record.recordedBy.isNotEmpty ? record.recordedBy : 'Self-reported';
    }
  }

  @override
  void dispose() {
    _heartRateController.dispose();
    _systolicController.dispose();
    _diastolicController.dispose();
    _weightController.dispose();
    _temperatureController.dispose();
    _heightController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _saveHealthRecord() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Parse values with defaults
      final heartRate = int.tryParse(_heartRateController.text) ?? 0;
      final systolic = int.tryParse(_systolicController.text) ?? 0;
      final diastolic = int.tryParse(_diastolicController.text) ?? 0;
      final weight = double.tryParse(_weightController.text) ?? 0.0;
      final temperature = double.tryParse(_temperatureController.text) ?? 0.0;
      final height = double.tryParse(_heightController.text) ?? 0.0;

      // Create blood pressure string
      final bpValue =
          (systolic > 0 && diastolic > 0) ? '$systolic/$diastolic' : '0/0';

      final result = await _healthService.createCompleteHealthRecord(
        userId: widget.userId,
        heartRateValue: heartRate,
        heartRateUnit: _heartRateUnit,
        bpValue: bpValue,
        bpUnit: _bpUnit,
        kgValue: weight,
        kgUnit: _weightUnit,
        tempValue: temperature,
        tempUnit: _temperatureUnit,
        heightValue: height,
        heightUnit: _heightUnit,
        notes: _notesController.text.trim(),
        recordedBy: _recordedBy,
      );

      if (result != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Amakuru y\'ubuzima yashyizweho neza'),
              backgroundColor: AppTheme.successColor,
            ),
          );
          Navigator.of(context).pop(result);
        }
      } else {
        throw Exception('Failed to save health record');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Habaye ikosa: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.existingRecord != null
              ? 'Guhindura amakuru y\'ubuzima'
              : 'Kwandika amakuru y\'ubuzima',
          style: AppTheme.headlineSmall.copyWith(color: Colors.white),
        ),
        backgroundColor: AppTheme.primaryColor,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveHealthRecord,
              child: Text(
                'Bika',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildSectionHeader('Amakuru y\'umutima'),
            _buildHeartRateSection(),

            const SizedBox(height: 24),
            _buildSectionHeader('Umuvuduko w\'amaraso'),
            _buildBloodPressureSection(),

            const SizedBox(height: 24),
            _buildSectionHeader('Ibiro n\'uburebure'),
            _buildWeightHeightSection(),

            const SizedBox(height: 24),
            _buildSectionHeader('Ubushyuhe bw\'umubiri'),
            _buildTemperatureSection(),

            const SizedBox(height: 24),
            _buildSectionHeader('Inyandiko'),
            _buildNotesSection(),

            const SizedBox(height: 24),
            _buildSectionHeader('Amakuru y\'inyongera'),
            _buildMetadataSection(),

            const SizedBox(height: 32),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: AppTheme.headlineSmall.copyWith(
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildHeartRateSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  flex: 3,
                  child: TextFormField(
                    controller: _heartRateController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    decoration: InputDecoration(
                      labelText: 'Umuvuduko w\'umutima',
                      hintText: 'Urugero: 72',
                      prefixIcon: Icon(
                        Icons.favorite,
                        color: AppTheme.errorColor,
                      ),
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value?.isNotEmpty == true) {
                        final rate = int.tryParse(value!);
                        if (rate == null || rate < 30 || rate > 200) {
                          return 'Shyiramo umubare uri hagati ya 30 na 200';
                        }
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 1,
                  child: DropdownButtonFormField<String>(
                    value: _heartRateUnit,
                    decoration: InputDecoration(
                      labelText: 'Igipimo',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        ['bpm']
                            .map(
                              (unit) => DropdownMenuItem(
                                value: unit,
                                child: Text(unit),
                              ),
                            )
                            .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _heartRateUnit = value);
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBloodPressureSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _systolicController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    decoration: InputDecoration(
                      labelText: 'Systolic (Hejuru)',
                      hintText: '120',
                      prefixIcon: Icon(
                        Icons.water_drop,
                        color: AppTheme.primaryColor,
                      ),
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value?.isNotEmpty == true) {
                        final systolic = int.tryParse(value!);
                        if (systolic == null ||
                            systolic < 70 ||
                            systolic > 250) {
                          return 'Hagati ya 70-250';
                        }
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _diastolicController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    decoration: InputDecoration(
                      labelText: 'Diastolic (Hasi)',
                      hintText: '80',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value?.isNotEmpty == true) {
                        final diastolic = int.tryParse(value!);
                        if (diastolic == null ||
                            diastolic < 40 ||
                            diastolic > 150) {
                          return 'Hagati ya 40-150';
                        }
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 1,
                  child: DropdownButtonFormField<String>(
                    value: _bpUnit,
                    decoration: InputDecoration(
                      labelText: 'Igipimo',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        ['mmHg']
                            .map(
                              (unit) => DropdownMenuItem(
                                value: unit,
                                child: Text(unit),
                              ),
                            )
                            .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _bpUnit = value);
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeightHeightSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _weightController,
                    keyboardType: TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    decoration: InputDecoration(
                      labelText: 'Ibiro',
                      hintText: '65.5',
                      prefixIcon: Icon(
                        Icons.monitor_weight,
                        color: AppTheme.warningColor,
                      ),
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value?.isNotEmpty == true) {
                        final weight = double.tryParse(value!);
                        if (weight == null || weight < 20 || weight > 300) {
                          return 'Hagati ya 20-300 kg';
                        }
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 1,
                  child: DropdownButtonFormField<String>(
                    value: _weightUnit,
                    decoration: InputDecoration(
                      labelText: 'Igipimo',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        ['kg', 'lbs']
                            .map(
                              (unit) => DropdownMenuItem(
                                value: unit,
                                child: Text(unit),
                              ),
                            )
                            .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _weightUnit = value);
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _heightController,
                    keyboardType: TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    decoration: InputDecoration(
                      labelText: 'Uburebure',
                      hintText: '170.5',
                      prefixIcon: Icon(Icons.height, color: AppTheme.infoColor),
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value?.isNotEmpty == true) {
                        final height = double.tryParse(value!);
                        if (height == null || height < 50 || height > 250) {
                          return 'Hagati ya 50-250 cm';
                        }
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 1,
                  child: DropdownButtonFormField<String>(
                    value: _heightUnit,
                    decoration: InputDecoration(
                      labelText: 'Igipimo',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        ['cm', 'm', 'ft']
                            .map(
                              (unit) => DropdownMenuItem(
                                value: unit,
                                child: Text(unit),
                              ),
                            )
                            .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _heightUnit = value);
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemperatureSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: TextFormField(
                controller: _temperatureController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  labelText: 'Ubushyuhe bw\'umubiri',
                  hintText: '36.5',
                  prefixIcon: Icon(
                    Icons.thermostat,
                    color: AppTheme.warningColor,
                  ),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value?.isNotEmpty == true) {
                    final temp = double.tryParse(value!);
                    if (temp == null || temp < 30 || temp > 45) {
                      return 'Hagati ya 30-45°C';
                    }
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              flex: 1,
              child: DropdownButtonFormField<String>(
                value: _temperatureUnit,
                decoration: InputDecoration(
                  labelText: 'Igipimo',
                  border: OutlineInputBorder(),
                ),
                items:
                    ['°C', '°F']
                        .map(
                          (unit) =>
                              DropdownMenuItem(value: unit, child: Text(unit)),
                        )
                        .toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _temperatureUnit = value);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: TextFormField(
          controller: _notesController,
          maxLines: 4,
          decoration: InputDecoration(
            labelText: 'Inyandiko z\'inyongera',
            hintText: 'Andika inyandiko zose z\'inyongera...',
            prefixIcon: Icon(Icons.note_add, color: AppTheme.primaryColor),
            border: OutlineInputBorder(),
          ),
        ),
      ),
    );
  }

  Widget _buildMetadataSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextFormField(
              initialValue: _recordedBy,
              decoration: InputDecoration(
                labelText: 'Byanditswe na',
                prefixIcon: Icon(Icons.person, color: AppTheme.primaryColor),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                _recordedBy = value;
              },
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              title: Text('Amakuru yemejwe'),
              subtitle: Text('Emeza ko amakuru yose ari ukuri'),
              value: _isVerified,
              onChanged: (value) {
                setState(() => _isVerified = value ?? false);
              },
              activeColor: AppTheme.successColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveHealthRecord,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child:
            _isLoading
                ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text('Birashyirwa...'),
                  ],
                )
                : Text(
                  widget.existingRecord != null
                      ? 'Guhindura amakuru'
                      : 'Bika amakuru',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
      ),
    );
  }
}
