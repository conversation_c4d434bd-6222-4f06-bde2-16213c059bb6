-- Test data for Ubuzima appointment booking system

-- Insert Health Facilities
INSERT INTO health_facilities (name, facility_type, address, phone_number, email, latitude, longitude, operating_hours, services_offered, is_active, created_at, updated_at) VALUES
('Kimisagara Health Center', 'HEALTH_CENTER', 'Kimisagara, Nyarugenge, Kigali', '+250788111222', '<EMAIL>', -1.9441, 30.0619, '08:00-17:00 (Mon-Fri), 08:00-12:00 (Sat)', 'Family Planning, Maternal Health, Child Health, General Medicine', true, NOW(), NOW()),
('Kigali University Teaching Hospital', 'HOSPITAL', 'KG 7 Ave, Nyarugenge, Kigali', '+250788333444', '<EMAIL>', -1.9536, 30.0606, '24/7', 'Emergency, Surgery, Maternity, Pediatrics, Family Planning', true, NOW(), NOW()),
('Nyarugenge Health Center', 'HEALTH_CENTER', '<PERSON>yarugenge, Kigali', '+250788555666', '<EMAIL>', -1.9506, 30.0588, '08:00-17:00 (Mon-Fri), 08:00-12:00 (Sat)', 'Family Planning, Maternal Health, General Medicine', true, NOW(), NOW()),
('Kacyiru Hospital', 'HOSPITAL', 'Kacyiru, Gasabo, Kigali', '+250788777888', '<EMAIL>', -1.9355, 30.0928, '24/7', 'Emergency, Surgery, Maternity, Pediatrics, Family Planning', true, NOW(), NOW());

-- Insert Health Workers
INSERT INTO users (name, email, phone, password, role, facility_id, is_active, created_at, updated_at) VALUES
('Dr. Uwimana Jean', '<EMAIL>', '+250788234567', '$2a$10$example_hash', 'HEALTH_WORKER', 1, true, NOW(), NOW()),
('Nurse Mukamana Marie', '<EMAIL>', '+250788345678', '$2a$10$example_hash', 'HEALTH_WORKER', 1, true, NOW(), NOW()),
('Dr. Nkurunziza Paul', '<EMAIL>', '+250788456789', '$2a$10$example_hash', 'HEALTH_WORKER', 2, true, NOW(), NOW()),
('Nurse Uwimana Grace', '<EMAIL>', '+250788567890', '$2a$10$example_hash', 'HEALTH_WORKER', 2, true, NOW(), NOW()),
('Dr. Mutesi Alice', '<EMAIL>', '+250788678901', '$2a$10$example_hash', 'HEALTH_WORKER', 3, true, NOW(), NOW()),
('Nurse Habimana John', '<EMAIL>', '+250788789012', '$2a$10$example_hash', 'HEALTH_WORKER', 3, true, NOW(), NOW()),
('Dr. Kagame Eric', '<EMAIL>', '+250788890123', '$2a$10$example_hash', 'HEALTH_WORKER', 4, true, NOW(), NOW()),
('Nurse Nyirahabimana Rose', '<EMAIL>', '+250788901234', '$2a$10$example_hash', 'HEALTH_WORKER', 4, true, NOW(), NOW());

-- Insert some test clients
INSERT INTO users (name, email, phone, password, role, is_active, created_at, updated_at) VALUES
('Uwimana Claudine', '<EMAIL>', '+250788123456', '$2a$10$example_hash', 'CLIENT', true, NOW(), NOW()),
('Mukamana Jeanne', '<EMAIL>', '+250788234567', '$2a$10$example_hash', 'CLIENT', true, NOW(), NOW()),
('Niyonsenga Patrick', '<EMAIL>', '+250788345678', '$2a$10$example_hash', 'CLIENT', true, NOW(), NOW());

-- Insert some time slots for testing
INSERT INTO time_slots (facility_id, health_worker_id, start_time, end_time, is_available, created_at, updated_at) VALUES
-- Kimisagara Health Center slots
(1, 1, '2024-01-15 09:00:00', '2024-01-15 09:30:00', true, NOW(), NOW()),
(1, 1, '2024-01-15 09:30:00', '2024-01-15 10:00:00', true, NOW(), NOW()),
(1, 1, '2024-01-15 10:00:00', '2024-01-15 10:30:00', true, NOW(), NOW()),
(1, 1, '2024-01-15 10:30:00', '2024-01-15 11:00:00', true, NOW(), NOW()),
(1, 2, '2024-01-15 14:00:00', '2024-01-15 14:30:00', true, NOW(), NOW()),
(1, 2, '2024-01-15 14:30:00', '2024-01-15 15:00:00', true, NOW(), NOW()),
(1, 2, '2024-01-15 15:00:00', '2024-01-15 15:30:00', true, NOW(), NOW()),

-- KUTH slots
(2, 3, '2024-01-15 08:00:00', '2024-01-15 08:30:00', true, NOW(), NOW()),
(2, 3, '2024-01-15 08:30:00', '2024-01-15 09:00:00', true, NOW(), NOW()),
(2, 3, '2024-01-15 09:00:00', '2024-01-15 09:30:00', true, NOW(), NOW()),
(2, 4, '2024-01-15 13:00:00', '2024-01-15 13:30:00', true, NOW(), NOW()),
(2, 4, '2024-01-15 13:30:00', '2024-01-15 14:00:00', true, NOW(), NOW()),
(2, 4, '2024-01-15 14:00:00', '2024-01-15 14:30:00', true, NOW(), NOW());
