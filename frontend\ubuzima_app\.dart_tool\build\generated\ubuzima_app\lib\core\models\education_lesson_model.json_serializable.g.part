// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EducationLesson _$EducationLessonFromJson(Map<String, dynamic> json) =>
    EducationLesson(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      content: json['content'] as String,
      category: json['category'] as String,
      level: json['level'] as String,
      orderIndex: (json['orderIndex'] as num).toInt(),
      isPublished: json['isPublished'] as bool,
      imageUrl: json['imageUrl'] as String?,
      videoUrl: json['videoUrl'] as String?,
      estimatedDuration: (json['estimatedDuration'] as num?)?.toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt:
          json['updatedAt'] == null
              ? null
              : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$EducationLessonToJson(EducationLesson instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'content': instance.content,
      'category': instance.category,
      'level': instance.level,
      'orderIndex': instance.orderIndex,
      'isPublished': instance.isPublished,
      'imageUrl': instance.imageUrl,
      'videoUrl': instance.videoUrl,
      'estimatedDuration': instance.estimatedDuration,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
