import 'package:json_annotation/json_annotation.dart';
import 'contraception_enums.dart';

part 'contraception_reminder_model.g.dart';

@JsonSerializable()
class ContraceptionReminder {
  final String id;
  final String userId;
  final String title;
  final String? description;
  final ReminderType type;
  final DateTime reminderTime;
  final bool isActive;
  final bool isRepeating;
  final String? repeatInterval;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ContraceptionReminder({
    required this.id,
    required this.userId,
    required this.title,
    this.description,
    required this.type,
    required this.reminderTime,
    required this.isActive,
    required this.isRepeating,
    this.repeatInterval,
    required this.createdAt,
    this.updatedAt,
  });

  factory ContraceptionReminder.fromJson(Map<String, dynamic> json) =>
      _$ContraceptionReminderFromJson(json);

  Map<String, dynamic> toJson() => _$ContraceptionReminderToJson(this);
}
